'use client';

import type {
  GetDataParams,
  MessageData,
  MessageHandler,
  NativeMessage,
  NativeResponse,
} from '@/types/native-bridge';
import { nlog } from './logger';

const messageHandlers = new Map<string, MessageHandler>();
const callbackHandlers = new Map<string, MessageHandler>();

export const registerMessageHandler = (type: string, handler: MessageHandler) => {
  messageHandlers.set(type, handler);
};

export const removeMessageHandler = (type: string) => {
  messageHandlers.delete(type);
};

export const registerCallbackHandler = (type: string, handler: MessageHandler) => {
  callbackHandlers.set(type, handler);
};

export const removeCallbackHandler = (type: string) => {
  callbackHandlers.delete(type);
};

export const initNativeBridge = () => {
  registerHandlerFromNative();

  window.receiveMessageFromNative = (jsonMessage: string) => {
    try {
      const message = JSON.parse(jsonMessage);
      const handler = messageHandlers.get(message.type);
      nlog(`receiveMessageFromNative1 ${JSON.stringify(handler)}`);
      if (handler) {
        nlog(`receiveMessageFromNative handler ${JSON.stringify(message.data)}`);
        handler(message.data);
      } else {
        console.warn(
          `(receiveMessageFromNative)No handler registered for message type: ${message.type}`,
        );
      }
    } catch (error) {
      console.log('(receiveMessageFromNative)Error parsing message:', error);
    }
  };

  window.receiveCallbackFromNative = (jsonMessage: string) => {
    try {
      const message = JSON.parse(jsonMessage) as NativeMessage;
      const handler = callbackHandlers.get(message.type);
      if (handler) {
        handler(message.data);
      } else {
        console.warn(
          `(receiveCallbackFromNative)No handler registered for message type: ${message.type}`,
        );
      }
    } catch (error) {
      console.log('(receiveCallbackFromNative)Error parsing message:', error);
    }
  };
};

export const sendMessageToNative = (message: NativeMessage) => {
  if (message.callback) {
    registerCallbackHandler(message.type, message.callback);
  }
  const wrappedMessage = {
    type: message.type,
    data: message.data,
    callback: 'receiveCallbackFromNative',
  };
  if (window.webkit?.messageHandlers?.nativeHandler) {
    window.webkit.messageHandlers.nativeHandler.postMessage(wrappedMessage); // iOS
  } else {
    window.AndroidInterface?.postMessage(JSON.stringify(wrappedMessage)); // Android
  }
};

//native send msg to webview
export const registerHandlerFromNative = () => {
  //open-callback-url
  registerMessageHandler('open-callback-url', (data) => {
    nlog('opencallbackurl');
    window.location.href = data?.url;
  });
};

// ネイティブからデータを取得する
export const getMessageToNative = (type: string, callback: (data: any) => void) => {
  // 登録コールバック処理
  registerCallbackHandler(type, callback);
  const message = {
    type: type,
    data: null,
    callback: 'receiveCallbackFromNative',
  };
  if (window.webkit?.messageHandlers?.nativeHandler) {
    window.webkit.messageHandlers.nativeHandler.postMessage(message); // iOS
  } else {
    window.AndroidInterface?.postMessage(JSON.stringify(message)); // Android
  }
};
