// example: 3月19日(水)
// example: 2025年03月19日(水)
// example: 2025-03-19
// example: 20250319
// example: 2025年3月19日(水)
type DateFormat =
  | 'MM月DD日(d)'
  | 'mm月dd日(d)'
  | 'yyyy年MM月dd日(d)'
  | 'yyyy-MM-dd'
  | 'yyyyMMdd'
  | 'yyyy年M月d日(d)'
  | 'yyyy年M月d日'
  | 'yyyy-MM-dd HH:mm:ss'
  | 'yyyy-MM-ddTHH:mm:ss.SSS'
  | 'yyyy/M/d/(d)'
  | 'yyyy年M月d日 HH:mm'
  | 'yyyy/M/d';
export enum DateFormatEnum {
  DATE_MDD_JP = 'MM月DD日(d)',
  DATE_mdd_JP = 'mm月dd日(d)',
  DATE_YYMMDD_JP = 'yyyy年MM月dd日(d)',
  DATE_Y_M_D_EN = 'yyyy-MM-dd',
  DATE_YMD_EN = 'yyyyMMdd',
  DATE_YMDD_JP = 'yyyy年M月d日(d)',
  DATE_YMD_JP = 'yyyy年M月d日',
  DATE_YMDHMS_EN = 'yyyy-MM-dd HH:mm:ss',
  DATE_YMDHMS_ISO_EN = 'yyyy-MM-ddTHH:mm:ss.SSS',
  DATE_YMDD_NORMAL = 'yyyy/M/d/(d)',
  DATE_YMDHM = 'yyyy年M月d日 HH:mm',
  DATE_YMD_NORMAL = 'yyyy/M/d',
}

// date maybe 20250810
export function formatDate(date: Date | string | null, format: DateFormat) {
  let dateObj: Date | null = null;
  if (!date) {
    return '';
  }
  if (typeof date === 'string') {
    if (date.length === 8) {
      const dateStr = date.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
      dateObj = new Date(dateStr);
    } else {
      dateObj = new Date(date);
    }
  } else {
    dateObj = date;
  }
  const year = dateObj.getFullYear();
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();
  const hour = dateObj.getHours();
  const minute = dateObj.getMinutes();
  const second = dateObj.getSeconds();
  switch (format) {
    case DateFormatEnum.DATE_MDD_JP:
      return `${pad(month)}月${pad(day)}日(${formatWeekday(dateObj)})`;
    case DateFormatEnum.DATE_mdd_JP:
      return `${month}月${day}日(${formatWeekday(dateObj)})`;
    case DateFormatEnum.DATE_Y_M_D_EN:
      return `${year}-${pad(month)}-${pad(day)}`;
    case DateFormatEnum.DATE_YMD_EN:
      return `${year}${pad(month)}${pad(day)}`;
    case DateFormatEnum.DATE_YMDD_JP:
      return `${year}年${month}月${day}日(${formatWeekday(dateObj)})`;
    case DateFormatEnum.DATE_YMD_JP:
      return `${year}年${month}月${day}日`;
    case DateFormatEnum.DATE_YMDHMS_EN:
      return `${year}-${pad(month)}-${pad(day)} ${pad(hour)}:${pad(minute)}:${pad(second)}`;
    case DateFormatEnum.DATE_YMDD_NORMAL:
      return `${year}/${month}/${day}/(${formatWeekday(dateObj)})`;
    case DateFormatEnum.DATE_YMDHM:
      return `${year}年${month}月${day}日 ${pad(hour)}:${pad(minute)}`;
    case DateFormatEnum.DATE_YMD_NORMAL:
      return `${year}/${month}/${day}`;
    default:
      return `${year}年${pad(month)}月${pad(day)}日(${formatWeekday(dateObj)})`;
  }
}

function formatWeekday(date: Date) {
  const weekday = date.getDay();
  return ['日', '月', '火', '水', '木', '金', '土'][weekday];
}

function pad(value: number) {
  return String(value).padStart(2, '0');
}

export function yyyyMMddToDate(date: string) {
  const year = date.slice(0, 4);
  const month = date.slice(4, 6);
  const day = date.slice(6, 8);
  return new Date(`${year}-${month}-${day}`);
}

// 日本時間Offset
const JAPAN_OFFSET_MINUTES = 9 * 60; // UTC+9

// 日本現在時間を取得
function getJapanTime(date: string) {
  const now = new Date(date);
  return new Date(now.getTime() + (JAPAN_OFFSET_MINUTES - now.getTimezoneOffset()) * 60000);
}

// 日本時間の１8時
function getJapanNoonTime(date: Date) {
  const japanDate = new Date(date);
  japanDate.setHours(12, 0, 0, 0);
  return japanDate;
}

//startDate:yyyy-MM-dd
//startDateから今日までの日付リストを取得する
//戻り値：[yyyy-MM-dd]
export const generateDateRange = (startDate: string) => {
  const start = new Date(startDate);
  const today = new Date();
  start.setHours(0, 0, 0, 0);

  const dates = [];
  while (start <= today) {
    dates.push(formatDateYMD(start));
    start.setDate(start.getDate() + 1);
  }
  return dates;
};
//Date to yyyy-MM-dd
export const formatDateYMD = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}; // 日本時間の18時00分00秒
function getJapanEndNoonTime(date: Date) {
  const japanDate = new Date(date);
  japanDate.setHours(18, 0, 0, 0);
  return japanDate;
}
//date:'yyyy-MM-dd'
export function getYesterdayNoonByDate(date: string) {
  const yesterday = new Date(date);
  yesterday.setHours(12, 0, 0, 0);
  yesterday.setDate(yesterday.getDate() - 1);
  return yesterday;
}

export function getJapanTimeRange(date: string) {
  // const today = getJapanTime(date);
  const today = new Date(date);
  const todayNoon = getJapanNoonTime(today);
  let yesterdayNoon = new Date(todayNoon);
  yesterdayNoon.setDate(yesterdayNoon.getDate() - 1);
  yesterdayNoon = getJapanEndNoonTime(yesterdayNoon);

  return {
    yesterday: formatDate(yesterdayNoon, 'yyyy-MM-dd'),
    today: formatDate(todayNoon, 'yyyy-MM-dd'),
    yesterdayNoon: formatDate(yesterdayNoon, 'yyyy-MM-dd HH:mm:ss'),
    todayNoon: formatDate(todayNoon, 'yyyy-MM-dd HH:mm:ss'),
  };
}

export const formatDateTime = (dateTime: string): string => {
  return dateTime.replace('T', ' ').split('.')[0];
};
