export const COMMON_TEXT = {
  HOME: {
    TSUNAGARI: 'つながり',
    NOTICE: 'お知らせ',
    GRAPH_DETAIL: 'グラフを見る',
    MITSUYOSO: 'ミッション',
    MORE: 'もっと見る',
    TODAY: '今日',
    STEPS: '歩数',
    WEEKLY_ACHIEVEMENT: '今週は',
    DAYS_COMPLETED: '日達成',
    WALKING_DISTANCE: '歩行距離',
    WALKING_TIME: '歩行時間',
    CALORIES_BURNED: '消費カロリー',
    MINUTES: '分',
    KILOMETERS: 'km', // 新增公里单位
    KILOCALORIES: 'kcal', // 新增千卡单位
    ADD_RECORD: '記録をつける',
    MENU: 'メニュー',
    PHOTO_UPLOAD: '写真投稿',
    COUPON: 'クーポン',
    EVENT: 'イベント',
    STEPS_UNIT: '歩',
  },
  PUSH: {
    TITLE: '健康診断の結果を調べます',
  },
  MENU: {
    HOME: 'ホーム',
    MAP: 'マップ',
    POST: 'ポスト',
    FRIEND: 'フレンド',
    MENU: 'メニュー',
  },
  BUTTON: {
    ADD: '追加',
    EDIT: '編集',
    DELETE: '削除',
    SAVE: '保存',
    CANCEL: 'キャンセル',
    SUBMIT: '送信',
    RESET: 'リセット',
    UPLOAD: 'アップロード',
    DOWNLOAD: 'ダウンロード',
    EXPORT: 'エクスポート',
    IMPORT: 'インポート',
    APPLY: '適用',
    CONFIRM: '確認',
    BACK: '戻る',
    NEXT: '次へ',
    FINISH: '完了',
    REFRESH: '更新',
    SEARCH: '検索',
    FILTER: 'フィルター',
    SORT: '並び替え',
    SELECT_ALL: 'すべて選択',
    DESELECT_ALL: 'すべて解除',
    CLOSE: '閉じる',
    KEEP_ON: '続ける',
    VIEW_PHOTO_POST: '写真投稿を見る',
    POST: '投稿',
    SETTING: '設定を開く',
  },
  MESSAGE: {
    LOADING: '読み込み中...',
    DATA_SYNC: 'データを通信中...',
    ERROR: 'エラーが発生しました',
    SUCCESS: '成功しました',
    NETWORK_ERROR: 'ネットワークエラーが発生しました',
    TIMEOUT: 'タイムアウトしました',
    MAINTENANCE: 'メンテナンス中です',
    NO_DATA: 'データがありません',
    REQUIRED: '必須項目です',
    UNAUTHORIZED: '権限がありません',
    SESSION_EXPIRED: 'セッションの有効期限が切れました',
    RETRY: '再試行',
  },
  AUTH: {
    LOGIN: 'ログイン',
    LOGOUT: 'ログアウト',
    SIGNUP: '新規登録',
    TERMS: '利用規約',
    PRIVACY_POLICY: 'プライバシーポリシー',
  },
  DIALOG: {
    CONFIRM: '確認',
    CANCEL: 'キャンセル',
    CLOSE: '閉じる',
    OK: 'OK',
    YES: 'はい',
    NO: 'いいえ',
    AGREE: '同意する',
    DISAGREE: '同意しない',
    DELETE_CONFIRM: '本当に削除しますか？',
    SAVE_CONFIRM: '保存せずに閉じますか？',
    LOGOUT_CONFIRM: 'ログアウトしますか？',
    BACK_CONFIRM: '変更が保存されていません。戻りますか？',
    PROCEED: '続行',
    ABORT: '中止',
  },
  ERROR: {
    400: '不正なリクエストです',
    401: '認証が必要です',
    403: 'アクセスが拒否されました',
    404: 'ページが見つかりません',
    408: 'リクエストタイムアウト',
    500: 'サーバーエラーが発生しました',
    502: 'ゲートウェイエラー',
    503: 'サービス利用不可',
    504: 'ゲートウェイタイムアウト',
    UNKNOWN: '不明なエラーが発生しました',
    TRY_AGAIN: '再試行してください',
    CONTACT_SUPPORT: 'サポートにお問い合わせください',
    CONNECTION_LOST: 'インターネット接続がありません',
    CONNECTION_RESTORED: 'インターネット接続が復旧しました',
  },
  UNIT: {
    CM: 'cm',
    KG: 'kg',
    KCAL: 'kcal',
    KM: 'km',
    STEP: '歩',
    YEN: '円',
    WEEK: '週',
    YEAR: '年',
    MONTH: '月',
    DAY: '日',
    HOUR: '時間',
    MINUTE: '分',
    SECOND: '秒',
    PERCENT: '%',
  },
};
