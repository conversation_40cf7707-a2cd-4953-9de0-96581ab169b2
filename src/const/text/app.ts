export const APP_TEXT = {
  HOME_PAGE: {
    TITLE: '',
  },
  HEALTH_SCORE_PAGE: {
    TITLE: 'ヘルスチェックAI',
    SETTING: '設定',
    MESSAGE: '今週のメッセージ',
    SCORE: 'スコア',
    WEEK_SCORE: '今週のスコア',
    MISSION: 'あなたの改善ミッション',
    HISTORY: 'スコア履歴',
    HISTORY_TITLE: '表示するヘルスチェックAI',
    ALL: 'すべて',
    FRAIL: 'フレイル予防',
    BLOODP: '血圧上昇習慣',
    IMMUNITY: '免疫力',
    BLOOD_GLUCOSE_AND_NEUTER_FAT: '血糖値・中性脂肪改善習慣',
    // WELL_BEING: 'Well-being',
    // STRESS: 'ストレス推定',
    BLOOD_GLUCOSE: '血糖値改善習慣',
    NEUTER_FAT: '中性脂肪改善習慣',
    ONE_MONTH: '1ヶ月',
    THREE_MONTHS: '3ヶ月',
    SIX_MONTHS: '6ヶ月',
    ONE_YEAR: '1年',
    FRAIL_DESC:
      'スマートフォンで収集した、直近２週間の歩数などの生活習慣情報から、フレイル予防に関するリスクを推定します。点数が低いほど、健康リスクが高いと推定されます。',
    BLOODP_DESC:
      'スマートフォンで収集した直近２週間の歩数などの生活習慣情報から、血圧が上昇するリスクのある生活習慣となっているかどうかを推定します。点数が低いほど、血圧が上昇するリスクが高い生活習慣であると推定されます。',
    IMMUNITY_DESC:
      'スマートフォンで収集した直近２週間の歩数などの生活習慣情報から、体内に侵入してきた病原体から体をまもる仕組みである免疫力の低下リスクを推定します。点数が低いほど、免疫力が低下するリスクが高いと推定されます。',
    BLOOD_GLUCOSE_DESC:
      '健康診断結果と直近２週間の歩数・体重から、１年後の検査値リスクを推定します。',
    NEUTER_FAT_DESC: '健康診断結果と直近２週間の歩数・体重から、１年後の検査値リスクを推定します。',
    // WELL_BEING_DESC:
    //   'スマートフォンで収集した直近２週間の歩数などの生活習慣情報から、Well-being（身体的・精神的・社会的に良好な状態を指します）の度合いを推定します。点数が低いほど、良くない状態になるリスクが高いと推定されます。',
    // STRESS_DESC:
    //   'スマートフォンで収集した、直近2週間の血圧情報から、血圧上昇習慣に関するリスクを推定します。点数が低いほど、健康リスクが高いと推定されます。',
    WALKING: '歩数',
    WAKE_UP_TIME: '起床時間',
    SLEEP_TIME: '睡眠時間',
    BED_TIME: '就寝時間',
    OUT_TIME: '外出時間',
    MESSAGE_CALCULATING:
      '現在ヘルスチェックAIの計算中です。ヘルスチェックAI利用開始から2週間経過した後の月曜日からメッセージやスコアを確認できます。',
    POINT: 'ポイント',
    POINT2: '点',
    MISSION_NOT_FOUND: '現在ミッションがありません',
    TIP1: '※毎週月曜日に判定結果が表示されます。',
    TIP2: '※複数のヘルスチェックAIをご利用の場合は、最も点数が低いヘルスチェックAIの結果が表示されます。',
    GOOD: '良好',
    CAUTION: '注意',
    CALCULATING: '計算中...',
    WORRY: '心配',
  },
  HEALTH_SCORE_INIT_USAGE_PAGE: {
    TITLE: 'ヘルスチェックAIの使い方',
    NEXT: '設定に進む',
    USERTEXT: 'ヘルスチェックAIでは、AIがあなたの健康に関するリスクを推定し判定結果を表示します。',
    USERTEXT2:
      'ヘルスチェックAIの判定結果に基づき、あなた専用の「改善ミッション」を作成します。改善ミッションをクリアして、今や将来の健康リスクを減らしていきましょう！',
    USERTEXT4:
      '改善ミッションは、ホーム画面、ミッション一覧、ヘルスチェックAI画面に毎週月曜日に表示されます。',
    USEMISSIONTEXT:
      '※初回の改善ミッションは、ヘルスチェックAI利用開始の2週間後の月曜日に表示されます。',
  },
  HEALTH_SCORE_INIT_SEL_PAGE: {
    TITLE: 'ヘルスチェックAIの選択',
    USERTEXT2:
      'スマートフォンの利用履歴や歩数等の生活習慣情報から、以下のAIによるヘルスチェックAIを提供します。提供を希望するヘルスチェックAIを選択してください。',
    USERTEXT4:
      '※すでにチェックが入っているものは、主催団体から利用必須となっているヘルスチェックAIです。',
    USERTEXT5: '※推定を実施するために取得するデータは、次のページでご確認いただけます。',
    FRAIL_TITLE: 'フレイル予防',
    FRAIL_CONTENT:
      '体と心の健康に関するリスクを推定します。点数が低いほど、健康リスクが高いと推定されます。',
    BLOODP_TITLE: '血圧上昇習慣',
    BLOODP_CONTENT:
      '血圧が上昇するリスクのある生活習慣となっているかどうかを推定します。点数が低いほど、血圧が上昇するリスクが高い生活習慣であると推定されます。',
    IMMUNITY_TITLE: '免疫力',
    IMMUNITY_CONTENT:
      '体内に侵入してきた病原体から体をまもる仕組みである免疫力の低下リスクを推定します。点数が低いほど、免疫力が低下するリスクが高いと推定されます。',
    SALT_TITLE: '血糖値・中性脂肪改善習慣',
    SALT_CONTENT:
      '直近の健康診断結果と現状の歩数・体重から、各種生活習慣病の判定指標となる検査値が、１年後に悪化するリスクを統計的に推定します。点数が低いほどリスクが高いと推定されます。',
  },
  HEALTH_SCORE_INIT_AGREE_PAGE: {
    TITLE: 'ヘルスチェックAI利用同意',
    NEXT: '同意する',
    CANCEL: '同意しない',
    USERTEXT2: '「取得データ」の利用について',
    USERTEXT21: '以下の「取得データ」をもとに、健康に関するヘルスチェックAIを実施します。',
    USERTEXT22: '※「位置情報」や「生体情報」の取得利用も含まれます。',
    USERTEXT23:
      'また、以下の「取得データ」について、本サービスの分析、営業、研究、開発を目的として、個人を特定できないように加工し、統計データとして活用することができるものとします。',
    USERTEXT24:
      'なお、各機能は、医療行為や医業を目的として提供されるものではありません。各機能において提供される健康管理等に関する情報は参考情報であり、確実性、有用性、完全性等を保証するものではありません。何らかの傷病、障害、妊娠、その他心身の不調や変調がある場合は、適切な医療機関への相談等を行ってください。各機能を利用してなされた参加者の行為及びその結果について責任を負うものではありません。',
    USERTEXT3: '上記内容にご同意いただける場合、「同意する」を選択してください。',
    USERTEXT31: '※「同意しない」を選択しても、ミッション等の機能はご利用いただけます。',
    USERTEXT32:
      '※メニューの「ヘルスチェックAI機能の利用設定」から、機能をオフに設定すると、データの取得を停止することが可能です。ただし、機能をオフにすると、その機能はご利用いただけません。',
    DATA_TITLE: '取得データ',
    FRAIL_TITLE: 'フレイル予防',
    FRAIL_IOS_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>・性別<br/>・生年月日<br/>・身長<br/>・体重<br/>・歩幅<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>・就寝時間<br/>・起床時間<br/>・睡眠時間<br/>　<br/>■ヘルスケアから取得した生体情報<br/>・上った階数<br/>・ウォーキング＋ランニングの距離<br/>・歩数<br/>・歩行速度<br/>・歩行両脚支持時間<br/>・歩幅<br/>・歩行非対称性<br/>・睡眠開始時間<br/>・睡眠終了時間<br/>・身長<br/>・体重',
    FRAIL_ANDROID_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>・性別<br/>・生年月日<br/>・身長<br/>・体重<br/>・歩幅<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>・就寝時間<br/>・起床時間<br/>・睡眠時間<br/>　<br/>■位置情報<br/>・Google位置情報サービスが提供する位置情報（GPSチップが測定した位置情報のほか、基地局やWi-Fiアクセスポイントなどから割り出された緯度・経度情報）を5分間隔で取得<br/>　<br/>■アプリケーションの利用ログ<br/>・アプリケーションを操作した時間<br/>・アプリケーションのパッケージ名<br/>・アプリケーションのクラス名<br/>・アプリケーションのイベントタイプ（起動／終了）<br/>　<br/>■Google Fitで取得した生体情報<br/>・歩数<br/>・距離<br/>・ハートポイント<br/>・通常の運動<br/>・消費エネルギー<br/>・速度<br/>・身長<br/>・体重<br/>・睡眠開始時間<br/>・睡眠終了時間',
    BLOODP_TITLE: '血圧上昇習慣',
    BLOODP_IOS_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>・性別<br/>・生年月日<br/>・身長<br/>・体重<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>・就寝時間<br/>・起床時間<br/>　<br/>■位置情報<br/>・Apple位置情報サービスが提供する位置情報（GPSチップが測定した位置情報のほか、基地局やWi-Fiアクセスポイントなどから割り出された緯度・経度情報）を5分間隔で取得<br/>　<br/>■ヘルスケアから取得した生体情報<br/>・歩数<br/>・身長<br/>・体重<br/>・睡眠開始時間<br/>・睡眠終了時間',
    BLOODP_ANDROID_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>・性別<br/>・生年月日<br/>・身長<br/>・体重<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>・就寝時間<br/>・起床時間<br/>　<br/>■位置情報<br/>・Google位置情報サービスが提供する位置情報（GPSチップが測定した位置情報のほか、基地局やWi-Fiアクセスポイントなどから割り出された緯度・経度情報）を5分間隔で取得<br/>　<br/>■アプリケーションの利用ログ<br/>・アプリケーションを操作した時間<br/>・アプリケーションのパッケージ名<br/>・アプリケーションのクラス名<br/>・アプリケーションのイベントタイプ（起動／終了）<br/>　<br/>■Google Fitで取得した生体情報<br/>・歩数<br/>・身長<br/>・体重<br/>・睡眠開始時間<br/>・睡眠終了時間',
    IMMUNITY_TITLE: '免疫力',
    IMMUNITY_IOS_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>・就寝時間<br/>・起床時間<br/>　<br/>■位置情報<br/>・Apple位置情報サービスが提供する位置情報（GPSチップが測定した位置情報のほか、基地局やWi-Fiアクセスポイントなどから割り出された緯度・経度情報）を5分間隔で取得<br/>　<br/>■ヘルスケアから取得した生体情報<br/>・歩数<br/>・睡眠開始時間<br/>・睡眠終了時間',
    IMMUNITY_ANDROID_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>・就寝時間<br/>・起床時間<br/>　<br/>■位置情報<br/>・Google位置情報サービスが提供する位置情報（GPSチップが測定した位置情報のほか、基地局やWi-Fiアクセスポイントなどから割り出された緯度・経度情報）を5分間隔で取得<br/>　<br/>■アプリケーションの利用ログ<br/>・アプリケーションを操作した時間<br/>・アプリケーションのパッケージ名<br/>・アプリケーションのクラス名<br/>・アプリケーションのイベントタイプ（起動／終了）<br/>　<br/>■Google Fitで取得した生体情報<br/>・歩数<br/>・睡眠開始時間<br/>・睡眠終了時間',
    SALT_TITLE: '血糖値・中性脂肪改善習慣',
    SALT_IOS_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>・性別<br/>・生年月日<br/>・身長<br/>・体重<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>　<br/>■ヘルスケアから取得した生体情報<br/>・歩数<br/>・身長<br/>・体重<br/>　<br/>■健康マイレージに登録された健康診断情報<br/>・受診日<br/>・収縮期血圧<br/>・拡張期血圧<br/>・AST(GOT)<br/>・ALT(GPT)<br/>・γ-GTP<br/>・HDLコレステロール<br/>・LDLコレステロール<br/>・中性脂肪<br/>・HbA1c(NGSP)<br/>・空腹時血糖<br/>・血清クレアチニン<br/>・BMI',
    SALT_ANDROID_CONTENT:
      '■健康マイレージに登録された会員情報<br/>・会員番号<br/>・性別<br/>・生年月日<br/>・身長<br/>・体重<br/>　<br/>■健康マイレージに登録された生体情報<br/>・歩数<br/>　<br/>■Google Fitで取得した生体情報<br/>・歩数<br/>・身長<br/>・体重<br/>　<br/>■健康マイレージに登録された健康診断情報<br/>・受診日<br/>・収縮期血圧<br/>・拡張期血圧<br/>・AST(GOT)<br/>・ALT(GPT)<br/>・γ-GTP<br/>・HDLコレステロール<br/>・LDLコレステロール<br/>・中性脂肪<br/>・HbA1c(NGSP)<br/>・空腹時血糖<br/>・血清クレアチニン<br/>・BMI',
    RISK_OPEN: '＋ 開く',
    RISK_CLOSE: 'ー 閉じる',
  },
  HEALTH_SCORE_SET_PAGE: {
    TITLE: 'ヘルスチェックAIの利用設定',
    TOGGLE_TITLE: 'ヘルスチェックAI機能',
    TOGGLE_CONTENT_OFF: 'オフにすると、すべてのヘルスチェックAI機能がオフになります。',
    TOGGLE_CONTENT_ON: 'オンにすると、すべてのヘルスチェックAI機能がオンになります。',
    NOT_CHECKBLE_TITLE: '必須利用のヘルスチェックAI',
    CHECKBLE_TITLE: '任意利用のヘルスチェックAI',
    OFF_ALL1:
      'ヘルスチェックAIの利用設定をオフにすると、直ちに全てのヘルスチェックAI判定が停止になり、過去の判定結果は確認できなくなります。利用再開する場合は、またその日から新たにヘルスチェックAI判定を開始します。',
    OFF_ALL2: '本当にヘルスチェックAI機能をオフにしますか？',
    OFF_BTN: 'オフにする',
    OFF_PART1:
      'ヘルスチェックAIの利用設定をオフにすると、直ちに該当のスコア機能は停止になり、過去の判定結果は確認できなくなります。利用再開する場合は、またその日から新たにヘルスチェックAI判定を開始します。',
    OFF_PART2: '本当に以下のヘルスチェックAI機能をオフにしますか？',
    NEXT: '設定を保存',
    FRAIL: 'フレイル予防',
    BLOODP: '血圧上昇習慣',
    IMMUNITY: '免疫力',
    SALT: '血糖値・中性脂肪改善習慣',
    MODIFY_BUTTON: '変更する',
  },
  HEALTH_SCORE_CHANGE_PAGE: {
    TITLE: 'ヘルスチェックAIを変更',
    CHECKBLE_TITLE: '任意利用のヘルスチェックAI',
    OFF_PART1:
      'ヘルスチェックAIの利用設定をオフにすると、直ちに該当のスコア機能は停止になり、過去の判定結果は確認できなくなります。利用再開する場合は、またその日から新たにヘルスチェックAI判定を開始します。',
    OFF_PART2: '本当に以下のヘルスチェックAI機能をオフにしますか？',
    NEXT: '設定を保存',
    OFF_BTN: 'オフにする',
    FRAIL: 'フレイル予防',
    BLOODP: '血圧上昇習慣',
    IMMUNITY: '免疫力',
    SALT: '血糖値・中性脂肪改善習慣',
  },
  HEALTH_RECORD_PAGE: {
    TITLE: '健康記録',
    SYNC: 'データを同期する',
    ADD_RECORD: '記録をつける',
    STEPS: '歩数',
    STEPS_DISTANCE: '歩行距離',
    STEPS_TIME: '歩行時間',
    STEPS_CALORIES: '消費カロリー',
    WEIGHT: '体重',
    SLEEP: '睡眠',
    SLEEP_TIME: '睡眠時間',
    BLOOD_PRESSURE: '血圧',
    MORNING: '午前',
    AFTERNOON: '午後',
    BLOOD_GLUCOSE: '血糖値',
    BREAKFAST_BEFORE: '朝食前',
    BREAKFAST_AFTER: '朝食後',
    LUNCH_BEFORE: '昼食前',
    LUNCH_AFTER: '昼食後',
    DINNER_BEFORE: '夕食前',
    DINNER_AFTER: '夕食後',
    SLEEP_BEFORE: '就寝前',
    STEP: '歩',
    KG: 'kg',
    MMHG: 'mmHg',
    MGDL: 'mg/dl',
    HOURS: '時間',
    MINUTES: '分',
    SECONDS: '秒',
    KILOMETERS: 'km',
    KILOCALORIES: 'kcal',
    TARGET_ACHIEVEMENT_CALENDAR: '目標達成カレンダーを見る',
    BMI: 'BMI',
    BODY_FAT_RATE: '体脂肪率',
    PERCENT: '%',
    OTHER_RECORD: 'その他の記録',
    SETTING: '設定',
    HEALTH_CHECKUP: '健診・検診日登録',
    HEALTH_CHECKUP_RESULT: '健診・検診結果',
    MEDICATION_RECORD: '服薬記録',
    WALKING_TARGET: '歩数目標設定',
    DATA_SYNC_SETTING: 'データ連携設定',
    HEALTH_CHECKUP_TITLE: '健(検)診受診日',
    SYNC_TIMEOUT_TITLE: 'データ同期失敗',
    SYNC_TIMEOUT:
      'データ同期がタイムアウトしました。後ほど再度試すか、端末を再起動してお試しください。',
  },
  SET_GOAL_INTRO: {
    TITLE: '歩数目標',
    SUBTITLE: '毎日の歩数目標を設定しよう',
    TEXT: 'あなたに最適な歩数目標を設定することで、日々の達成状況を可視化し、健康管理につなげることができます。',
    NEXT: '設定を始める',
  },
  SET_GOAL_PLAN: {
    TITLE: 'プランを選択',
    RECOMMEND_TITLE: 'おすすめ歩数プラン',
    RECOMMEND_CONTENT: '性別・年齢に合わせた、健康で長生きのための歩数を自動で目標に設定します。',
    PLUSTEN_TITLE: '＋10プラン',
    PLUSTEN_CONTENT: '普段のあなたの歩数に＋10のウォーキングを自動計算して目標に設定します。',
    CUSTERM_TITLE: '自分で設定プラン',
    CUSTERM_CONTENT: '目標歩数を自分で入力して設定します。',
    POPUP_TITLE: '＋10（プラス・テン）とは？',
    POPUP_CONTENT:
      '「座りっぱなしの時間が長くなりすぎないように、今より10分多くからだを動かそう」という意味で「＋10（プラス・テン）」という取り組みがあります。これは厚生労働省が策定した「健康づくりのための身体活動基準2013」を基に、国民がよりわかりやすく実践しやすいように作成された「アクティブガイド」の中で提唱されているものです。',
    NEXT: '設定に進む',
  },
  SET_GOAL_REC: {
    TITLE: '目標歩数を設定',
    SUBTITLE: '性別と年代を選択してください。',
    TEXT: '毎日この歩数を達成することで、心疾患や糖尿病、高血圧などのリスクを減らす効果があります。',
    DATASRC: '引用元：',
    SEX: '性別',
    AGE: '年代',
    UNIT: '歩 / 日',
    DATA_TITLE: 'おすすめの目標歩数',
    NEXT: '目標を決定する',
    NEXT_CHANGE: '目標を変更する',
    MAN: '男性',
    WOMAN: '女性',
    AGE10: '10代',
    AGE20: '20代',
    AGE30: '30代',
    AGE40: '40代',
    AGE50: '50代',
    AGE60: '60代',
    AGE70: '70代',
    AGE80: '80代',
  },
  SET_GOAL_PLUSTEN: {
    TITLE: '目標歩数を設定',
    SUBTITLE: 'あなたにおすすめの目標歩数をご確認ください。',
    TEXT: 'あなたの過去の平均歩数に＋10（プラス・テン）の運動＝1000歩を加算した歩数を表示しています。過去歩数が1日も無い場合は、全国平均の6000歩に1000歩を加算した歩数を表示しています。',
    UNIT: '歩 / 日',
    SELECTTEXT: 'の平均歩数を基準にする',
    DATA_TITLE: 'おすすめの目標歩数',
    NEXT: '目標を決定する',
  },
  SET_GOAL_CUS: {
    TITLE: '目標歩数を設定',
    SUBTITLE: '目標歩数を入力してください。',
    TEXT: '継続のため8,000歩〜15,000歩の目標をおすすめします。厚生労働省の健康日本21によると、20〜64歳の男女は1日8,000歩、65歳以上の男女は1日6,000歩が推奨されています。',
    UNIT: '歩',
    NEXT: '目標を決定する',
  },
  GOAL: {
    TITLE: '目標歩数を設定',
    CHOICE: '選択中のプラン',
    REC: 'おすすめ歩数プラン',
    PLUSTEN: '＋10プラン',
    CUS: '自分で設定プラン',
    STEP_TITLE: '目標歩数',
    CALORIE_TITLE: '想定消費カロリー',
    DIST_TITLE: '想定歩行距離',
    TIME_TITLE: '想定歩行時間',
    STEP_UNIT: '歩 / 日',
    CALORIE_UNIT: 'kcal / 日',
    DIST_UNIT: 'km / 日',
    TIME_UNIT: ' / 日',
    BTN_PLAN: 'プランを変更',
    BTN_STEPS: '目標歩数のみを変更',
  },
  HEALTH_CHECKUP: {
    TITLE: '健康診断',
    OTHER_SERVICES: '他サービスと連携する',
    HOME_HINT:
      '健康診断情報をまだ登録されていない場合は、マイナポータル連携もしくは手入力で登録をしてください。',
    SUBTITLE: '健康診断情報登録',
  },
  CHECKUP_DATE_RECORD: {
    TITLE: '健(検)診受診日',
    SUBTITLE: '各種健(検)診受診日の一覧です。',
  },
  DATA_CONNECT: {
    TITLE: 'データ連携設定',
    SUB_TITLE1: '連携サービス設定',
    SUB_TITLE2: 'データ連携設定',
    HEALTHCARE: 'ヘルスケア',
    HEALTCONNECT: 'ヘルスコネクト',
    HEALTCONNECT_CONNECT: '設定方法を確認する',
    FITBIT: 'Fitbit',
    OMRON: 'OMRON connect',
    WEIGHT: '体重',
    SLEEP: '睡眠時間',
    PRESS: '血圧',
    INPUT: '手入力',
    SMARTPHONE: 'スマートフォン',
    SRCITEMS: [
      '手入力',
      'ヘルスケア iPhone',
      'ヘルスケア Apple Watch',
      '',
      'ヘルスコネクト',
      '',
      'Fitbit',
      'OMRON connect',
    ],
    HEALTHCARE_DESC: '※無効にできません',
    IOS_DESC1:
      '※本アプリと「ヘルスケア」を連携する場合、事前に「ヘルスケア」側の設定許可をONにしてください。',
    IOS_DESC2:
      '※「ヘルスケア」側の許可設定がOFFの場合、本アプリ内のデータ連携設定を行なってもデータ連携がされません。',
    IOS_DESC3:
      '＜手順＞<br/>①「設定」を起動する<br/>②プライバシーとセキュリティを選択する<br/>③ヘルスケアを選択する<br/>④健康マイレージを選択する<br/>⑤すべての読み出し許可をONにする<br/>※詳細な操作手順は、ヘルプセンターをご参照ください。',
    ANDROID_DESC1:
      '※「スマートフォン」または「ヘルスコネクト」を連携する場合、事前に「スマートフォン」側の「身体活動」と「位置情報」の設定許可をONにしてください。',
    ANDROID_DESC2:
      '※「スマートフォン」側の許可設定がOFFの場合、画面上部のデータ連携設定を行なってもデータ連携がされません。',
    ANDROID_DESC3:
      '※設定方法はお使いの機種によって異なります。詳しくはお使いの機種メーカーにお問い合わせください。',
    CONFIRM_OFF_PREFIX: '連携サービス設定を無効にすると、データ連携設定において「',
    CONFIRM_OFF_SURFIX: '」を選択できなくなります。本当に設定を無効にしますか？',
    CONFIRM_OFF_BTN: '無効にする',
    CONFIRM_CLOSE: '閉じる',
    CONFIRM_OFF_DISABLE_TITLE: '設定を無効にできません',
    CONFIRM_OFF_DISABLE_PREFIX: 'データ連携を利用中のため、「',
    CONFIRM_OFF_DISABLE_SURFIX: '」の連携サービス設定を無効にできません。',
    CONFIRM_OFF_DISABLE_TXT2: '下記のデータ連携設定を別のものに変更してください。',
    CONFIRM_ITEMS_TITLE: '連携中のデータ',
  },
  CONNECT: {
    PATH: '/other-connect/health-connect',
    TITLE_BAR: 'ヘルスコネクト連携同意',
    TITLE_SETTING: '設定方法',
    TITLE: '下記のサービスと連携します。',
    DATA_TITLE: '取得するデータ',
    DATAITEMS: ['体重', '睡眠時間', '血圧'],
    HEALTH_CONNECT: 'ヘルスコネクト',
    CONFIRM_CLOSE: '閉じる',
    DATA_NOTE:
      '※けんこうマイレージに反映するデータはサービス連携後に「データ連携設定画面」から変更可能です。',
    CONNECTDESCRIPTION:
      'サービス連携することで、取得したデータが自動で記録されます。ヘルスコネクトとけんこうマイレージを連携することに同意しますか？',
    AGREE_BUTTON: '同意して次へ',
    OPEN_GOOGLE_PLAY: 'Google Play ストアを開く',
    GOOGLE_PLAY_CONTEXT:
      'ヘルスコネクトのアプリがインストールされていません。Google Play ストアに移動しますか？',
    CONNECT_GUIDE_TITLE1: 'データ登録用アプリのインストールおよび初期設定をする',
    CONNECT_GUIDE_CONTEXT1:
      ' 歩数等を連携いただく場合には、ヘルスコネクトに対しデータ連携しているデータ登録用のアプリ（GoogleFit等）が必要になります。\nデータ登録用のアプリがない場合には、Google Playストアからインストールし、初期設定を行ってください。',
    CONNECT_GUIDE_TITLE2: 'ヘルスコネクトのインストールおよび初期設定をする',
    CONNECT_GUIDE_CONTEXT2:
      'つぎにヘルスコネクトアプリが必要になります。\n アプリが入っていない場合には、Google Playストアからインストールし、初期設定を行なってください。',
    CONNECT_GUIDE_TITLE3: '1でインストールしたアプリとヘルスコネクトを同期設定する',
    CONNECT_GUIDE_CONTEXT3: 'Google Fitの場合の設定方法\nGoogle Fit を押してアプリを起動する',
  },
  FITBIT: {
    TITLE_BAR: 'Fitbit連携同意',
    TITLE: '下記のサービスと連携します。',
    DATA_TITLE: '取得するデータ',
    DATAITEMS: ['体重', '睡眠時間'],
    CONTENT: 'Fitbit',
    DATA_NOTE:
      '※健康マイレージに反映するデータはサービス連携後「データ連携設定画面」から変更可能です。',
    CONNECTDESCRIPTION:
      'サービス連携することで、取得したデータが自動で記録されます。Fitbitと健康マイレージを連携することに同意しますか？',
    AGREE_BUTTON: '同意して次へ',
  },
  OMRON: {
    TITLE_BAR: 'OMRON connect連携同意',
    TITLE: '下記のサービスと連携します。',
    DATA_TITLE: '取得するデータ',
    CONTENT: 'OMRON connect',
    DATA_NOTE:
      '※健康マイレージに反映するデータはサービス連携後「データ連携設定画面」から変更可能です。',
    CONNECTDESCRIPTION:
      'サービス連携することで、取得したデータが自動で記録されます。OMRON connectと健康マイレージを連携することに同意しますか？',
    AGREE_BUTTON: '同意して次へ',
  },
  FITBIT_FINISH: {
    TITLE: 'Fitbit連携',
    SUCCESS: '設定が完了しました',
    SUCCESS_DESC:
      '「データ連携設定」の歩数・体重・睡眠時間において、Fitbitのデータを連携する項目については、「Fitbit」を選択してください。',
    BTN_DATA_CONNECT: 'データ連携設定に戻る',
    FAIL: '連携ができませんでした',
    FAIL_DESC: 'エラーが発生したため、連携が完了しませんでした。再度連携設定を実施してください。',
    CHANGE_USER_DESC1:
      'このFitbitアカウントは、すでに別の健康マイレージアカウントと連携されています。別の健康マイレージアカウントとFitbitの連携を解除して、このアカウントと連携してもよろしいですか？',
    CHANGE_USER_DESC2:
      '※一つのFitbitアカウントに、複数の健康マイレージアカウントを連携させることはできません。',
    CHANGE_USER_OK: 'このアカウントと連携する',
    CHANGE_USER_CANCEL: '連携設定をやめる',
  },
  POINT: {
    TITLE: 'ポイント',
    HOLD_POINT: '保有ポイント',
    GIFT_SELECTABLE: '景品選択可能ポイント',
    RUNNING: 'ただいま抽選応募受付中！',
    START_SOON: 'まもなく抽選を開催予定！',
    NO_START: '現在抽選は開催されていません',
    DRAWING: '抽選情報を見る',
    DRAWING_HISTORY: '抽選履歴を見る',
    YEAR_POINT: '今年度獲得ポイント',
    ACQUISTION_POINT: '通算獲得ポイント',
    HISTORY: 'ポイント履歴',
    NO_HISTORY: 'ポイント履歴がありません',
    CATEGORY: '表示カテゴリー',
    DATE: '表示期間',
    HINT_PAGE: 'ランキングについて',
    CHANGE_POINT: '今年度のポイント交換状況',
    POSSIBLE_POINT: '交換可能ポイント',
    MAX_POINT: '(交換上限)',
    OTHER_POINT: '他のポイントに交換する',
    POINT_RESET_DESC: '※ポイント交換には本人確認が必要です。',
    POINT_RESET_DESC2:
      '※交換したポイントは寄付も可能です。（寄付する場合は交換内容確認画面で設定してください）',
  },
  HEALTH_CONNECT_FINISH: {
    TITLE: 'ヘルスコネクト連携',
    SUCCESS: '設定が完了しました',
    SUCCESS_DESC:
      '「データ連携設定」の歩数・体重・睡眠時間・血圧・血糖値において、ヘルスコネクトのデータを連携する項目については、「ヘルスコネクト」を選択してください。',
    FAIL: '連携ができませんでした',
    BTN_DATA_CONNECT: 'データ連携設定に戻る',

    FAIL_DESC: 'エラーが発生したため、連携が完了しませんでした。再度連携設定を実施してください。',
  },
  OMRON_FINISH: {
    TITLE: 'OMRON connect連携',
    SUCCESS: '設定が完了しました',
    SUCCESS_DESC: '',
    BTN_DATA_CONNECT: 'データ連携設定に戻る',
    FAIL: '連携ができませんでした',
    FAIL_DESC: 'エラーが発生したため、連携が完了しませんでした。再度連携設定を実施してください。',
  },
  LOTTERY: {
    TITLE: '抽選',
    TITLE_RESULT: '抽選結果',
    SCHEDULED: '開催予定の抽選',
    DURING: '開催中の抽選',
    NO_LOTTERY: '現在開催中の抽選はありません',
    ENDED: '終了した抽選',
    SELECT_AND_APPLY: '景品を選んで応募する',
    APPLICATION_PERIOD: '応募期間',
    TARGET_POINTS: '対象ポイント',
    GET_POINT: 'に獲得したポイント',
    TIME_END: ' 23:59まで',
    DATE_END: 'まで',
    ANNOUNCEMENT_DATE: '当選発表日',
    ANNOUNCEMENT_METHOD: '発表方法',
    ANNOUNCEMENT_METHOD_CONTENT: 'アプリ内のお知らせにてご連絡',
    NOTES: '注意事項',
    NOTES_CONTENT:
      '• 応募期間中は何度でも景品の変更が可能です。\n• 応募には個人情報の登録が必要です。\n • 応募には事前に景品の選択が必要です。\n •  ポイントは',
    NOTES_CONTENT_END: 'に消費されます。',
    CURRENT_APPLICATION: '現在の応募内容',
    SELECTED_PRIZE: '選択中の景品',
    NO_SELECTED_PRIZE: '選択中の景品がありません',
    APPLY: '景品を選んで応募する',
    LOTTERY_CHANGE: '応募内容を変更する',
    WINNING: '当選',
    NOT_STARTED: '応募開始前です',
    FUTURE_RELEASE: '景品は後日公開予定',
    APPLICATION_COUNT: '応募口数',
    DONATE: '寄付する',
    PRIZE: '景品',
    MOUTH: '口',
    TOTAL: '合計',
    EVENT_INFORMATION: '開催情報',
    EVENT_OVERVIEW: '開催概要',
    PRIZE_LIST: '景品一覧',
    APPLIED_PRIZE: '応募した景品',
    RECEIVED: '受取済',
    RECEIVE: '受け取る',
    RECEIVED_COMPLETED: '受取済み',
    OWNED_POINTS: '保有ポイント',
    SELECTED_POINTS: '選択中のポイント',
    REMAINING_SELECTABLE_POINTS: '残りの選択可能ポイント',
    PARTICIPANT_NAME_SUFFIX: '名様', // 添加了属性名 "PARTICIPANT_NAME_SUFFIX"
    PER_ENTRY_UNIT: 'p / 1口',
    WON_PRIZE: '当選した景品',
    CLAIM_PERIOD: '受取期限',
    CLAIM_EXPIRED: '受取期限を過ぎました',
    NOT_PARTICIPATED: 'この抽選に参加していません',
    PRIZE_IS: '景品は',
    DATE_SEND: '頃に発送予定です。',
    LOTTERY_PUSH_LOSE: '次の抽選へのご応募をお待ちしております。',
    LOTTERY_PUSH_WIN_TITLE: '当選おめでとうございます！',
    LOTTERY_PUSH_WIN:
      '景品を受け取るためには、電子マネーの交換手続きが必要です。下記のボタンからお手続きを実施してください。',

    LOTTERY_DAY: '抽選日',
    GO_LOTTERY_DETAIL: '抽選結果を見る',
  },
  DATA_SRCSET: {
    TITLE_PRESS: '血圧データ連携設定',
    TITLE_WEIGHT: '体重データ連携設定',
    TITLE_SLEEP: '睡眠時間データ連携設定',
    CHOICE_INPUT: '手入力',
    CHOICE_HEALTHCON: 'ヘルスコネクト',
    CHOICE_FITBIT: 'Fitbit',
    CHOICE_OMRON: 'OMRON connect',
    CHOICE_HEALTHCARE: 'ヘルスケア',
    WATCH: 'Apple Watch',
    IPHONE: 'iPhone',
    SRCITEMS: [
      '手入力',
      'ヘルスケア',
      'ヘルスケア',
      '',
      'ヘルスコネクト',
      '',
      'Fitbit',
      'OMRON connect',
    ],
    BTN_DATA_CONNECT: '変更する',
    CONFIRM_TITLE: 'データ連携設定を変更する際の注意事項',
    CONFIRM_SUB1: 'ご利用前に',
    CONFIRM_TXT1:
      'お使いのウェアラブルデバイスの歩数をiPhoneのヘルスケアに反映いただく必要があります。',
    CONFIRM_SMTXT1:
      '※反映方法はウェアラブルデバイスの種類によって異なるため、お使いのデバイスメーカーにご確認ください。',
    CONFIRM_SUB2: '連携サービスの切替について',
    CONFIRM_TXT2: '連携サービスを変更する前の歩数は反映されませんので、ご注意ください。',
    CONFIRM_BTN: '変更する',
    CONFIRM_CLOSE: '閉じる',
    FITBIT_OFF_TITLE: 'Fitbitのデータを取得できませんでした',
    OMRON_OFF_TITLE: 'OMRON connectのデータを取得できませんでした',
    OFF_CONTENT:
      '連携サービス設定から、一度OFFにしてから再度ONにしていただき、連携設定を最初から実施してください。',
    PERMISSION_TITLE: 'ヘルスコネクトのデータを取得できませんでした',
    PERMISSION_CONTETNT:
      '連携サービスへのアクセスが許可されていません。再度アクセス権限を付与してから操作してください。',
    PERMISSION_OK: '閉じる',
  },
  MISSION_PAGE: {
    TITLE: 'ミッション',
    ACHIEVEMENT_TITLE: '今日の達成状況',
    ACHIEVEMENT_DESCRIBE_NO: 'ポイントゲットはまた明日',
    ACHIEVEMENT_DESCRIBE: 'ポイントゲットできるミッションがあります',
    DESCRIBE: 'ポイントゲットできるミッション',
    MORE: '詳しく見る',
    IMPROVE: 'あなたの改善ミッション',
    AFTER: 'あと',
    INDIVIDUAL: '個',
    ACHIEVEMENTCEILING: '達成上限',
    ACHIEVE_ALL: '全て達成!',
    ACHIEVE_TEXT: '達成!',
    COLUMN: 'コラム',
    VIDEO: '動画',
    POINT: 'ポイント',
    LEVEL: 'レベル',
    JUDGMENT: '改善',
    HEALTH_GENERAL: '健康一般',
    HEALTH_BUTTON_CONFIRM: 'ヘルスチェックAIを確認する',
    DETIAL_TITLE: 'ミッション詳細',
    DETIAL_BUTTON_COLUMN: '最後まで読んでミッション達成',
    DETIAL_BUTTON_TRUE: 'ミッション達成',
    DETIAL_BUTTON_FINISH: 'ミッションを達成した',
    DETIAL_BUTTON_VIDEO: '動画を最後まで見てミッション達成',
    DETIAL_BUTTON_WALK: '目標以上の歩数になったら\n自動でミッション達成',
    DETIAL_BUTTON_SETTING: '設定が完了した',
    DETIAL_BUTTON_LOOK: 'マイページ（仮称）を見る',
    DETIAL_POPUP_WALK: 'ミッションを達成！',
    DETIAL_TOMORROW: 'ポイントは明日以降の獲得となります',
    DETIAL_BUTTON_CONTENT: 'このミッションは、あなたのヘルスチェックAIに基づいて生成されています。',
    DETIAL_SETTING_MESSAGE: '設定がまだ完了していません',
    DIALOG_SUB_TITLE: 'ポイントゲット',
    BONUS: 'ボーナス',
    BONUS_COUNT: '1回',
    POIONT_COUNT: '1p',
    WALK_FINISH: '歩数ミッションを達成しました',
    MISSION_P: 'p',
    CORRECTPOINT: '正解',
    INCORRECTPOINT: '不正解',
    ANSWER: 'あなたの回答',
    ANSWER_BETTER: '回答は以上でよろしいですか？',
    ANSWER_CANNOT: '※この回答は取り消しができません',
    ANSWER_SORRY: '残念！不正解',
    ANSWER_CORRECTPOINT: '正解！',
    CORRECT_ANSWER: '正解は',
    ACQUIRED_AFTER_TOMORROW: 'このミッションはすでに達成済みです',
    TODAY_ACHIEVED: 'ミッション達成済み',
  },
  HOME_DIALOG: {
    STEP_POINT: '歩数ボーナスを獲得！',
    POINT: 'ポイントゲット',
  },
  REGISTRATION_CREATE: {
    TITLE: 'ユーザー情報の確認',
    TITLE_INPUT: 'ユーザー情報の入力',
    TITLE_BASIC: '基本情報',
    GROUP_NM: '所属団体',
    NAME: '氏名',
    BIRTH_DT: '生年月日',
    SEX: '性別',
    NICK_NAME: 'ニックネーム',
    CONTACT: '連絡先',
    ADDRESS: '住所',
    EMAIL: 'メールアドレス',
    PHONE_NO: '電話番号',
    BODY_INFO: 'からだの情報',
    HEIGHT: '身長',
    WEIGHT: '体重',
    PACE: '歩幅',
    INSURER_INFO: '保険証情報',
    // TODO: wait api name
    INSURER_NO: '保険者番号',
    INSURED1: '被保険者記号',
    INSURED2: '被保険者番号',
    OTHER: 'その他',
    INSURED3: '社員番号',
    EDIT: '編集',
    CONFIRM_TITLE: '登録が完了しました',
    CONFIRM_CONTENT:
      '画面上部のポイントカードをスワイプすることで、参加団体ごとのポイントを確認することができます。',
    CONFIRM_AGREEMENT:
      '上記の登録いただいた個人情報を、以下の団体で利用します。詳細な利用目的等は利用規約をご確認いただき、同意の上、次の画面に進んでください。',
    REQUIRED: '必須',
    CONFIRM: '確認する',
    COMMON_HALF_NUMBER: 'この項目はすべて半角数字で入力してください',
    HALF_NUMBER: '半角数字で入力してください',
    DESC: '例：',
    INSPECT_EXAMPLE: '保険証の一例',
    INSPECT_DESC: '各項目の記載箇所は、保険証によって異なります。',
    INSURED1_MAX_BYTES: '※保険者証の記号番号前2桁を入力してください。',
    INSURED2_MAX_BYTES: '※保険者証の記号番号後6桁を入力してください。',
    PACE_DESC: '※歩幅は、歩行距離の算出などで使用します。',
    CONFIRM_DISAGREE:
      '個人情報の取扱いおよび、けんこうマイレージサービス利用規約に同意いただく必要があります。同意されない場合、アプリをご利用いただけません。',
    BODY_INFO_DESC:
      '※身長・体重を最新情報から取得するように設定すると毎日の「体重」入力での最新の値が、からだ情報に自動で反映されるようになります。\n※身長体重は、消費カロリーの算出などで使用します。',
    CONNECT_FINISH_TITLE: 'データ連携が完了しました',
    CONNECT_DISABLE_TITLE: 'データ連携ができません',
    CONNECT_DISABLE_CONTEXT: '歩数はステップカウントから本日分のみ取得します。',
  },
  PRICE_SELECTION: {
    PRICE_SELECTION_TITLE: '景品選択',
    PRICE_UPDATE_TITLE: '景品を変更',
    SELECTED_POINTS: '選択中のポイント',
    REMAINING_POINTS: '残りの選択可能ポイント',
    OVERSELECT_WARING: '選択可能なポイント数を超えています。景品の数量を変更しください',
    SINGLE_WARING: '注意：応募は1種類の景品に限られます',
    INSELECT_COUNT: '件を選択中',
    NAME: '名様',
    UNABLE_MAIL: '寄付不可',
    UNIT: 'p / 1口',
    CONTENT_CONFIRM: '応募内容を確認する',
    CLEAR_WARNING:
      '景品が選択されていません。 このまま進むと抽選に応募されなくなりますが、よろしいですか?',
    NO_LOTTERY: '抽選に応募しない',
    BACK: 'もどる',
    ALL_DONE: 'すべてクリア',
    CONFIRM_MODIFY: '応募内容変更の確認',
    CONFIRM_CONTENT_MODIFY: '応募内容確認',
    CONFIRM_LOTTERY_CONTENT: '抽選に応募する内容をご確認ください。',
    CONFIRM_SELECTED_POINTS: '選択中のポイント',
    OUT_OF_POINT: '選択可能なポイント数を超えています。景品の数量を変更しください。',
    SELECTED_PRIZE: '選択中の景品',
    CANCEL: '削除',
    DONATE_PRIZE: 'この景品を寄付する',
    REMARK_ONE: '※応募期間中は何度でも景品の変更が可能です。',
    REMARK_TWO: '※応募には個人情報の登録が必要です。',
    REMARK_THREE: '※ポイントは2025年5月1日に消費されます。',
    APPLY_CONTENT: 'この内容で応募する',
    MODIFY_APPLICATION: '応募内容を修正する',
    LOTTERY_APPLI_COMPLETE: '抽選応募完了',
    ACCEPTE_FOR_LOTTERY: '抽選の応募を受け付けました。',
    ELECT_ANNO_DAY: '当選発表日',
    ANNO_METHOD: '発表方法',
    CONTACT_US: 'アプリ内のお知らせにてご連絡',
    PUSH_LOTTERY_RESULT: 'プッシュ通知をONにしておくと、すぐに抽選結果を受け取れます。',
    SET_PUSH_NOTIFICATION: 'プッシュ通知を設定する',
    LOTTERY_APPLI_CONTENT: '抽選に応募する内容',
    APPLI_NUM: '応募口数：',
    DONATION: ' 寄付する',
    COMPLETE_REMARK_ONE: '※応募期間中は何度でも景品の変更が可能です。',
    COMPLETE_REMARK_TWO: (date: string) => `※ポイントは${date}に消費されます。`,
    LOTTERY_CHANGE: '応募内容を変更する',
    NEED_POINT: '必要ポイント',
    WINNING_NUM: '当選人数',
    DETAIL_REMARK: '※寄付する場合は応募内容確認画面で設定が必要です。',
  },
  COUPON: {
    CURRENT_COUPON: '配布中のクーポン',
    SHOP_INFO: '店舗情報',
    SHOP_ADDRESS: '住所',
    SHOP_PHONENUMBER: '電話番号',
    SHOP_RELATEDLINK: '関連リンク',
    SEARCH_PLACEHOLDER: '店舗名、住所、キーワードを入力',
    COUPON_TITLE: 'クーポン',
    OTHER_AREA_COUPON: '周辺の他のクーポン',
    SEARCHING: '検索中...',
    SEARCH_HISTORY: '検索履歴',
    SEARCH_HISTORY_NOT_FOUND: '検索履歴がありません',
    FILTER: '絞り込み',
    RECOMMENDED: 'あなたにおすすめ',
    POPULAR: '人気',
    SHOP_WITH_COUPON: 'クーポンがあるお店',
    ITEMS: '件',
    SORT: '並び替え',
    SEARCH_RESULT: '検索結果',
    INPUT_COUPON_NAME: 'クーポン名を入力',
    CATEGORY: 'カテゴリー',
    DISTANCE_FROM_HERE: '現在地からの距離',
    CONDITIONS_APPLY: '条件を適用する',
    CLEAR: 'クリア',
    TIMES_PER_PERSON: (times: string) => `1人${times}回まで`,
    EXPIRATION_DATE: '有効期限：',
    COUPON_USED_PEOPLE: '人がこのクーポンを利用しています',
    MESSAGE_USE_COUPON: 'クーポンを使用しますか？この操作は取り消せません。',
    BUTTON_USE: '使用する',
    COUPON_USED: 'このクーポンは使用済みです',
    SHOW_TO_SHOP: 'お店にこの画面を提示してください',
    CONDITIONS_USE: '適用条件',
    TIMES_CAN_USE: '使える回数',
    DRINK_IMAGE: 'ドリンク画像',
    COUPON_IMAGE: 'クーポン画像',
    REMAINING: '残り',
    TIMES: '回',
    COUPON_USED_CHECKED: '✓ クーポンを使用しました',
    USE_COUPON: 'クーポンを使用する',
    MESSAGE_FROM_SHOP: 'お店からのメッセージ',
    MESSAGE_SHOW_TO_STAFF:
      '1品以上ご注文いただいたお客様はサラダ追加無料です♫ご利用の際はクーポン画面をスタッフにご提示ください。',
    NOTES: '注意事項',
    MESSAGE_USE_ONLY_ONE:
      '同店舗のクーポンを2枚以上獲得した場合、1回のお会計で使用できるのは1枚のみです。',
    MESSAGE_CONFIRM: '対象店舗のみご利用いただけます。事前にご確認ください。',
    MESSAGE_MAYBE_END:
      'クーポンの付与総額が所定の金額に達した場合、利用可能期間の終了を待たず終了する場合がございます。ご了承ください。',
    CHECK_OTHER_AREA: '周辺の他のクーポンを見る',
    COUPON_USED_UNCHECKED: 'クーポンを使用しました',
  },
  MENU: {
    TITLE: '設定',
    COMMON: '一般',
    DATA_LINK_SETTINGS: 'データ連携設定',
    STEP_GOAL_SETTINGS: '歩数目標設定',
    PUSH_NOTIFICATION: 'プッシュ通知',
    PUSH_NOTIFICATION_SETTINGS: 'プッシュ通知設定',
    APP_INFORMATION: 'アプリ情報',
    HELP: 'ヘルプ',
    SERVICE_TERMS: '利用規約',
    LICENSE: 'ライセンス',
    SUPERVISOR: '監修者',
    RELATED_LINK: '関連リンク',
    CANCEL_PROCEDURE: '退会手続き',
    HEALTH_RECORD: '健康記録',
    STEP_COUNT: '歩数',
    BODY_WEIGHT: '体重',
    SLEEP_TIME: '睡眠時間',
    BLOOD_PRESSURE: '血圧',
    BLOOD_SUGAR_LEVEL: '血糖値',
    OTHER: 'その他',
    HEALTH_SCORE: 'ヘルスチェックAI',
    HEALTH_SCORE_SUBTITLE: '判定結果が出たお知らせを受け取れます。',
    WALKING_COURSE: 'ウォーキングコース',
    WALKING_COURSE_SUBTITLE: '新しいウォーキングコース<br/>のお知らせを受け取れます。',
    EVENT: 'イベント',
    EVENT_SUBTITLE: 'あなたにおすすめのイベント情報を受け取れます。',
    COUPON: 'クーポン',
    COUPON_SUBTITLE: 'あなたにおすすめのクーポン情報を受け取れます。',
    MISSION: 'ミッション',
    MISSION_SUBTITLE: '新しいミッションのお知らせを受け取れます。',
    PHOTO_SUBMISSION: '写真投稿',
    PHOTO_SUBMISSION_SUBTITLE: '自分の投稿にいいねされたことをタイムリーに受け取れます。',
    FRIEND: 'フレンド',
    FRIEND_SUBTITLE: 'フレンドからエールされたことなどをタイムリーに受け取れます。',
    LOTTERY: '抽選',
    LOTTERY_SUBTITLE: '抽選の開始や応募の締め切りなど情報をタイムリーに受け取れます。',
    APP_SERVICE_TERMS: 'アプリの利用規約',
    POLICY: 'プライバシーポリシー',
    YOKOHAMA_TERMS: '横浜ウォーキングポイントの利用規約',
    DOCOMO_TERMS: 'ドコモヘルスケアデモの利用規約',
    LICENSE_UNIRX_TITLE: 'UniRX',
    LICENSE_UNIRX_CONTENT:
      'The MIT License(MIT)\nCopyright(c)2014 Yoshifumi Kawai\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files(this “Software”), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions: \n the above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PRPOSE AND NONINFRINGEMENT, IN NO EVENT SHALL THE ANY cLIAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.',
    LICENSE_LINQ_TITLE: 'LINQ ti GameObjec',
    LICENSE_LINQ_CONTENT:
      'The MIT License(MIT)\nCopyright(c)2014 Yoshifumi Kawai\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files(this “Software”), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions: \n the above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PRPOSE AND NONINFRINGEMENT, IN NO EVENT SHALL THE ANY cLIAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.',
    SUPERVISOR_INFO: [
      {
        NAME: '近藤　克則',
        CONTENT1: 'フレイル推定AIに基づく助言内容に関する医学的妥当性の確認、監修者',
        CONTENT2: '※本アプリのその他の機能やサービスは監修していません。',
        SUBCONTENT1:
          '千葉大学　予防医学センター　社会予防医学研究部門　教授 \n国立長寿医療研究センター　研究所　老年学・社会科学研究センター　老年学評価研究部長（併任）\n一般社団法人　日本老年学的評価研究機構　代表理事（併任）',
      },
      {
        NAME: '薬師寺　忠幸',
        CONTENT1:
          '血圧上昇習慣推定AIの、血圧上昇につながる生活習慣リスクを推定する技術の仕様に関する医学的妥当性の確認、監修者',
        CONTENT2: '※本アプリのその他の機能やサービスは監修していません。',
        SUBCONTENT1: 'ウェルネストクリニック　院長',
      },
      {
        NAME: '清水　和弘',
        CONTENT1:
          '免疫力推定AIの、sIgAの変動につながる生活習慣を推定する技術の仕様に関する医学的妥当性の確認、監修者',
        CONTENT2: '※本アプリのその他の機能やサービスは監修していません。',
        SUBCONTENT1:
          '独立行政法人日本スポーツ振興センター ハイパフォーマンススポーツセンター／国立スポーツ科学センター 副主任研究員',
      },
    ],
    RELATED_LINK_INFO: {
      TITLE_ONE: (name: string) => `${name}役所健康増進課`,
      CONTENT1: (name: string) => `なごや健康マイレージ（${name}公式ウェブサイト）`,
      TITLE_TWO: (name: string) => `${name}役所地域ケア推進課`,
      CONTENT2: (name: string) => `介護予防の取り組み（${name}公式ウェブサイト）`,
      CONTENT3: '介護予防・日常生活支援総合事業（NAGOYAかいごネット）',
      CONTENT4: 'なごや八○（はちまる）フレイルテストを作成しました！（NAGOYAかいごネット）',
    },
    OUT_LOGIN: {
      TITLE: '退会手続き',
      BTN: '次へ',
      INPUT: '退会する団体を選択してください。',
      HINT: '※複数の団体を退会する場合は、該当する団体をすべて選択してください。',
      CONFIRM: {
        TITLE: '退会前にご確認ください',
        SUB_TITLE: '以下の団体を退会します。',
        DESCRIPTION:
          '退会すると、すべてのサービスが利用できなくなります。ポイントや記録されたデータが失われ、復元することはできません。',
        HINT: '上記をご了承いただいた上で、「退会する」を押してください。',
        CONFIRM_BTN: '退会する',
        CANCEL_BTN: 'キャンセル',
      },
      DIALOG: {
        TITLE: '本当に退会してよろしいですか？',
        CONTENT:
          '※退会の操作は取り消すことはできません。下記の「退会する」ボタンを押すことで退会処理は完了し、データは復元できません。',
      },
      SUCCESS: {
        TITLE: '退会が完了しました',
        ONLY_DESCRIPTION: 'ご利用いただきありがとうございました。',
        MULT_DESCRIPTION: '継続する団体で、引き続きサービスをご利用ください。',
        MULT_TITLE: '以下の団体を退会しました。',
        CLOSE_BTN: '閉じる',
      },
    },
    PUSH_NOTIFICATION_WARNING:
      '通知を受け取るには、端末でけんこうマイレージからの通知を許可してください',
    PUSH_NOTIFICATION_TIP:
      '※重要なお知らせについては、設定内容にかかわらず通知される場合があります。',
    SERVICE_TERMS_TIP: '本サービスについて各種利用規約を確認できます。',
  },
  MY_PAGE: {
    OPEN: 'マイページを見る',
    DIALOG: {
      FIRST_TIME:
        'ホーム画面のレベルアップミッションをクリアすると、レベルが一つずつアップします。ぜひチャレンジしてみてください！',
      GET_ICON: (level: string) =>
        `ミッションを達成してレベルアップしました。新しいアイコンになるまで、あと${level}レベルです。`,
      LEVEL_UP: (level: string) =>
        `ミッションを達成してレベルアップしました。次はレベル${level}でアイコンを獲得できます`,
      LEVEL_UP_MAX:
        'ミッションを達成してレベルアップしました。レベルは現在MAXです。おめでとうございます！',
    },
    MISSION_DETAIL: {
      MANUAL_COMPLETION: 'ミッションを達成した',
      AUTO_COMPLETION: 'ミッション内容を実行したら自動で達成',
    },
  },
  POST: {
    TITLE: 'いいね一覧',
    TAB_LEFT: 'もらった',
    TAB_RIGHT: 'おくった',
    RECEIVED_NO_DATA: 'まだいいねされた投稿がありません',
    SENT_NO_DATA: 'まだいいねした投稿がありません',
    SUB_MESSAGE: '他の人が投稿した写真をいいねしましょう！',
    TEXT1: 'さんに',
    TEXT2: 'いいねされました',
    REVIEW_TEXT: '審査中',
    REJECT_TEXT: '審査却下',
    NEW_POST: '新規投稿',
    NUM_SELECT: '枚選択中',
    MAX_PHOTO_NUM: '※最大3枚まで選択可',
  },
  MY_PHOTO_SUBMISSIONS: {
    TITLE: '自分の投稿',
    NO_DATA: 'まだ投稿がありません',
    SUB_MESSAGE: 'お気に入りの写真を投稿してみんなとシェアしましょう！',
  },
  OTHER_PHOTO_SUBMISSIONS: {
    TITLE: 'さんの投稿',
    NO_DATA: 'まだ投稿がありません',
  },
  POST_PAGE: {
    TITLE: '写真投稿',
    FILTER: '絞り込み',
    FRIEND: 'フレンド',
    CATEGORY: 'カテゴリー',
    USERS: 'ユーザー',
    DISTANCE: '現在地からの距離',
    CATEGORYS: ['すべて', '散歩', '自然', 'グルメ', 'スポーツ', 'おでかけ', 'イベント', 'その他'],
    FILTER_USERS: ['すべて', 'フレンド', 'いいねしたユーザー'],
    CLEAR: 'クリア',
    APPLY_CONDITIONS: '条件を適用する',
    TOP: '上へ',
    POST: '投稿',
    DETAIL_TITLE: 'さんの投稿',
    MY_DETAIL_TITLE: '自分の投稿',
    DETAIL_MENUE: 'メニュー',
    DETAIL_RECORD: 'この投稿を報告する',
    DETAIL_DELETE: '投稿を削除する',
    DETAIL_EDIT: '投稿を編集する',
    MAP_TEXT: 'マップで表示する',
  },
  POST_CONFIRM_PAGE: {
    TITLE: '投稿',
    COMMENT: 'コメント',
    COMMENT_SUB: '最大140文字',
    LOCATION: '位置情報',
    LOCATION_SUB: 'マップから変更できます',
    LOCATION_CHECK: 'マップに表示する',
    CATEGORY: 'カテゴリー',
    CATEGORYS: ['散歩', '自然', 'グルメ', 'スポーツ', 'おでかけ', 'イベント', 'その他'],
    POLICY: '投稿ポリシーを確認する',
    POST: '投稿する',
    MAP_POPUP_TEXT1: '自宅等の場所が位置情報から推測されないように配慮しましょう。',
    MAP_POPUP_TEXT2: '次回以降のマップへの表示設定は、前回投稿時の設定が自動で引き継がれます。',
    MAP_POPUP_TEXT3:
      '投稿によって表示の有無を変えたい場合は、手動でチェックの付け外しを行ってください。',
    FINISH_TITLE: '投稿しました',
    FINISH_CONTENT: '※投稿は内容を確認してから反映します。',
    FINISH_BUTTON: '写真投稿一覧へ戻る',
  },
  POST_MAP_PAGE: {
    TITLE: 'マップから位置情報を選択',
    COMMENT: '駅、都市、キーワードを入力',
    COMMENT_SUB: '選択された場所',
    CONFIRM: '決定',
  },
  POST_POLICY_PAGE: {
    TITLE: '投稿ポリシー',
    TEXT1: 'ご投稿いただく内容は、以下の要件をみたしたものに限ります。',
    POLICY: [
      '自分または第三者のプライバシーを侵害していないもの',
      '公序良俗に反しないもの',
      '誹謗中傷又は、他人の名誉もしくは信用を毀損する内容を含まないもの',
      '攻撃的表現、差別的表現、脅迫、嫌がらせを含まないもの',
      '他人へのなりすましや、第三者や組織との関係を詐称していなもの',
      'サービス提供者に対する要望、苦情を含まないもの',
      'サービス提供者が適切と判断した投稿',
    ],
    TEXT2:
      '投稿された内容については弊社スタッフが内容の確認を行い、掲載基準を満たす内容のみ公開いたします。',
    TEXT3:
      '掲載の是非や掲載の中止に関するお問い合わせにはお答えできませんので、あらかじめご了承ください。',
  },
  POST_IMAGE_CROP_PAGE: {
    TITLE: '画像を編集する',
    CONFIRM: '完了する',
  },
  POST_SELECT_PAGE: {
    TEXT1: '投稿写真を準備しましょう',
    TEXT2: '写真を撮る、またはアルバムから写真を選択してください。（最大3枚まで選択可）',
    BUTTON_CAMERA: '写真を撮る',
    BUTTON_PHOTO_SELECT: 'アルバムから写真を選択する',
    PHOTO_POLICY_TITLE1: '写真へのアクセスを',
    PHOTO_POLICY_TITLE2: '許可してください',
    PHOTO_POLICY_COMMENT:
      'これにより、けんこうマイレージがライブラリから写真をシェアし、写真をカメラロールに保存できるようになります。',
    CAMERA_POLICY_TITLE1: 'カメラへのアクセスを',
    CAMERA_POLICY_TITLE2: '許可してください',
    CAMERA_POLICY_COMMENT: 'これにより写真の撮影が可能になります。',
    DETAIL_MENU: '投稿',
  },
};
