// タイプ定義
export interface LevelUpIcon {
  icon: IconInfo; // アイコン
  getNewIcon: boolean; // 新規獲得かどうか
}

export interface IconInfo {
  imageUrl: string;
  name: string;
  speedInfo?: string;
}

export const ICON_DATA: Record<string, IconInfo> = {
  lv1: { imageUrl: '1', name: 'ナマケモノ', speedInfo: '' },
  lv2: { imageUrl: '2', name: 'ホッキョクグマ', speedInfo: '' },
  lv4: { imageUrl: '4', name: 'ニワトリ', speedInfo: '' },
  lv6: { imageUrl: '6', name: 'カワウソ', speedInfo: '' },
  lv8: { imageUrl: '8', name: 'ブタ', speedInfo: '' },
  lv10: { imageUrl: '10', name: 'ラッコ', speedInfo: '' },
  lv15: { imageUrl: '15', name: 'ミツバチのなかま', speedInfo: '' },
  lv20: { imageUrl: '20', name: 'ラクダ', speedInfo: '' },
  lv25: { imageUrl: '25', name: 'アオウミガメ', speedInfo: '' },
  lv30: { imageUrl: '30', name: 'ゾウ', speedInfo: '' },
  lv35: { imageUrl: '35', name: 'ゴマフアザラシ', speedInfo: '' },
  lv40: { imageUrl: '40', name: 'クロサイ', speedInfo: '' },
  lv45: { imageUrl: '45', name: 'ネコ', speedInfo: '' },
  lv50: { imageUrl: '50', name: 'イシイルカ', speedInfo: '' },
  lv55: { imageUrl: '55', name: 'キリン', speedInfo: '' },
  lv60: { imageUrl: '60', name: 'コウテイペンギン', speedInfo: '' },
  lv65: { imageUrl: '65', name: '競走犬', speedInfo: '' },
  lv70: { imageUrl: '70', name: 'シャチ', speedInfo: '' },
  lv75: { imageUrl: '75', name: 'サラブレッド', speedInfo: '' },
  lv80: { imageUrl: '80', name: 'カンガルー', speedInfo: '' },
  lv85: { imageUrl: '85', name: 'ノウサギ', speedInfo: '' },
  lv90: { imageUrl: '90', name: 'ライオン', speedInfo: '' },
  lv95: { imageUrl: '95', name: 'チーター', speedInfo: '' },
  lv100: { imageUrl: '100', name: 'ハヤブサ', speedInfo: '' },
};

export const levelUpIconsNew: Record<string, LevelUpIcon> = {
  lv1: { icon: ICON_DATA.lv1, getNewIcon: true },
  lv2: { icon: ICON_DATA.lv2, getNewIcon: true },
  lv3: { icon: ICON_DATA.lv2, getNewIcon: false },
  lv4: { icon: ICON_DATA.lv4, getNewIcon: true },
  lv5: { icon: ICON_DATA.lv4, getNewIcon: false },
  lv6: { icon: ICON_DATA.lv6, getNewIcon: true },
  lv7: { icon: ICON_DATA.lv6, getNewIcon: false },
  lv8: { icon: ICON_DATA.lv8, getNewIcon: true },
  lv9: { icon: ICON_DATA.lv8, getNewIcon: false },
  lv10: { icon: ICON_DATA.lv10, getNewIcon: true },
  lv11: { icon: ICON_DATA.lv10, getNewIcon: false },
  lv12: { icon: ICON_DATA.lv10, getNewIcon: false },
  lv13: { icon: ICON_DATA.lv10, getNewIcon: false },
  lv14: { icon: ICON_DATA.lv10, getNewIcon: false },
  lv15: { icon: ICON_DATA.lv15, getNewIcon: true },
  lv16: { icon: ICON_DATA.lv15, getNewIcon: false },
  lv17: { icon: ICON_DATA.lv15, getNewIcon: false },
  lv18: { icon: ICON_DATA.lv15, getNewIcon: false },
  lv19: { icon: ICON_DATA.lv15, getNewIcon: false },
  lv20: { icon: ICON_DATA.lv20, getNewIcon: true },
  lv21: { icon: ICON_DATA.lv20, getNewIcon: false },
  lv22: { icon: ICON_DATA.lv20, getNewIcon: false },
  lv23: { icon: ICON_DATA.lv20, getNewIcon: false },
  lv24: { icon: ICON_DATA.lv20, getNewIcon: false },
  lv25: { icon: ICON_DATA.lv25, getNewIcon: true },
  lv26: { icon: ICON_DATA.lv25, getNewIcon: false },
  lv27: { icon: ICON_DATA.lv25, getNewIcon: false },
  lv28: { icon: ICON_DATA.lv25, getNewIcon: false },
  lv29: { icon: ICON_DATA.lv25, getNewIcon: false },
  lv30: { icon: ICON_DATA.lv30, getNewIcon: true },
  lv31: { icon: ICON_DATA.lv30, getNewIcon: false },
  lv32: { icon: ICON_DATA.lv30, getNewIcon: false },
  lv33: { icon: ICON_DATA.lv30, getNewIcon: false },
  lv34: { icon: ICON_DATA.lv30, getNewIcon: false },
  lv35: { icon: ICON_DATA.lv35, getNewIcon: true },
  lv36: { icon: ICON_DATA.lv35, getNewIcon: false },
  lv37: { icon: ICON_DATA.lv35, getNewIcon: false },
  lv38: { icon: ICON_DATA.lv35, getNewIcon: false },
  lv39: { icon: ICON_DATA.lv35, getNewIcon: false },
  lv40: { icon: ICON_DATA.lv40, getNewIcon: true },
  lv41: { icon: ICON_DATA.lv40, getNewIcon: false },
  lv42: { icon: ICON_DATA.lv40, getNewIcon: false },
  lv43: { icon: ICON_DATA.lv40, getNewIcon: false },
  lv44: { icon: ICON_DATA.lv40, getNewIcon: false },
  lv45: { icon: ICON_DATA.lv45, getNewIcon: true },
  lv46: { icon: ICON_DATA.lv45, getNewIcon: false },
  lv47: { icon: ICON_DATA.lv45, getNewIcon: false },
  lv48: { icon: ICON_DATA.lv45, getNewIcon: false },
  lv49: { icon: ICON_DATA.lv45, getNewIcon: false },
  lv50: { icon: ICON_DATA.lv50, getNewIcon: true },
  lv51: { icon: ICON_DATA.lv50, getNewIcon: false },
  lv52: { icon: ICON_DATA.lv50, getNewIcon: false },
  lv53: { icon: ICON_DATA.lv50, getNewIcon: false },
  lv54: { icon: ICON_DATA.lv50, getNewIcon: false },
  lv55: { icon: ICON_DATA.lv55, getNewIcon: true },
  lv56: { icon: ICON_DATA.lv55, getNewIcon: false },
  lv57: { icon: ICON_DATA.lv55, getNewIcon: false },
  lv58: { icon: ICON_DATA.lv55, getNewIcon: false },
  lv59: { icon: ICON_DATA.lv55, getNewIcon: false },
  lv60: { icon: ICON_DATA.lv60, getNewIcon: true },
  lv61: { icon: ICON_DATA.lv60, getNewIcon: false },
  lv62: { icon: ICON_DATA.lv60, getNewIcon: false },
  lv63: { icon: ICON_DATA.lv60, getNewIcon: false },
  lv64: { icon: ICON_DATA.lv60, getNewIcon: false },
  lv65: { icon: ICON_DATA.lv65, getNewIcon: true },
  lv66: { icon: ICON_DATA.lv65, getNewIcon: false },
  lv67: { icon: ICON_DATA.lv65, getNewIcon: false },
  lv68: { icon: ICON_DATA.lv65, getNewIcon: false },
  lv69: { icon: ICON_DATA.lv65, getNewIcon: false },
  lv70: { icon: ICON_DATA.lv70, getNewIcon: true },
  lv71: { icon: ICON_DATA.lv70, getNewIcon: false },
  lv72: { icon: ICON_DATA.lv70, getNewIcon: false },
  lv73: { icon: ICON_DATA.lv70, getNewIcon: false },
  lv74: { icon: ICON_DATA.lv70, getNewIcon: false },
  lv75: { icon: ICON_DATA.lv75, getNewIcon: true },
  lv76: { icon: ICON_DATA.lv75, getNewIcon: false },
  lv77: { icon: ICON_DATA.lv75, getNewIcon: false },
  lv78: { icon: ICON_DATA.lv75, getNewIcon: false },
  lv79: { icon: ICON_DATA.lv75, getNewIcon: false },
  lv80: { icon: ICON_DATA.lv80, getNewIcon: true },
  lv81: { icon: ICON_DATA.lv80, getNewIcon: false },
  lv82: { icon: ICON_DATA.lv80, getNewIcon: false },
  lv83: { icon: ICON_DATA.lv80, getNewIcon: false },
  lv84: { icon: ICON_DATA.lv80, getNewIcon: false },
  lv85: { icon: ICON_DATA.lv85, getNewIcon: true },
  lv86: { icon: ICON_DATA.lv85, getNewIcon: false },
  lv87: { icon: ICON_DATA.lv85, getNewIcon: false },
  lv88: { icon: ICON_DATA.lv85, getNewIcon: false },
  lv89: { icon: ICON_DATA.lv85, getNewIcon: false },
  lv90: { icon: ICON_DATA.lv90, getNewIcon: true },
  lv91: { icon: ICON_DATA.lv90, getNewIcon: false },
  lv92: { icon: ICON_DATA.lv90, getNewIcon: false },
  lv93: { icon: ICON_DATA.lv90, getNewIcon: false },
  lv94: { icon: ICON_DATA.lv90, getNewIcon: false },
  lv95: { icon: ICON_DATA.lv95, getNewIcon: true },
  lv96: { icon: ICON_DATA.lv95, getNewIcon: false },
  lv97: { icon: ICON_DATA.lv95, getNewIcon: false },
  lv98: { icon: ICON_DATA.lv95, getNewIcon: false },
  lv99: { icon: ICON_DATA.lv95, getNewIcon: false },
  lv100: { icon: ICON_DATA.lv100, getNewIcon: true },
};

export function getLevelIconPath(level?: number | string): string {
  const levelNum = Number(level);
  const levelKey = levelNum > 100 ? 'lv100' : `lv${levelNum}`;
  const currentLevelIcon = levelUpIconsNew[levelKey] ?? levelUpIconsNew.lv1;
  return `/images/my-page/lv${currentLevelIcon.icon.imageUrl}.png`;
}

export function getDialogImagePath(level: number | string): string {
  return `/images/my-page/dialog-lv${level}.gif`;
}

// ユーティリティ関数: レベルからアイコン情報を取得する
export function getLevelIconInfo(level: number | string) {
  const levelNum = Number(level);
  const levelKey = levelNum > 100 ? 'lv100' : `lv${levelNum}`;

  // 該当データを取得。なければ lv1 にフォールバック
  const currentLevelIcon = levelUpIconsNew[levelKey] ?? levelUpIconsNew.lv1;

  return {
    imageUrl: currentLevelIcon.icon.imageUrl ?? '',
    name: currentLevelIcon.icon.name,
    getNewIcon: currentLevelIcon.getNewIcon ? '1' : '0',
    // speedInfo: currentLevelIcon.icon.speedInfo ?? "",
  };
}

/**
 * 计算距离下次获得头像还差多少等级
 * @param currentLevel 当前等级
 * @returns 距离下次获得新头像的等级差
 */
export const getNewIconGetLvlCnts = (currentLevel: string): number => {
  // 从当前等级开始向上查找，找到下一个 getNewIcon: true 的等级
  for (let level = Number(currentLevel) + 1; level <= 100; level++) {
    // 假设最高等级为100
    const levelKey = `lv${level}`;
    if (levelUpIconsNew[levelKey]?.getNewIcon === true) {
      return level - Number(currentLevel);
    }
  }

  // 如果没有找到更高等级的新头像，返回0或者一个默认值
  return 0;
};
