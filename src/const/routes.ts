export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  MENU: {
    MENU: '/menu',
    SETTINGS: '/menu/settings',
    PUSH_NOTIFICATION: '/menu/settings/push-notification',
    SERVICE_TERMS: '/menu/settings/service-terms',
    LICENSE: '/menu/settings/license',
    SUPERVISOR: '/menu/settings/supervisor',
    RELATED_LINK: '/menu/settings/related-link',
    OUT_LOGIN_LINK: '/out-login',
    ONLY_OUT_LOGIN_LINK: '/out-login/confirm',
  },
  POST: {
    POST: '/post',
    LIKE_LIST: '/post/like-list',
    MY_PHOTO_SUBMISSIONS: '/post/my-photo-submissions',
    OTHER_PHOTO_SUBMISSIONS: '/post/other-photo-submissions',
    CONFIRM: '/post/confirm',
    MAP: '/post/map',
    POLICY: '/post/policy',
    DETAIL: '/post/detail',
    PHOTO_SELECT: '/photo-post/photo-select',
  },
  HEALTH_SCORE: '/health-score',

  AUTH: {
    // LOGIN: '/auth/login',
    LOGIN: '/registration',
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password',
  },
  USER: {
    PROFILE: '/user/profile',
    SETTINGS: '/user/settings',
  },
  RISK: {
    USAGE: '/health-score-init/riskusage',
    AGREE: '/health-score-init/riskagree',
    SELECT: '/health-score-init/risksel',
    SETTING: '/health-score-init/risksetting',
    CHANGE: '/health-score-init/riskchange',
  },
  HEALTH_CHECKUP: {
    HEALTH_CHECKUP: '/health-checkup',
    MINA_PORTAL1: '/health-checkup/mina-portal1', //page 1
    MINA_PORTAL2: '/health-checkup/mina-portal2',
    HEALTH_COMPLETED: '/health-checkup/health-completed',
    LINK_OTHER_SERVICES: '/health-checkup/link-other-services',
    EXAM_RESULT: '/health-checkup',
  },
  GOAL: {
    GOAL: '/goal',
    INTRO: '/set-goal/intro',
    PLAN: '/set-goal/plan',
    PLUSTEN: '/set-goal/plusten',
    CUSTOM: '/set-goal/custom',
    RECMMEND: '/set-goal/recommend',
    HEALTH_RECORD: '/health-record',
  },
  DATA_CONNECT: {
    MAIN: '/data-connect',
    SRCSET: '/other-connect/datasrc',
    FITBIT_FINISH: '/other-connect/fitbit/finish',
    FITBIT: '/other-connect/fitbit',
    OMRON_FINISH: '/other-connect/omron/finish',
    OMRON: '/other-connect/omron',
    HEATHCON_FINISH: '/other-connect/health-connect/finish',
    HEATHCON: '/other-connect/health-connect',
    HEATHCON_INTRO: '/other-connect/health-connect/introduce',
  },
  WALKING_COURSE: {
    DETAIL: '/walking-course/detail',
  },
  LOTTERY: {
    DETAIL: '/lottery-result/result',
    DETAIL_LOSING: '/lottery-result/losing',
    LOTTERY: '/lottery-result',
    // DETAIL:'',
  },
} as const;

// 类型定义
export type RoutePath = (typeof ROUTES)[keyof typeof ROUTES];
export type AuthRoutePath = (typeof ROUTES.AUTH)[keyof typeof ROUTES.AUTH];
export type UserRoutePath = (typeof ROUTES.USER)[keyof typeof ROUTES.USER];
