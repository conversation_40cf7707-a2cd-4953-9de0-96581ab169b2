'use client';
import { outLoginAPI } from '@/api/modules/out-login';
import { stepGoalAPI } from '@/api/modules/step-goal';
import TopBar from '@/components/layout/top-bar';
import MenuPanel from '@/components/shared/menu-panel';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { useOutLoginStore } from '@/store/out-login-store';

export default function MenuSettingsPage() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const handleTarget = () => {
    setLoading(true, { text: 'データを通信中...' });
    stepGoalAPI
      .getStepTargetInfo()
      .then((response) => {
        setLoading(false);
        if (response.targetPlan === undefined) {
          router.push(ROUTES.GOAL.INTRO);
        } else {
          router.push(ROUTES.GOAL.GOAL);
        }
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        return Promise.reject();
      });
  };

  const { setIsMult } = useOutLoginStore();
  const handleOutLogin = () => {
    // setIsMult(true)
    // router.push(ROUTES.MENU.OUT_LOGIN_LINK);
    setLoading(true, { text: 'データを通信中...' });
    outLoginAPI
      .startup()
      .then((response) => {
        setLoading(false);
        if (response.organizerIdList && response.organizerIdList.length > 0) {
          setIsMult(true);
          router.push(ROUTES.MENU.OUT_LOGIN_LINK);
        } else {
          setIsMult(false);
          router.push(ROUTES.MENU.ONLY_OUT_LOGIN_LINK);
        }
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        return Promise.reject();
      });
  };

  const COMMON_ITEMS = [
    {
      label: APP_TEXT.MENU.DATA_LINK_SETTINGS,
      href: ROUTES.DATA_CONNECT.MAIN,
    },
    {
      label: APP_TEXT.MENU.STEP_GOAL_SETTINGS,
      onClick: () => {
        handleTarget();
      },
      href: '',
    },
    {
      label: APP_TEXT.MENU.PUSH_NOTIFICATION_SETTINGS,
      href: ROUTES.MENU.PUSH_NOTIFICATION,
    },
  ];
  const APP_INFO_ITEMS = [
    {
      label: APP_TEXT.MENU.HELP,
      href: '',
    },
    {
      label: APP_TEXT.MENU.SERVICE_TERMS,
      href: ROUTES.MENU.SERVICE_TERMS,
    },
    {
      label: APP_TEXT.MENU.LICENSE,
      href: ROUTES.MENU.LICENSE,
    },
    {
      label: APP_TEXT.MENU.SUPERVISOR,
      href: ROUTES.MENU.SUPERVISOR,
    },
    {
      label: APP_TEXT.MENU.RELATED_LINK,
      href: ROUTES.MENU.RELATED_LINK,
    },
  ];
  const APP_INFO_ITEMS_TWO = [
    {
      label: APP_TEXT.MENU.SERVICE_TERMS,
      href: ROUTES.MENU.SERVICE_TERMS,
    },
    {
      label: APP_TEXT.MENU.SUPERVISOR,
      href: ROUTES.MENU.SUPERVISOR,
    },
  ];
  const CANCEL_PROCEDURE = [
    {
      label: APP_TEXT.MENU.CANCEL_PROCEDURE,
      onClick: () => {
        handleOutLogin();
      },
      href: '',
    },
  ];
  const handleBackClick = () => {
    router.push('/menu');
  };
  return (
    <>
      <TopBar onBack={handleBackClick} title={APP_TEXT.MENU.TITLE} />
      <div className="p-6 pb-2 font-bold">{APP_TEXT.MENU.COMMON}</div>
      <MenuPanel menuItems={COMMON_ITEMS} className="rounded-lg" />
      <div className="p-6 pb-2 font-bold">{APP_TEXT.MENU.APP_INFORMATION}</div>
      <MenuPanel
        menuItems={process.env.NEXT_PUBLIC_APP_ENV === 'dev' ? APP_INFO_ITEMS : APP_INFO_ITEMS_TWO}
        className="rounded-lg"
      />
      <MenuPanel menuItems={CANCEL_PROCEDURE} className="mt-6 mb-6 rounded-lg" />
    </>
  );
}
