'use client';
import { menuAP<PERSON> } from '@/api/modules/menu';
import VersionTag from '@/components/layout/version-tag';
import MenuHealthRecordCreate from '@/components/shared/menu-health-record-create';
import Link from '@/components/shared/router-link';
import { Button } from '@/components/ui/button';
import { COLORS } from '@/const/colors';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useDynamicMenu } from '@/hooks/use-dynamic-menu';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { useRiskData } from '@/hooks/use-risk-data';
import { useRoutes } from '@/hooks/use-routes';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useScanInapp } from '@/hooks/use-scan-inapp';
import { useAuthStore } from '@/store/auth';
import { useDevModeStore } from '@/store/dev-mode';
import { useGlobalStore } from '@/store/global';
import type { LevelInfoResponse } from '@/types/menu';
import { sendMessageToNative } from '@/utils/native-bridge';
import { ChartPie, ChevronRight, NotebookPen, Trophy } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { iconMap } from '../home/<USER>/icon-map';

export default function MenuPage() {
  const [showHealthRecordDialog, setShowHealthRecordDialog] = useState(false);
  const [hidden, setHidden] = useState(true);
  const router = useRouter();
  const { goLogout } = useRoutes();
  const safeArea = useSafeArea();
  const { user } = useAuthStore();
  const { refreshRiskData } = useRiskData();
  const { setFromPage } = useGlobalStore();
  const { log: nlog } = useDevModeStore();
  const { buttonList, refreshButtons } = useDynamicMenu();
  const { handleQrScan } = useScanInapp();
  const { setDialog } = useMessageDialog();
  const { triggerLocationUpdate } = useGeolocation();
  // User info section
  const settings = {
    alt: '設定',
    icon: '/images/menu/Icon-menu-settings.png',
    href: 'menu/settings',
  }; // setting

  // Main action buttons
  const mainActions = [
    { name: 'スキャン', icon: '/images/menu/icon-menu-qrcode.png', href: '', isDisabled: true }, // /data-connect
    { name: '投稿', icon: '/images/menu/icon-menu-camera.png', href: '', isDisabled: true }, // /phone/camera
  ];

  // Feature buttons (first row)
  // const featureButtons1 = [
  //   { name: 'グラフ', icon: '/images/menu/icon-menu-graph.png', href: '/graph' },
  //   { name: '健康記録', icon: '/images/menu/icon-menu-record.png', href: '/health-record' },
  //   { name: 'ミッション', icon: '/images/menu/icon-menu-mission.png', href: '/mission' },
  // ];

  const featureButtons2 = {
    name: 'ヘルスチェックAI',
    icon: '/images/menu/icon-menu-score.png',
    href: '/health-score',
  };
  // const featureButtons1 = [
  //   { name: 'グラフ', icon: '/images/menu/icon-menu-graph.png', href: '/graph' },
  //   { name: '健康記録', icon: '/images/menu/icon-menu-record.png', href: '/health-record' },
  //   { name: 'ミッション', icon: '/images/menu/icon-menu-mission.png', href: '/mission' },
  //   { name: 'ランキング', icon: '/images/menu/icon-menu-ranking.png', href: '/ranking' }
  // ];

  // Feature buttons (second row)
  // const featureButtons2 = [
  //   { name: 'ウォーキングコース', icon: '/images/menu/icon-menu-walking.png', href: '/walking-course' },
  //   { name: 'イベント', icon: '/images/menu/icon-menu-event.png', href: '/event' },
  //   { name: 'クーポン', icon: '/images/menu/icon-menu-coupon.png', href: '/coupon' },
  //   { name: 'ヘルスチェックAI', icon: '/images/menu/icon-menu-score.png', href: '/health-score' }
  // ];

  // Feature buttons (third row)
  // const featureButtons3 = [
  //   { name: 'ランキング', icon: Trophy, href: '/ranking' },
  //   { name: '健康診断結果登録', icon: '/images/menu/icon-menu-health-check.png', href: '/health-checkup' },
  //   { name: '健診・検診日の記録', icon: '/images/menu/icon-menu-checkup-date-record.png', href: '/checkup-date-record' },
  //   { name: '服薬の記録', icon: '/images/menu/icon-menu-medicine-record.png', href: '' },
  //   { name: 'プロフィールの変更', icon: '/images/menu/icon-menu-profile.png', href: '' }
  // ];

  // External services
  const externalServices = [
    {
      name: 'Samsung Health',
      logo: '/images/samsung-health.png',
      href: '/phone/samsung-health',
    },
    {
      name: 'あすけん',
      logo: '/images/asuken.png',
      href: '/phone/asuken',
    },
    {
      name: '脳の健康チェック',
      logo: '/images/brain-health.png',
      href: '/phone/brain-health',
    },
    // Theme
    {
      name: 'テーマ',
      logo: '/images/theme.png',
      href: '/menu/theme-setting',
    },
    {
      name: '共通コンポーネント',
      logo: '/images/theme.png',
      href: '/shared-preview',
    },
  ];

  const handleBack = () => {
    sendMessageToNative({
      type: 'start-ai',
      data: { info: '1,2,4' },
      callback: (data) => {},
    });
  };
  const handleBackUser = () => {
    nlog('handleBackUser');
    const { user } = useAuthStore.getState();
    sendMessageToNative({
      type: 'start-other-link',
      data: {
        link: 'https://www.baidu.com',
      },

      callback: (data) => {},
    });
  };

  const logoutClick = () => {
    sendMessageToNative({
      type: 'logout',
      data: {},
    });
    goLogout();
  };

  const checkRiskScore = () => {
    refreshRiskData((riskState) => {
      if (riskState?.isRiskInited) {
        router.push(featureButtons2.href);
      } else {
        setFromPage('menu');
        router.push(ROUTES.RISK.USAGE);
      }
    });
  };

  // スキャンボタンのクリックハンドラー
  const handleScanClick = (action: any) => {
    // handleQrScan();
    // ここにスキャン機能のロジックを追加
    const hasHealtch = false;
    const titleStr = hasHealtch
      ? APP_TEXT.REGISTRATION_CREATE.CONNECT_FINISH_TITLE
      : APP_TEXT.REGISTRATION_CREATE.CONNECT_DISABLE_TITLE;

    const contextStr = hasHealtch ? '' : APP_TEXT.REGISTRATION_CREATE.CONNECT_DISABLE_CONTEXT;

    setDialog(true, {
      title: titleStr,
      content: (
        <div className="text-base font-normal">
          {/* <img src="/images/create/finish.svg" alt="complete" className="mb-2" />
           */}
          {contextStr}
        </div>
      ),
      outSideClickClose: true,
      footer: (
        <Button
          className="w-full rounded-full h-12"
          // type="button"
          onClick={() => {
            // sendHealthSetting(allPermissionArrary); // 传入具体设备类型
          }}
        >
          <div className="font-semibold text-base font-bold">{COMMON_TEXT.BUTTON.CLOSE}</div>
        </Button>
      ),
    });
  };
  // スキャンボタンのクリックハンドラー
  const handlePostClick = () => {
    router.push(ROUTES.POST.PHOTO_SELECT);
  };
  const [levelInfo, setLevelInfo] = useState<LevelInfoResponse>();

  useEffect(() => {
    menuAPI.levelInfo().then((res) => {
      if (res) {
        setLevelInfo(res);
      }
    });
    triggerLocationUpdate();
    refreshButtons();
  }, []);

  return (
    <div className="pb-16">
      {/* User profile section */}
      <div
        style={{ marginTop: safeArea.top }}
        className="flex items-center bg-white gap-4 pt-3 pl-6 pb-3 pr-6"
      >
        <div className="flex items-center flex-1 gap-2">
          <div className="h-16 w-16 flex-none overflow-hidden rounded-full">
            <div className="h-full w-full bg-primary-30 flex items-center justify-center">
              {/* Placeholder for user avatar */}
              <img
                src="/images/menu/icon-menu-avatar.png"
                alt=""
                className="w-16 h-16 text-gray-800"
              />
            </div>
          </div>
          <div className="flex-1">
            <h2 className="text-lg text-[#000000] font-bold">{levelInfo?.nickName}</h2>
            <p className="text-sm text-[#666666]">会員番号：{levelInfo?.userId}</p>
            <p className="flex gap-1">
              <span className="bg-primary-5 pt-1 pb-1 pl-2 pr-2 rounded-[12px] font-bold text-primary">
                {`Lv.${levelInfo?.level || ''}`}
              </span>
              <span className="text-sm content-center text-[#666666]">{levelInfo?.animalName}</span>
            </p>
          </div>
        </div>
        <div className="w-6 flex-none">
          <Link key={settings.alt} href={settings.href}>
            <img src={settings.icon} alt={settings.alt} className="w-6 h-6 text-gray-800" />
          </Link>
        </div>
      </div>

      {/* rectangle */}
      <div className="flex-1 bg-white h-8 rounded-b-[16px]" />

      {/* Main action buttons */}
      {process.env.NEXT_PUBLIC_APP_ENV === 'dev' ? (
        <div className="mx-6 mt-[-32px] mb-2 flex rounded-[16px] bg-primary pt-2 pl-6 pb-2 pr-6 text-primary-foreground">
          <>
            {mainActions.map((action, index) => (
              <Link
                key={action.name}
                href={action.href}
                className="flex flex-1 flex-col h-12 items-center justify-center"
                onClick={() => setShowHealthRecordDialog((prev) => !prev)}
              >
                <div className="mb-1 flex h-6 w-6 items-center justify-center">
                  {action.name === 'スキャン' && action.isDisabled === true && (
                    <button
                      type="button"
                      onClick={() => handleScanClick(action)}
                      onKeyDown={(e) => e.key === 'Enter' && handleScanClick(action)}
                      className="flex items-center justify-center w-6 h-6 cursor-pointer"
                      aria-label={`${action.name}をクリック`}
                    >
                      <img src={action.icon} alt={action.name} className="w-6 h-6 text-gray-800" />
                    </button>
                  )}
                  {action.name === '投稿' && (
                    <img src={action.icon} alt={action.name} className="w-6 h-6 text-gray-800" />
                  )}
                </div>
                <span className="text-xs">{action.name}</span>
              </Link>
            ))}
            <MenuHealthRecordCreate />
          </>
        </div>
      ) : (
        <></>
      )}

      {/* Feature buttons grid */}

      <div className="mx-6 pt-2">
        <div className="pb-2">
          <span className="mb-2 text-xl font-bold">メニュー</span>
        </div>
        <div className="bg-white p-4 rounded-[16px] grid grid-cols-4 gap-y-3 justify-center">
          {buttonList.map((button) => {
            const iconNode = iconMap[button.iconKey];
            return (
              <Link
                key={button.id}
                onClick={(e) => {
                  e.preventDefault();
                  if (button.callback) {
                    button.callback();
                  } else if (button.url) {
                    router.push(button.url);
                  }
                }}
                href={''}
                className="flex flex-col items-center gap-1"
              >
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-[#F6F8FF]">
                  {iconNode}
                </div>
                <span className="text-center text-[11px] h-7 w-[72px] font-bold leading-[1.3]">
                  {button.label}
                </span>
              </Link>
            );
          })}
          {/* First row */}
          {/* {featureButtons1.map((button) => (
            <Link
              key={button.name}
              href={button.name === 'ヘルスチェックAI' ? '' : button.href}
              className="flex flex-col items-center"
            >
              <div className="mb-1 flex h-9 w-9 items-center justify-center rounded-full bg-[#F6F8FF]">
                {button.name === 'グラフ' && (
                  <img src={`${button.icon}`} alt={button.name} className="w-6 h-6 text-gray-800" />
                )}
                {button.name === '健康記録' && (
                  <img src={`${button.icon}`} alt={button.name} className="w-6 h-6 text-gray-800" />
                )}
                {button.name === 'ミッション' && (
                  <img src={`${button.icon}`} alt={button.name} className="w-6 h-6 text-gray-800" />
                )}
              </div>
              <span className="text-xs text-center font-bold">{button.name}</span>
            </Link>
          ))} */}

          {/* <Link
            key={featureButtons2.name}
            onClick={(e) => {
              e.preventDefault();
              checkRiskScore();
            }}
            href={''}
            className="flex flex-col items-center"
          >
            <div className="mb-1 flex h-9 w-9 items-center justify-center rounded-full bg-[#F6F8FF]">
              <img
                src={`${featureButtons2.icon}`}
                alt={featureButtons2.name}
                className="w-6 h-6 text-gray-800"
              />
            </div>
            <span className="text-xs text-center font-bold">{featureButtons2.name}</span>
          </Link> */}

          {/* Second row */}
          {/* {featureButtons2.map((button) => (
            <Link key={button.name} href={button.href} className="flex flex-col items-center">
              <div className="mb-1 flex h-9 w-9 items-center justify-center rounded-full bg-[#F6F8FF]">
                {button.name === 'ウォーキングコース' && (
                  <img
                    src={`${button.icon}`}
                    alt={button.name}
                    className="w-6 h-6 text-gray-800"
                  />
                )}
                {button.name === 'イベント' && (
                  <img
                    src={`${button.icon}`}
                    alt={button.name}
                    className="w-6 h-6 text-gray-800"
                  />
                )}
                {button.name === 'クーポン' && (
                  <img
                    src={`${button.icon}`}
                    alt={button.name}
                    className="w-6 h-6 text-gray-800"
                  />
                )}
                {button.name === 'ヘルスチェックAI' && (
                  <img
                    src={`${button.icon}`}
                    alt={button.name}
                    className="w-6 h-6 text-gray-800"
                  />
                )}
              </div>
              <span className="text-xs text-center font-bold">{button.name}</span>
            </Link>
          ))} */}

          {/* Third row */}
          {/* {featureButtons3.map((button) => (
            <Link key={button.name} href={button.href} className="flex flex-col items-center">
              <div className="mb-1 flex h-9 w-9 items-center justify-center rounded-full bg-[#F6F8FF]">
                 <img src={`${button.icon}`} alt={button.name} className="w-6 h-6 text-gray-800" />
              </div>
              <span className="text-xs text-center font-bold">{button.name}</span>
            </Link>
          ))} */}
        </div>
      </div>
      {/* External services section */}
      <div className="mx-4 mb-4 hidden">
        <h3 className="mb-2 text-sm font-medium">外部サービス</h3>
        <div className="grid grid-cols-2 gap-4">
          {externalServices.map((service, index) => (
            <Link
              key={service.name}
              href={service.href}
              className={`flex items-center gap-3 rounded-xl bg-card p-3 shadow-sm ${
                index === 2 ? 'col-span-2' : ''
              }`}
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-full">
                {service.name === 'Samsung Health' && (
                  <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary-foreground"
                    >
                      <path d="M3 7V5c0-1.1.9-2 2-2h2" />
                      <path d="M17 3h2c1.1 0 2 .9 2 2v2" />
                      <path d="M21 17v2c0 1.1-.9 2-2 2h-2" />
                      <path d="M7 21H5c-1.1 0-2-.9-2-2v-2" />
                      <path d="M8 14s1.5 2 4 2 4-2 4-2" />
                      <path d="M9 9h.01" />
                      <path d="M15 9h.01" />
                    </svg>
                  </div>
                )}
                {service.name === 'あすけん' && (
                  <div className="h-10 w-10 flexflex bg-accent flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-accent-foreground"
                    >
                      <path d="M12 2a8 8 0 0 0-8 8c0 5.4 3.4 10 8 10s8-4.6 8-10a8 8 0 0 0-8-8zm0 12a4 4 0 1 1 0-8 4 4 0 0 1 0 8z" />
                      <path d="M12 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
                    </svg>
                  </div>
                )}
                {service.name === '脳の健康チェック' && (
                  <div className="h-10 w-10 rounded-full bg-card flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-muted-foreground"
                    >
                      <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4" />
                      <path d="M3 15h14a2 2 0 1 1 0 4H3" />
                      <path d="M11 3v8h8" />
                      <path d="M11 21v-8h8" />
                    </svg>
                  </div>
                )}
              </div>
              <span className="text-sm">{service.name}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Settings section */}
      <div className="mx-4 mb-4 hidden">
        <h3 className="mb-2 text-sm font-medium">設定</h3>
        <button
          type="button"
          className="bg-card w-full rounded-2xl px-6 flex items-center h-[72px] mb-6"
          onClick={() => logoutClick()}
        >
          <div className="flex-1 text-base text-left">ログアウト</div>
          <ChevronRight className="w-5 h-5" />
        </button>
      </div>
      <div className="mx-4 mb-4 hidden">
        <h3 className="mb-2 text-sm font-medium">テストツール</h3>
        <button
          type="button"
          className="bg-card w-full rounded-2xl px-6 flex items-center h-[72px] mb-6"
          onClick={() => handleBack()}
        >
          <div className="flex-1 text-base text-left">test</div>
          <ChevronRight className="w-5 h-5" />
        </button>
        <button
          type="button"
          className="bg-card w-full rounded-2xl px-6 flex items-center h-[72px] mb-6"
          onClick={() => handleBackUser()}
        >
          <div className="flex-1 text-base text-left">test send user info</div>
          <ChevronRight className="w-5 h-5" />
        </button>
        <button
          type="button"
          className="bg-card w-full rounded-2xl px-6 flex items-center h-[72px] mb-6"
          onClick={() => router.push('/test-page/set-user')}
        >
          <div className="flex-1 text-base text-left">set user info</div>
          <ChevronRight className="w-5 h-5" />
        </button>
        <button
          type="button"
          className="bg-card w-full rounded-2xl px-6 flex items-center h-[72px] mb-6"
          onClick={() => router.push('/test-page/resize')}
        >
          <div className="flex-1 text-base text-left">resize test</div>
          <ChevronRight className="w-5 h-5" />
        </button>
        <VersionTag />
      </div>
    </div>
  );
}
