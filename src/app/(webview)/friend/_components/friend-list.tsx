import { Button } from '@/components/shared/button';
import RankIcon from '@/components/shared/rank-icon';
import { TextButton } from '@/components/shared/text-button';
import YellIcon from '@/components/shared/yell-icon';
import { getLevelIconPath } from '@/const/gamification';
import { cn } from '@/lib/utils';
import type { User } from '@/store/auth';
import type { FriendRanking } from '@/types/friend';
import { Check, Plus } from 'lucide-react';
import { RANK_COLOR } from '../_const/index';
import TextTrim from './text-trim';

function formatNumber(number: number | undefined) {
  if (!number) return '0';
  return number.toLocaleString();
}

export default function FriendList({
  friendCount,
  data,
  user,
  onYellClick,
  onShowMore,
}: {
  friendCount: number;
  data: FriendRanking[];
  user: User | null;
  onYellClick: (id: number, hasSendYell: boolean) => void;
  onShowMore: () => void;
}) {
  const sendYell = (id: number, hasSendYell: boolean) => {
    onYellClick(id, hasSendYell);
  };
  const handleShowMore = () => {
    onShowMore();
  };
  return (
    <>
      <div className="flex mt-4 items-center">
        <div className="text-lg font-bold">フレンドリスト</div>
        <div className="text-sm text-text-secondary">(歩数ランキング順)</div>
      </div>
      <div className="bg-card rounded-2xl mt-2">
        {data.map((item, index) => (
          <div
            className="flex items-center px-3 border-gray-200 border-b last:border-none"
            key={item.friendUserId}
          >
            <div className="w-6 h-6 relative flex items-center justify-center">
              <RankIcon
                className={cn('absolute top-0 left-[-2px] w-7 h-6', index > 2 && 'hidden')}
                fill={
                  index === 0
                    ? RANK_COLOR.GOLD
                    : index === 1
                      ? RANK_COLOR.SILVER
                      : RANK_COLOR.BRONZE
                }
              />
              <span className="text-sm">{index + 1}</span>
            </div>
            <div className="flex items-center flex-1">
              <div className="relative ml-2 h-[54px] px-1">
                <div
                  className={cn(
                    'absolute top-[-20px] left-[-39px] w-[86px] h-[40px] flex items-center justify-center',
                    !item.hasReceivedYell && 'hidden',
                  )}
                >
                  <img
                    src="/images/friend/yell-message.png"
                    alt="がんばってね!"
                    width={86}
                    height={40}
                  />
                </div>
                <div className="w-10 h-10 rounded-full flex items-center justify-center bg-primary-30">
                  {item.iconUrl && <img src={item.iconUrl} alt="level" width={40} height={40} />}
                  {!item.iconUrl && (
                    <img src={getLevelIconPath(item.level)} alt="level" width={40} height={40} />
                  )}
                </div>
                <div className="absolute box-border bg-card flex items-center justify-center bottom-0 text-primary left-0 right-0 h-[18px] text-xs border-primary-50 border-2 rounded-[4px] font-bold">
                  LV.{String(item.level || 1)}
                </div>
              </div>
              <div className="ml-2 py-3">
                <div className="flex items-center">
                  <span className="text-sm font-bold">
                    <TextTrim text={item.nickname} maxLength={10} allowLength={3} />
                  </span>
                  {user?.id === String(item.friendUserId) && (
                    <span className="ml-1 h-5 w-8 rounded-sm text-xs text-primary bg-primary/10 flex items-center justify-center">
                      自分
                    </span>
                  )}
                </div>
                <div className="text-sm">
                  {formatNumber(item.stepCount)}
                  <span className="ml-[2px]">歩</span>
                </div>
                {user?.id !== String(item.friendUserId) && (
                  <div className="text-xs text-text-secondary">最終起動日：{item.lastUsedDate}</div>
                )}
              </div>
            </div>
            {user?.id !== String(item.friendUserId) &&
              (item.hasSentYell ? (
                <Button
                  size="xs"
                  variant="outline"
                  className="ml-2"
                  onClick={() => {
                    sendYell(item.friendUserId, true);
                  }}
                >
                  エール
                  <Check className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  size="xs"
                  className="ml-2"
                  onClick={() => {
                    sendYell(item.friendUserId, false);
                  }}
                >
                  エール
                  <YellIcon className="w-4 h-4" />
                </Button>
              ))}
          </div>
        ))}
        {friendCount > data.length && (
          <div className="flex items-center pb-3">
            <TextButton
              className="w-full mt-3"
              onClick={() => {
                handleShowMore();
              }}
            >
              もっと見る
              <Plus className="w-5 h-5" />
            </TextButton>
          </div>
        )}
      </div>
    </>
  );
}
