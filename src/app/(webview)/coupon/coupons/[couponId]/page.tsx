'use client';

import { couponAPI } from '@/api/modules/coupon';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import useCenterToast from '@/hooks/use-center-toast';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useNavigation } from '@/hooks/use-navigation';
import { useParams, useRouter } from '@/hooks/use-next-navigation';
import type { couponInfo } from '@/types/coupon-types';
import { ChevronRight, Star } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { StampSpotProvider } from '../../shops/[shopId]/_context/use-stamp-spot-state';

function CouponCard({ params }: { params: { couponId: string } }) {
  const couponId = params.couponId;
  const router = useRouter();
  const { setDialog } = useMessageDialog();
  const [couponDetail, setCouponDetail] = useState<couponInfo>();
  const { setLoading } = useLoading();

  // fetchCouponを定義し、依存配列を追加
  const fetchCoupon = useCallback(async (): Promise<void> => {
    try {
      const res = await couponAPI.couponDetail(couponId);
      if (res?.couponInfo) {
        setCouponDetail(res.couponInfo);
      } else {
        router.push('/coupon');
      }
    } catch (e) {
      console.log('Failed to fetch coupon data', e);
    }
    // ここには依存する変数や状態がないため、空の配列を使用
  }, [couponId]);

  useEffect(() => {
    if (couponId) {
      setLoading(true);
      fetchCoupon();
      setLoading(false);
    }
  }, [couponId, fetchCoupon]);

  // 確認ダイアログ
  const handleDialogOpen = (open: boolean) => {
    const { toast } = useCenterToast();
    setDialog(open, {
      content: APP_TEXT.COUPON.MESSAGE_USE_COUPON,
      outSideClickClose: true,
      footer: (
        <div className="flex flex-col">
          <Button
            className="w-full bg-primary text-card"
            // variant="destructive"
            onClick={() => {
              handleDialogOpen(false);
              handleConfirm();
              toast({
                message: (
                  <>
                    <span>クーポンを</span>
                    <br />
                    <span>使用しました</span>
                  </>
                ),
                duration: 3000,
              });
            }}
          >
            {APP_TEXT.COUPON.BUTTON_USE}
          </Button>
          <TextButton className="w-full mt-4" variant="muted" onClick={() => setDialog(false)}>
            {COMMON_TEXT.BUTTON.CANCEL}
          </TextButton>
        </div>
      ),
    });
  };

  // handleConfirmでfetchCouponを呼び出し、データを更新
  const handleConfirm = () => {
    couponAPI.useCoupon(couponId);
    setTimeout(() => {
      fetchCoupon(); // ここで呼び出し、データを再取得
    }, 2000);
  };

  const handleSearchNearbyCoupons = () => {
    router.push('/coupon/shops');
  };

  const formatEndDate = (dateStr?: string): string => {
    if (!dateStr) return '-';

    const date = new Date(dateStr);
    if (Number.isNaN(date.getTime())) return '-'; // 日付が無効

    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 月は0から始まる
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();

    // 時間が含まれているかどうかを判定（:）
    if (dateStr.includes(':')) {
      return `${year}年${month}月${day}日 ${padZero(hours)}:${padZero(minutes)}`;
    }
    return `${year}年${month}月${day}日`;
  };

  // 分または時間が2桁未満の場合は0を追加
  const padZero = (num: number) => (num < 10 ? `0${num}` : `${num}`);

  const handleBackClick = () => {
    router.back();
  };

  const handleCloseClick = () => {
    router.back();
  };

  return (
    <>
      <TopBar
        title={APP_TEXT.COUPON.COUPON_TITLE}
        enableBack={false}
        enableClose={true}
        onBack={handleBackClick}
        onClose={handleCloseClick}
      />
      <div className="mx-auto shadow-md overflow-hidden border border-gray-200 bg-white">
        {couponDetail &&
          couponDetail.remainingUsageCount !== undefined &&
          couponDetail.remainingUsageCount === 0 && (
            <div className="bg-gray-5 text-center font-bold text-muted-foreground py-2">
              {APP_TEXT.COUPON.COUPON_USED}
            </div>
          )}
        {couponDetail &&
          couponDetail.remainingUsageCount !== undefined &&
          couponDetail.remainingUsageCount > 0 && (
            <div className="bg-primary-5 text-center font-bold text-primary py-2">
              {APP_TEXT.COUPON.SHOW_TO_SHOP}
            </div>
          )}
        <div className="mx-6 mt-6 rounded-3xl bg-white overflow-hidden shadow-lg shadow-gray-300">
          {/* 上部のドリンク画像 */}
          <div className="bg-orange-50 h-[215px] overflow-hidden rounded-t-3xl">
            <img
              src={couponDetail?.imagePath ?? '/images/coupon/default-coupon.svg'}
              alt={APP_TEXT.COUPON.DRINK_IMAGE}
              width={200}
              height={180}
              className="w-full h-full object-cover"
              onError={(e) => {
                // 画像の読み込みに失敗した場合の処理
                e.currentTarget.src = '/images/coupon/default-coupon.svg';
              }}
            />
          </div>

          {/* タイトルと有効期限 */}
          <div className="mx-2 p-4 space-y-2">
            <h2 className="font-bold text-2xl line-clamp-5">{couponDetail?.couponName ?? '-'}</h2>
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-500">
                {APP_TEXT.COUPON.EXPIRATION_DATE + formatEndDate(couponDetail?.endDate)}
              </p>
              <div className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">
                <Star className="w-4 h-4 text-gray-600" />
              </div>
            </div>
          </div>

          {/* 条件情報 */}
          <div className="mx-6 rounded-3xl bg-secondary overflow-hidden h-[136px]">
            <div className="px-5 py-3 space-y-3 text-sm">
              <div>
                <p className="font-bold mb-1">{APP_TEXT.COUPON.CONDITIONS_USE}</p>
                <p>{couponDetail?.conditions ?? '-'}</p>
              </div>
              <div>
                <p className="font-bold mb-1 mt-1">{APP_TEXT.COUPON.TIMES_CAN_USE}</p>
                <p>
                  {APP_TEXT.COUPON.TIMES_PER_PERSON(
                    couponDetail?.usageLimitPerUser?.toString() ?? '-',
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* 店舗情報 */}
          <div className="flex items-center px-4 py-2 gap-2 text-sm ml-2">
            <img
              src={couponDetail?.shopIconPath ?? '/images/coupon/default-shop.svg'}
              alt={APP_TEXT.COUPON.COUPON_IMAGE}
              width={48}
              height={48}
              className="object-cover"
              onError={(e) => {
                // 画像の読み込みに失敗した場合の処理
                e.currentTarget.src = '/images/coupon/default-shop.svg';
              }}
            />
            <span className="font-bold text-lg flex-grow line-clamp-2">
              {couponDetail?.shopName ?? ''}
            </span>
            {/* 右側の遷移ボタン */}
            <a
              href={`/coupons/shops/${couponDetail?.shopId}`}
              className="ml-auto flex items-center py-1 rounded cursor-pointer"
            >
              <ChevronRight className="mr-1" />
            </a>
          </div>

          {/* 点線 */}
          <div className="border-t mt-2 border-dashed border-gray-400 mb-4" />
          {/* 残り回数 */}
          {couponDetail &&
            couponDetail.usageLimitPerUser > 1 &&
            couponDetail.remainingUsageCount !== undefined && (
              <div className="text-center font-bold text-muted-foreground py-2">
                {APP_TEXT.COUPON.REMAINING}
                <span className="text-primary">{couponDetail?.remainingUsageCount}</span>
                {APP_TEXT.COUPON.TIMES}
              </div>
            )}
          {/* 使用ボタン */}
          <div className="left-0 right-0 z-50">
            <div className="mx-6">
              {couponDetail?.remainingUsageCount === 0 ? (
                <Button
                  className="w-full py-2 rounded-full bg-primary text-card font-medium"
                  disabled
                >
                  {APP_TEXT.COUPON.COUPON_USED_CHECKED}
                </Button>
              ) : (
                <Button
                  onClick={() => handleDialogOpen(true)}
                  className="w-full py-2 rounded-full bg-primary text-card font-medium"
                >
                  {APP_TEXT.COUPON.USE_COUPON}
                </Button>
              )}
            </div>
          </div>
          {/* 人数 */}
          <div className="text-center text-sm text-muted-foreground py-2 mb-4">
            <span className="text-primary font-bold">{couponDetail?.userCount || 0}</span>
            {APP_TEXT.COUPON.COUPON_USED_PEOPLE}
          </div>
        </div>
        <div className="pb-[80px]">
          <h2 className="px-6 font-bold text-lg mt-6 mb-2">{APP_TEXT.COUPON.MESSAGE_FROM_SHOP}</h2>
          <p className="px-6 text-base">{APP_TEXT.COUPON.MESSAGE_SHOW_TO_STAFF}</p>
          <h3 className="px-6 font-bold text-lg mt-6 mb-2">{APP_TEXT.COUPON.NOTES}</h3>
          <ul className="px-7 text-sm">
            <li className="flex">
              <div className="w-4 mr-2 text-black font-bold">•</div>
              <div>
                <p>{APP_TEXT.COUPON.MESSAGE_USE_ONLY_ONE}</p>
              </div>
            </li>
            <li className="flex">
              <div className="w-4 mr-2 text-black font-bold">•</div>
              <div>
                <p>{APP_TEXT.COUPON.MESSAGE_CONFIRM}</p>
              </div>
            </li>
            <li className="flex">
              <div className="w-4 mr-2 text-black font-bold">•</div>
              <div>
                <p>{APP_TEXT.COUPON.MESSAGE_MAYBE_END}</p>
              </div>
            </li>
          </ul>
        </div>
        <div style={{ height: '80px' }} />
        {couponDetail?.remainingUsageCount != null && couponDetail?.remainingUsageCount === 0 && (
          <div
            className="fixed bottom-0 left-0 right-0 bg-white py-2 pb-[34px]"
            style={{ zIndex: 51 }}
          >
            <div className="mx-6">
              {/* mx-6 = margin-left/right: 24px */}
              <Button
                onClick={handleSearchNearbyCoupons}
                className="w-full py-2 rounded-full bg-primary text-card font-medium"
              >
                {APP_TEXT.COUPON.CHECK_OTHER_AREA}
              </Button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

export default function CouponDetailPage({ params }: { params: { couponId: string } }) {
  return <CouponCard params={params} />;
}
