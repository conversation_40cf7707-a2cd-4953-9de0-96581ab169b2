import { Card } from '@/components/ui/card';
import type { CouponListItem } from '@/types/coupon-types';
import { formatDate } from '@/utils/date-format';
export default function RecommendationsCouponCard({
  coupon,
  className,
  onClick,
}: {
  coupon: CouponListItem;
  className?: string;
  onClick: (couponId: string) => void;
}) {
  return (
    <Card
      className={`relative p-0 w-[124px] shadow-card-base inset-shadow-1xs hover:shadow-card-hover cursor-pointer ${className}`}
      onClick={() => onClick(coupon.couponId)}
    >
      <img
        className="w-[24px] h-[24px] absolute float top-2 right-2 object-cover rounded-2xl"
        src={coupon.shopIconPath ?? '/images/coupon/default-shop.svg'}
        alt={coupon.shopName}
        onError={(e) => {
          // 画像の読み込みに失敗した場合の処理
          e.currentTarget.src = '/images/coupon/default-shop.svg';
        }}
      />

      <img
        className="w-[124px] h-[80px] object-cover rounded-lg"
        src={coupon.imagePath ?? '/images/coupon/default-coupon.svg'}
        alt={coupon.couponName}
        onError={(e) => {
          // 画像の読み込みに失敗した場合の処理
          e.currentTarget.src = '/images/coupon/default-coupon.svg';
        }}
      />

      {/* 活動信息 */}
      <div className="f p-2">
        <p className="text-sm font-bold mb-1 line-clamp-2 h-10 ">{coupon.couponName}</p>

        <div className=" text-muted-foreground space-y-0.5">
          <p className="line-clamp-2 text-xs">{formatDate(coupon.endDate, 'yyyy/M/d')} ~ </p>
          <p className="line-clamp-1 text-xs">{coupon.shopName}</p>
        </div>
      </div>
    </Card>
  );
}
