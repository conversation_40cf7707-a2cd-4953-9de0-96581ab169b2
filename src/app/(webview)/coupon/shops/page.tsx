'use client';

import { couponAPI } from '@/api/modules/coupon';
import MapComponent from '@/components/shared/map/map';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useMap } from '@/hooks/use-map';
import { useRouter } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import type { SearchCouponRequest, SearchCouponResponse } from '@/types/coupon-types';
import { type Connection, type Marker, MarkerType } from '@/types/map';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { StampSpotProvider, useStampSpotState } from './[shopId]/_context/use-stamp-spot-state';
import ShopsNearbyDrawer from './_components/shops-nearby-drawer';

function ShopsNearbyContent() {
  const { location } = useGeolocation();
  const router = useRouter();
  const { mapRef } = useMap();
  const params: SearchCouponRequest = useMemo(() => {
    return {
      latitude: location.lat.toFixed(6) || '',
      longitude: location.lng.toFixed(6) || '',
      sortType: 'distance',
      filters: {
        distance: 5000,
        categories: [],
      },
    };
  }, []);
  const {
    data: couponsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['searchCoupon', params],
    queryFn: () => couponAPI.searchCoupon(params),
    enabled: !!params,
  });

  const { showShopDetail, setShowShopDetail } = useStampSpotState();

  const handleClickMarker = (marker: Marker) => {
    console.log(marker);
    router.push(`/coupon/shops/${marker.id}`);
  };

  useEffect(() => {
    if (showShopDetail) {
      document.body.style.overflow = 'hidden';
      document.body.style.height = '100vh';
      // clear vaul pointer events
      setTimeout(() => {
        document.body.style.pointerEvents = '';
      }, 200);

      console.log('mapRef', mapRef.current);
      if (mapRef.current) {
        mapRef.current.panBy(0, 0);
      }
    } else {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
      if (mapRef.current) {
        mapRef.current.panBy(0, 0);
      }
    }
    return () => {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
    };
  }, [showShopDetail, mapRef.current]);

  const userLocationMarker = useMemo<Marker>(() => {
    return {
      id: 'user-location',
      name: '自分の位置',
      type: MarkerType.USER_LOCATION,
      latitude: location.lat.toString(),
      longitude: location.lng.toString(),
    };
  }, [location]);

  const markers = useMemo(() => {
    const list: Marker[] =
      couponsData?.shopList?.map((shop) => ({
        id: shop.shopId,
        name: shop.shopName,
        type: MarkerType.COUPON,
        address: shop.address,
        latitude: shop.latitude,
        longitude: shop.longitude,
        imagePath: shop.shopIconPath,
      })) || [];
    return list;
  }, [couponsData]);

  const lines = useMemo(() => {
    const list: Connection[] | undefined = [];
    return list || [];
  }, [couponsData]);

  return (
    <div className="relative h-[100vh]">
      <div
        className={cn(
          'transition-all duration-300 h-full w-full',
          showShopDetail ? 'translate-y-[-25%]' : 'translate-y-0',
        )}
      >
        <MapComponent
          markers={markers}
          lines={lines}
          userLocationMarker={userLocationMarker}
          onMarkerClick={(marker: Marker) => {
            handleClickMarker(marker);
          }}
        />
      </div>
      <ShopsNearbyDrawer
        show={showShopDetail}
        data={couponsData}
        onLeftIconClick={() => router.back()}
        onShopClick={(shopId: string) => {
          router.push(`/coupon/shops/${shopId}`);
        }}
        onCouponClick={(couponId: string) => {
          router.push(`/coupon/coupons/${couponId}`);
        }}
      />
    </div>
  );
}

export default function ShopsNearbyPage() {
  return (
    <StampSpotProvider>
      <ShopsNearbyContent />
    </StampSpotProvider>
  );
}
