'use client';

import { couponAPI } from '@/api/modules/coupon';
import MapComponent from '@/components/shared/map/map';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useMap } from '@/hooks/use-map';
import { useRouter } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import type { SearchCouponRequest } from '@/types/coupon-types';
import { type Connection, type Marker, MarkerType } from '@/types/map';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import ShopDetailDrawer from './_components/shop-detail-drawer';
import { StampSpotProvider, useStampSpotState } from './_context/use-stamp-spot-state';

function ShopDetailContent({ id }: { id: string }) {
  const { mapRef } = useMap();
  const { location } = useGeolocation();
  const router = useRouter();
  const {
    data: shopData,
    isLoading,
    isSuccess,
    error,
  } = useQuery({
    queryKey: ['shopDetail', id],
    queryFn: () => couponAPI.shopDetail(id),
    enabled: !!id,
  });

  useEffect(() => {
    if (isSuccess && shopData.shopInfo === undefined) {
      router.back();
    }
  }, [isSuccess]);

  const { showShopDetail, setShowShopDetail } = useStampSpotState();

  useEffect(() => {
    if (showShopDetail) {
      document.body.style.overflow = 'hidden';
      document.body.style.height = '100vh';
      // clear vaul pointer events
      setTimeout(() => {
        document.body.style.pointerEvents = '';
      }, 200);

      console.log('mapRef', mapRef.current);
      if (mapRef.current) {
        mapRef.current.panBy(0, 0);
      }
    } else {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
      if (mapRef.current) {
        mapRef.current.panBy(0, 0);
      }
    }
    return () => {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
    };
  }, [showShopDetail, mapRef.current]);

  const userLocationMarker = useMemo<Marker>(() => {
    return {
      id: 'user-location',
      name: '自分の位置',
      type: MarkerType.USER_LOCATION,
      latitude: location.lat.toString(),
      longitude: location.lng.toString(),
    };
  }, [location]);

  const [markers, setMarkers] = useState<Marker[]>([]);

  const handleClickMarker = (marker: Marker) => {
    console.log(marker);
    if (marker.id.toString() === id) {
      setShowShopDetail(true);
      return;
    }
    router.push(`/coupon/shops/${marker.id}`);
  };

  useEffect(() => {
    const fetchMarkers = async () => {
      if (!shopData) return;
      const params: SearchCouponRequest = {
        sortType: 'distance',
        latitude: shopData.shopInfo.latitude,
        longitude: shopData.shopInfo.longitude,
        filters: {
          distance: 5000,
          categories: [],
        },
      };

      try {
        const resp = await couponAPI.searchCoupon(params);
        const list: Marker[] =
          resp?.shopList?.map((shop) => ({
            id: shop.shopId,
            name: shop.shopName,
            type: MarkerType.COUPON,
            address: shop.address,
            latitude: shop.latitude,
            longitude: shop.longitude,
            imagePath: shop.shopIconPath,
            isCurrent: shop.shopId === id,
          })) || [];

        setMarkers(list);
      } catch (err) {
        console.log('Failed to fetch markers', err);
        setMarkers([]);
      }
    };
    fetchMarkers();
  }, [shopData]);

  return (
    <div className="relative h-[100vh]">
      <div
        className={cn(
          'transition-all duration-300 h-full w-full',
          showShopDetail ? 'translate-y-[-25%]' : 'translate-y-0',
        )}
      >
        <MapComponent
          markers={markers}
          userLocationMarker={userLocationMarker}
          onMarkerClick={(marker: Marker) => {
            handleClickMarker(marker);
          }}
        />
      </div>
      <ShopDetailDrawer
        show={showShopDetail}
        data={shopData?.shopInfo}
        onLeftIconClick={() => router.back()}
      />
    </div>
  );
}

export default function ShopDetailPage({ params }: { params: { shopId: string } }) {
  return (
    <StampSpotProvider>
      <ShopDetailContent id={params.shopId} />
    </StampSpotProvider>
  );
}
