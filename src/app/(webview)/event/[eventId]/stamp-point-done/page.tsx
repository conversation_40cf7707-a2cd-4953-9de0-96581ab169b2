'use client';

import { eventAPI } from '@/api/modules/event-api';

import { EventStampCard } from '@/app/(webview)/event/_components/event-stamp-card';
import { Button } from '@/components/shared/button';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useGlobalVar } from '@/hooks/use-global-var';
import { useLoading } from '@/hooks/use-loading';
import { AnimationStampComplete } from '@/hooks/use-lottie-animation';
import { useParams, useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { useEventStore } from '@/store/event-store';
import type { EventDetailResponse, Stamp } from '@/types/event-types';
import { useQuery } from '@tanstack/react-query';
import { X } from 'lucide-react';
import { useEffect, useState } from 'react';
export default function StampPointDone() {
  const router = useRouter();
  const { eventRefreshTimeStamp } = useEventStore();
  const safeArea = useSafeArea();
  const safeTop = safeArea.top ?? 0;
  const { bottom } = useSafeArea();
  const [isComplete, setIsComplete] = useState(true);
  const [topBarHeight, setTopBarHeight] = useState(48);
  const { setFooterMenuSettingOptions } = useGlobalVar();
  const { eventId } = useParams();
  const { location } = useGeolocation();
  const latitude = location?.lat.toFixed(6) || '35.681236';
  const longitude = location?.lng.toFixed(6) || '139.767125';

  useEffect(() => {
    setFooterMenuSettingOptions({ isShow: false });
    return () => {
      setFooterMenuSettingOptions({ isShow: true });
    };
  }, []);
  const { data: eventDetailData } = useQuery<EventDetailResponse>({
    queryKey: ['stampPointDone', eventId, eventRefreshTimeStamp],
    queryFn: () => eventAPI.getEventDetail(eventId as string, latitude, longitude),
    enabled: !!eventId,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false,
  });

  // 異なるpointPatternに基づいてisCompleteを判定
  useEffect(() => {
    if (!eventDetailData?.eventInfo || !eventDetailData?.stampList) {
      return;
    }

    const pointPattern = eventDetailData.eventInfo.pointPattern;
    const pointPatternDetails = eventDetailData.eventInfo.pointPatternDetails;
    const stampList = eventDetailData.stampList;

    let completed = false;

    // 均一モード：stampList.length + 1 個のスタンプを表示、前stampList.length個は押印済み、最後の1個は空のスタンプなので、常にfalse
    if (pointPattern === '1') {
      completed = false;
    }
    // 一括モードと段階モード：スタンプの長さと総回数を判定
    else if (
      (pointPattern === '2' || pointPattern === '3') &&
      pointPatternDetails &&
      'totalTimes' in pointPatternDetails
    ) {
      completed = pointPatternDetails.totalTimes === stampList.length;
    }

    setIsComplete(completed);
    console.log('isComplete', isComplete);
  }, [eventDetailData]);

  useEffect(() => {
    if (isComplete) {
      setTopBarHeight(safeTop);
    } else {
      setTopBarHeight(safeTop + 48);
    }
  }, [safeTop, isComplete]);

  const handleClose = () => {
    router.back();
  };

  const handleOtherEvents = () => {
    router.push(`/event/${eventId}/other-events`);
  };

  const getLatestPoint = (stampList: Stamp[]) => {
    if (!stampList) return 0;
    // "isParticipated"が"1"の最後のStampのpointsを取得
    const latestStamp = stampList.filter((stamp) => stamp.isParticipated === '1').pop();
    return latestStamp?.points || 0;
  };

  return (
    <div className="bg-card">
      <div style={{ height: topBarHeight }} className="bg-card" />
      <div className={cn('fixed top-0 right-0 z-50 w-[48px]')}>
        <div className="flex h-[48px] items-center relative">
          <button
            type="button"
            onClick={handleClose}
            aria-label="close"
            className="absolute right-5"
          >
            <X size={24} />
          </button>
        </div>
      </div>
      {isComplete ? (
        <StampPointHeaderWithComplete
          title={eventDetailData?.eventInfo.eventName || ''}
          point={getLatestPoint(eventDetailData?.stampList || [])}
          organizerName={eventDetailData?.eventInfo.organizerName || ''}
        />
      ) : (
        <StampPointHeader
          title={eventDetailData?.eventInfo.eventName || ''}
          point={getLatestPoint(eventDetailData?.stampList || [])}
          organizerName={eventDetailData?.eventInfo.organizerName || ''}
        />
      )}

      <div className="mt-8">
        <EventStampCard
          stampList={eventDetailData?.stampList}
          eventInfo={eventDetailData?.eventInfo}
        />
      </div>

      <div
        className="fixed bottom-0 left-0 right-0 px-4 bg-card"
        style={{ paddingBottom: bottom + 80 }}
      >
        <Button variant="outline" className="mt-6 w-full" onClick={handleOtherEvents}>
          周辺の他のイベントを見る
        </Button>
      </div>
    </div>
  );
}

function StampPointHeader({
  title,
  point,
  organizerName,
}: {
  title: string;
  point: number;
  organizerName: string;
}) {
  return (
    <div className="text-center text-xl font-bold">
      <img
        src="/images/walking-course/stamp-point-done-icon.png"
        alt="スタンプを獲得！"
        className="w-16 h-16 mx-auto"
      />
      <h1 className="text-[22px] font-bold mt-1">{title}</h1>
      {organizerName && (
        <div
          className={
            'mt-8  mx-auto w-[320px]  rounded-2xl text-sm bg-primary-5 p-4 flex items-center gap-2'
          }
        >
          <span>{organizerName}</span>
          <span className="flex-1" />
          <span className="font-bold text-primary text-lg">{point}</span>P
        </div>
      )}
    </div>
  );
}

function StampPointHeaderWithComplete({
  title,
  point,
  organizerName,
}: {
  title: string;
  point: number;
  organizerName: string;
}) {
  return (
    <div className="relative h-[296px]">
      <div className="absolute flex justify-center items-center">
        <img
          src="/images/walking-course/stamp-complete-bg.png"
          alt="stamp-complete"
          className="w-full"
        />
      </div>
      <div className="absolute w-[318px] h-[64px] top-[8px] left-[50%] translate-x-[-50%]">
        <img
          src="/images/walking-course/course-name-bg.png"
          alt="course-name"
          className="w-full h-full"
        />
        <h1 className="absolute w-full top-[15px] text-center font-bold">{title}</h1>
      </div>

      <div className="absolute bottom-2 w-full">
        <div className="mx-auto rounded-2xl p-2 bg-card  w-[320px] flex flex-col gap-2 items-center">
          <div className="text-lg font-bold">
            <b className="text-primary">{point}</b>ポイントゲット
          </div>
          {organizerName && (
            <div className="w-full flex items-center h-[26px] justify-center text-xs rounded-full bg-primary-5">
              {organizerName}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
