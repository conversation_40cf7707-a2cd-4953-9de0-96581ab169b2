import ComplexEventCard from '@/app/(webview)/event/_components/complex-event-card';
import { Button } from '@/components/shared/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from '@/components/shared/drawer';
import NoData from '@/components/shared/no-data';
import { ScrollArea } from '@/components/shared/scroll-area';
import { useParams, useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { ComplexEventListItem } from '@/types/event-types';
import { ChevronLeft, X } from 'lucide-react';
import { useState } from 'react';
interface OtherEventsDrawerProps {
  eventList?: ComplexEventListItem[];
  show: boolean;
  setShowOtherEvents: (show: boolean) => void;
}

const snapPoints = [0.6, 1];

export default function OtherEventsDrawer({
  eventList,
  show,
  setShowOtherEvents,
}: OtherEventsDrawerProps) {
  const { eventId } = useParams();
  const router = useRouter();
  const [snap, setSnap] = useState<number | string | null>(snapPoints[0]);

  const safeArea = useSafeArea();
  const top = safeArea.top + 72 + 53 + 16;

  return (
    <Drawer
      open={show}
      onOpenChange={setShowOtherEvents}
      snapPoints={snapPoints}
      activeSnapPoint={snap}
      setActiveSnapPoint={setSnap}
    >
      <DrawerContent hideOverlay={true}>
        <DrawerTitle className="sr-only">{'周辺の他のイベント'}</DrawerTitle>
        <DrawerDescription className="sr-only">
          Set your daily activity goal.asdas
        </DrawerDescription>
        <div className="flex justify-between items-center  h-[86px] shrink-0 px-6 ">
          <button
            type="button"
            onClick={() => router.push(`/event/${eventId}`)}
            aria-label="back"
            className="left-[15px]"
          >
            <ChevronLeft size={32} />
          </button>
          <h1 className=" text-[22px] font-bold ">周辺の他のイベント</h1>
          <Button
            size="xs"
            className=" ml-4 relative"
            variant="icon"
            onClick={() => {
              setShowOtherEvents(false);
              setSnap(snapPoints[0]);
            }}
          >
            <X size={24} />
          </Button>
        </div>
        <ScrollArea
          disable={snap !== 1}
          className="w-full"
          style={{ height: `calc(100vh - ${top}px)` }}
          type="hover"
        >
          <div className="flex flex-col gap-2 py-2 px-6">
            {!eventList || eventList?.length === 0 ? <EventSearchNoData /> : null}
            {eventList?.map((event) => (
              <ComplexEventCard
                key={event.eventId}
                event={event}
                onClick={() => router.push(`/event/${event.eventId}`)}
              />
            ))}
          </div>
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
}

function EventSearchNoData() {
  return (
    <div>
      <NoData height={200} title="イベントがありません" imgUrl="/images/event/no-event.svg" />
    </div>
  );
}
