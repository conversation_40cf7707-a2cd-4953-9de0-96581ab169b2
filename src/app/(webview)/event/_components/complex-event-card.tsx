import { eventAPI } from '@/api/modules/event-api';
import { getEventStatus, isEventActive } from '@/app/(webview)/event/_utils/event-helper';
import { Button } from '@/components/shared/button';
import { FavoriteBadge } from '@/components/shared/favorite';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { useRouter } from '@/hooks/use-next-navigation';
import { useEventStore } from '@/store/event-store';
import type { ComplexEventListItem, PointPatternDetails } from '@/types/event-types';
import { formatDate } from '@/utils/date-format';
import { distanceFormat } from '@/utils/distance-format';
import { handleQrScan, parseQrCode } from '@/utils/qrcode';
import { BadgeCheckIcon, CircleParking, MapPin, ScanQrCode, Star } from 'lucide-react';
import { EventLabelList } from './event-label-list';
import { ListPointCard } from './point-card';
export default function ComplexEventCard({
  event,
  className,
  onClick,
}: { event: ComplexEventListItem; className?: string; onClick?: () => void }) {
  const handleAttendEvent = (eventId: string) => {
    const router = useRouter();
    handleQrScan({ eventId }, router);
    const { setEventRefreshTimeStamp } = useEventStore();
    setEventRefreshTimeStamp(new Date().getTime().toString());
  };

  return (
    <Card
      className={`py-3 border-b  rounded-none transition-shadow cursor-pointer ${className}`}
      onClick={onClick}
    >
      <div className="flex gap-3">
        {/* 固定サイズの画像 */}
        <div className="w-20  flex-shrink-0 flex flex-col gap-2 items-center">
          <div className="w-[68px] h-[68px] flex items-center justify-center rounded overflow-hidden bg-background">
            {event.eventImageFilePath ? (
              <img
                className="w-[68px] h-[68px] object-cover rounded"
                src={event.eventImageFilePath}
                alt={event.eventName}
                onError={(e) => {
                  // 画像の読み込みに失敗した場合の処理
                  e.currentTarget.src = '/images/event/default-event.svg';
                }}
              />
            ) : (
              <img
                className="w-[68px] h-[68px] object-cover rounded"
                src="/images/event/default-event.svg"
                alt={event.eventName}
              />
            )}
          </div>
          <div className="flex items-center justify-center gap-0 text-muted-foreground text-xs">
            <MapPin className="w-3 h-3" />

            {distanceFormat(event.distance)}
          </div>

          <FavoriteBadge isFavorited={event.isFavorite} />
        </div>

        {/* 活動信息 */}
        <div className="flex-1 min-w-0 flex flex-col gap-1">
          <p className="font-bold mb-1 line-clamp-2 ">{event.eventName}</p>

          <EventLabelList
            showEventLabel={false}
            eventStartDate={event.eventStartDate}
            eventEndDate={event.eventEndDate}
            hasFee={event.hasFee}
            isReservationRequired={event.isReservationRequired}
          />
          <div className=" text-muted-foreground space-y-0.5">
            <p className="line-clamp-2 text-sm">
              {event.eventStartDate === event.eventEndDate ? (
                formatDate(event.eventStartDate, 'yyyy年MM月dd日(d)')
              ) : (
                <>
                  {formatDate(event.eventStartDate, 'yyyy年MM月dd日(d)')}~{' '}
                  {event.eventEndDate
                    ? formatDate(event.eventEndDate, 'yyyy年MM月dd日(d)')
                    : '終了日未定'}
                </>
              )}
            </p>
            <p className="line-clamp-2 text-sm">{event.venueName}</p>
          </div>
          <div className="flex gap-1">
            <ListPointCard
              pointPattern={event.pointPattern}
              pointPatternDetails={event.pointPatternDetails}
              organizerName={event.organizerName}
            />
          </div>
        </div>
      </div>

      {isEventActive(event.eventStartDate, event.eventEndDate) ? (
        <Button
          className="w-full mt-3"
          variant="outline"
          onClick={() => handleAttendEvent(event.eventId)}
        >
          <ScanQrCode className="w-5 h-5" />
          イベントに参加する
        </Button>
      ) : null}
    </Card>
  );
}
