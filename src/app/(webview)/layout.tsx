'use client';
import AppFooter from '@/components/layout/app-footer';
import ProtectedRoute from '@/components/layout/protected-route';
import { AlertDialogComponent } from '@/components/layout/push-dialog';
import { DynamicMenuProvider } from '@/hooks/use-dynamic-menu';
import { LevelUpProvider } from '@/hooks/use-level-up';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { useCallback, useState } from 'react';

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [dialogState, setDialogState] = useState({
    isDialogOpen: false,
    pushMsg: '',
    pushUrl: '',
  });
  const router = useRouter();

  const { isShow, setDialog } = useMessageDialog();
  const handleCloseDialog = useCallback(() => {
    setDialogState((prev) => ({ ...prev, isDialogOpen: false }));
  }, []);
  const handlePushData = useCallback((jsonStr: string) => {
    try {
      const data = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;

      const message = data?.pushMsg || '';
      const url = data?.pushUrl || '/home';
      const title = data?.pushTitle || '';

      if (url) {
        router.push(url);
      }
      if (message) {
        // 0.5秒後にダイアログを表示
        setTimeout(() => {
          setDialog(true, {
            title: title,
            content: <div className="text-center text-lg font-bold">{message}</div>,
          });
        }, 500);
      }
    } catch (e) {
      console.error('push json fail:', e);
    }
  }, []);
  // useEffect(() => {
  //   sendMessageToNative({
  //     type: 'query-push',
  //     callback: (data) => {
  //       handlePushData(JSON.stringify(data));
  //     },
  //   });
  //   window.pushDataFunction = handlePushData;
  //   return () => {
  //     window.pushDataFunction = undefined;
  //   };
  // }, [handlePushData]);

  return (
    <div className="flex min-h-screen flex-col">
      <ProtectedRoute>
        <LevelUpProvider>
          <DynamicMenuProvider>
            <div className="flex-1">{children}</div>
          </DynamicMenuProvider>
        </LevelUpProvider>
      </ProtectedRoute>

      <AlertDialogComponent
        open={dialogState.isDialogOpen}
        onOpenChange={handleCloseDialog}
        title={dialogState.pushMsg}
        navigateTo={dialogState.pushUrl}
        onConfirm={() => {}}
        onCancel={() => {}}
      />
      <AppFooter />
    </div>
  );
}
