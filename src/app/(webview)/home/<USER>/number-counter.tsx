import React, { useState, useEffect, useRef } from 'react';

const NumberCounter = ({
  targetNumber,
  duration = 2000,
}: { targetNumber: number; duration?: number }) => {
  const [currentNumber, setCurrentNumber] = useState(0);
  const startTimeRef = useRef<number | null>(null);
  const animationRef = useRef<number | null>(null);

  useEffect(() => {
    const animateNumber = (timestamp: number) => {
      if (!startTimeRef.current) startTimeRef.current = timestamp;
      const elapsedTime = timestamp - startTimeRef.current;
      const progress = Math.min(elapsedTime / duration, 1);

      // 使用缓动函数（如 easeOutQuad）使动画更自然
      const easedProgress = 1 - (1 - progress) ** 3;
      const newNumber = Math.floor(easedProgress * targetNumber);

      setCurrentNumber(newNumber);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animateNumber);
      }
    };

    animationRef.current = requestAnimationFrame(animateNumber);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [targetNumber, duration]);

  return (
    <div className="w-full flex justify-center text-3xl font-bold text-gray-900">
      {currentNumber.toLocaleString()}
    </div>
  );
};

export default NumberCounter;
