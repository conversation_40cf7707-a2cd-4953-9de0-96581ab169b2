import { Card, CardContent } from '@/components/ui/card';
import type { CouponListItem } from '@/types/coupon-types';

interface CouponSliderProps {
  couponList: CouponListItem[];
}

export function CouponSlider({ couponList }: CouponSliderProps) {
  return (
    <div className="relative">
      {/* 横向滚动容器 */}
      <div className="flex overflow-x-auto pb-4 px-6 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        <div className="flex space-x-2 mt-2">
          {couponList.map((promo) => (
            <Card key={promo.couponId} className={'border-0 rounded-[8px] w-[134px] flex-shrink-0'}>
              <CardContent className="p-0">
                <div className="flex justify-center">
                  <img
                    className="w-full h-full object-contain"
                    src={promo.imagePath || '/images/coupon/default.png'}
                    alt="coupon-info"
                  />
                </div>
                <div className="p-2">
                  <div className="text-sm font-semibold mr-2">
                    <p className="line-clamp-2">{promo.couponName}</p>
                  </div>
                  <div className="mt-1 text-[11px] text-gray-500">
                    <p>{promo.endDate}まで</p>
                    <p className="truncate">{promo.shopName}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
