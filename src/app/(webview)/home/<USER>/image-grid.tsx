import { homePageAPI } from '@/api/modules/home-page';
import { ROUTES } from '@/const/routes';
import { useRouter } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import type { HomePostItem } from '@/types/home-data';
import { Heart } from 'lucide-react'; // 或使用其他图标库
import { useEffect, useState } from 'react';

export function ImageGrid({ className }: { className?: string }) {
  const [images, setImages] = useState<HomePostItem[]>([]);
  const router = useRouter();
  const handleDetailClick = (post: HomePostItem) => {
    router.push(`${ROUTES.POST.DETAIL}?queryUserId=${post.userId}&postId=${post.postId}`);
  };
  //「いいね!」のクリックを処理
  useEffect(() => {
    homePageAPI.getHomePostList().then((response) => {
      setImages(response.posts || []);
    });
  }, []);
  return (
    <div className={cn('grid grid-cols-3 gap-2 mt-2 max-w-2xl mx-auto', className)}>
      {images?.map((image) => (
        <div
          key={image.userId}
          className="relative aspect-square"
          onClick={() => {
            handleDetailClick(image);
          }}
        >
          <img
            src={image.thumbnailImage}
            alt={image.thumbnailImage}
            className="w-full h-full object-cover"
          />
          <button
            type="button"
            className="absolute bottom-3 left-2 bg-white bg-opacity rounded-full px-1 py-px flex items-center justify-center shadow-sm"
          >
            <Heart
              fill={image.likeCount > 0 ? '#ff0000' : 'transparent'}
              stroke="#ff0000"
              className="w-3.5 h-3.5"
            />
            <span className="text-black text-xs ml-px font-medium leading-none">
              {image.likeCount}
            </span>
          </button>
        </div>
      ))}
    </div>
  );
}
