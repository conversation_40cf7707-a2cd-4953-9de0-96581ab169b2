import {
  SelectDrawer,
  SelectDrawerClose,
  SelectDrawerContent,
  SelectDrawerTrigger,
} from '@/components/shared/select-drawer';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import type { CodeOption } from '@/types/code-types';
import { X } from 'lucide-react';
import { useState } from 'react';

export function SelectCategoryDrawer({
  className,
  defaultValue,
  options,
  title,
  onSelect,
  onChange,
}: {
  className?: string;
  defaultValue: string;
  options: CodeOption[];
  title: string;
  onSelect?: (value: string) => void;
  onChange?: (value: string) => void;
}) {
  const defaultItem = options.find((option) => option.value === defaultValue);
  const handleSelect = (item: CodeOption) => {
    console.log('item', item);
    onSelect?.(item.value);
    onChange?.(item.value);
  };

  // console.log(options, defaultItem, selectedItem);
  return (
    <SelectDrawer>
      <SelectDrawerTrigger className={className}>
        <span>{defaultItem?.label || ''}</span>
      </SelectDrawerTrigger>
      <SelectDrawerContent className="bg-card pb-4">
        <div className="flex items-center justify-center my-3">
          <div className="text-center text-lg font-bold">{title}</div>
          <SelectDrawerClose asChild>
            <X className="absolute right-6" size={24} />
          </SelectDrawerClose>
        </div>
        <RadioGroup defaultValue={defaultItem?.value} className="h-[500px] overflow-auto">
          {options.map((item) => (
            <SelectDrawerClose
              key={item.value}
              asChild
              onClick={() => {
                handleSelect(item);
              }}
            >
              <div className="flex items-center h-14 px-6">
                <RadioGroupItem value={item.value} id={`select-${item.value}`} />
                <Label htmlFor={`select-${item.value}`} className="text-base ml-4">
                  {item.label}
                </Label>
              </div>
            </SelectDrawerClose>
          ))}
        </RadioGroup>
      </SelectDrawerContent>
    </SelectDrawer>
  );
}
