import {
  SelectDrawer,
  SelectDrawerClose,
  SelectD<PERSON>er<PERSON>ontent,
  SelectDrawerTrigger,
} from '@/components/shared/select-drawer';
import { TextButton } from '@/components/shared/text-button';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import React, { useState } from 'react';

interface SelectDateDrawerProps {
  className?: string;
  minYear: number;
  maxYear: number;
  onSelect: (value: { year: string; month: string }) => void;
  title: string;
  value?: {
    year: string | number;
    month: string | number;
  };
  distableDate?: Date;
}

export function SelectDateDrawer(props: SelectDateDrawerProps) {
  const { className, title, minYear, maxYear, value, distableDate, onSelect } = props;
  const [year, setYear] = useState(Number(value?.year) || new Date().getFullYear());
  const [month, setMonth] = useState(Number(value?.month) || new Date().getMonth());
  const [disDate, setDisDate] = useState(distableDate || new Date());

  const handleYearChange = (direction: string) => {
    const newYear = direction === 'prev' ? year - 1 : year + 1;
    if (newYear >= minYear && newYear <= maxYear) {
      setYear(newYear);
    }
  };

  const isMonthDisabled = (newMonth: number) => {
    const initialYear = disDate?.getFullYear();
    const initialMonth = disDate?.getMonth() + 1;
    if (initialYear === year && newMonth > initialMonth) {
      return true;
    }
    return false;
  };

  const handleMonthChange = (newMonth: number) => {
    if (isMonthDisabled(newMonth)) {
      return;
    }
    setMonth(newMonth);
    onSelect({ year: String(year), month: String(newMonth) });
  };

  return (
    <SelectDrawer>
      <SelectDrawerTrigger className={className}>
        {value?.year}年{value?.month}月
      </SelectDrawerTrigger>
      <SelectDrawerContent className="bg-card m-0 !pb-0">
        <div className="flex items-center justify-center my-3 ">
          <div className="text-center text-lg font-bold h-12 leading-[48px] ">{title}</div>
          <SelectDrawerClose asChild>
            <X className="absolute right-6" size={24} />
          </SelectDrawerClose>
        </div>
        <div className="bg-white px-4 py-6">
          <div className="flex justify-between items-center mb-[28px]">
            <TextButton
              size="sm"
              variant="muted"
              onClick={() => handleYearChange('prev')}
              disabled={year <= minYear}
              className="text-black"
            >
              <ChevronLeft />
            </TextButton>
            <span className="text-base font-normal">{year}年</span>
            <TextButton
              size="sm"
              variant="muted"
              onClick={() => handleYearChange('next')}
              disabled={year >= maxYear}
              className=" text-black"
            >
              <ChevronRight />
            </TextButton>
          </div>
          <div className="grid grid-cols-3 gap-3">
            {/* <div className="flex justify-center items-center flex-wrap"> */}
            {Array.from({ length: 13 }, (_, i) => (
              <SelectDrawerClose key={i} asChild>
                {i > 0 && (
                  <TextButton
                    onClick={() => handleMonthChange(i)}
                    className={`w-[98px] h-[56px] text-button text-xl font-bold ${month === i && !isMonthDisabled(i) ? 'bg-main-fix rounded-[28px] ' : ''} ${isMonthDisabled(i) ? 'opacity-50' : ''}`}
                    disabled={isMonthDisabled(i)}
                  >
                    {i}月
                  </TextButton>
                )}
              </SelectDrawerClose>
            ))}
          </div>
        </div>
      </SelectDrawerContent>
    </SelectDrawer>
  );
}
