'use client';
import TopBar from '@/components/layout/top-bar';
import { TextButton } from '@/components/shared/text-button';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { ChevronDown } from 'lucide-react';
import { useRef, useState } from 'react';
import { TablePage } from '../_components/table';
import { VerticalTablePage } from '../_components/vertical-table';
import { POINT_HINT_TITLE } from '../_const';

export default function PointHintPage() {
  const safeArea = useSafeArea();
  const safeTop = safeArea.top ?? 0;
  const pointRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  // Dynamically get or create ref
  const getPointRef = (value: string) => {
    return (element: HTMLDivElement | null) => {
      if (element) {
        pointRefs.current[value] = element;
      }
    };
  };

  // Scroll to the specified value element
  const scrollToPointGet = (value: string) => {
    const element = pointRefs.current[value];
    if (element) {
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - 60 - safeTop;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  return (
    <div className="">
      <TopBar title={APP_TEXT.POINT.HINT_PAGE} />

      {/* click title link to position */}
      <div className="p-6 bg-white">
        {POINT_HINT_TITLE.map((item) => (
          <div key={item.value} className="flex items-center mb-4">
            <TextButton
              onClick={() => scrollToPointGet(item.value)}
              className="text-button text-[15px]"
            >
              {item.name}
              <ChevronDown className="" />
            </TextButton>
          </div>
        ))}

        {/* Text is extracted based on the style book, and the image directly uses text */}
        <div className="font-bold text-xl my-6" ref={getPointRef('1')}>
          ポイント獲得について
        </div>
        <div className="font-normal text-base mt-2">
          <div className="font-bold mb-4">A.抽選機能を利用の主催団体：</div>
          歩数送信、アプリ利用、バイタルデータ入力などを行うと、それに応じたポイントがもらえます。ポイントは抽選の応募に使用されます。
          <div className="font-bold mb-4 mt-6">B.直接ポイント交換を利用の主催団体：</div>
          歩数送信、アプリ利用、バイタルデータ入力などを行うと、それに応じたポイントがもらえます。ポイントは他社の電子マナーに交換できます。
          <br />
        </div>
        <div className="font-bold text-lg my-6">歩数ボーナス(1日1回獲得)</div>
        <div className="font-normal text-base mt-2">
          1日の歩数により以下のポイントが獲得できます。
          <br />
          ・歩数条件と獲得ポイント対照表
          {/* <br /> */}
          {/* ※主催団体の設定によって変わる */}
          {/* <br /> */}
          {/* ※歩数ボーナスが設定しない主催団体が表示しません */}
        </div>
        <TablePage
          headerFirst={'1日の歩数'}
          headerSecond={'獲得ボーナス'}
          data={[
            {
              bodyFirst: '10,000歩〜',
              bodySecond: '10,000p',
            },
            {
              bodyFirst: '9,000歩〜',
              bodySecond: '9,000p',
            },
            {
              bodyFirst: '7,000歩〜',
              bodySecond: '8,000p',
            },
            {
              bodyFirst: '5,000歩〜',
              bodySecond: '7,000p',
            },
            {
              bodyFirst: '3,000歩〜',
              bodySecond: '6,000p',
            },
            {
              bodyFirst: '1,000歩〜',
              bodySecond: '5,000p',
            },
            {
              bodyFirst: '500歩〜',
              bodySecond: '1,000p',
            },
            {
              bodyFirst: '1歩〜',
              bodySecond: '100p',
            },
          ]}
        />
        <div className="font-bold text-lg my-6">初回起動ボーナス</div>
        <div className="font-normal text-base mt-2">
          ユーザーが新規登録後、アプリが初めて起動された時に、1,000ポイントが獲得できます。
          {/* <br />
          ※主催団体の設定によって、ポイント数が変わる
          <br />
          ※初回起動ボーナスが設定しない主催団体が表示しません */}
        </div>
        <div className="font-bold text-lg my-6">毎日初回起動ボーナス</div>
        <div className="font-normal text-base mt-2">
          アプリが毎日初めて起動された時に、1,000ポイントが獲得できます。ただ、初回起動ボーナス(優先)と同時獲得できません。
        </div>
        <div className="font-bold text-lg my-6">連続起動ボーナス</div>
        <div className="font-normal text-base mt-2">
          複数日連続でアプリが起動された時に獲得できるボーナスです。
        </div>
        <TablePage
          headerFirst={'連続起動日数'}
          headerSecond={'ポイント'}
          data={[
            { bodyFirst: '3', bodySecond: '10' },
            { bodyFirst: '7', bodySecond: '20' },
            { bodyFirst: '30', bodySecond: '30' },
          ]}
        />
        <div className="font-bold text-lg my-6">月目標達成ボーナスの設定</div>
        <div className="font-normal text-base mt-2">
          アプリ起動後、歩数送信の際に月間で自分が設定した1日の目標歩数以上歩く日が20日以上の方は、1,000ポイントが獲得できます。
        </div>
        <div className="font-bold text-lg my-6">「行きたい！」達成ボーナスの設定</div>
        <div className="font-normal text-base mt-2">
          マップに投稿した写真は、他の参加者から「行きたい！」がもらえます。100回「行きたい！」達成で、1,000ポイントが獲得できます。
        </div>
        <div className="font-bold text-lg my-6">バイタルデータ入力達成ボーナスの設定</div>
        <div className="font-normal text-base mt-2">
          毎日初めてバイタルデータを入力の時に獲得できるボーナスです。
        </div>
        <VerticalTablePage
          data={[
            { title: '血圧', content: '20' },
            { title: '体脂肪率', content: '20' },
            { title: '睡眠時間', content: '20' },
            { title: '血糖値', content: '20' },
          ]}
        />
        <div className="font-bold text-lg my-6">健診結果&検診日登録達成ボーナスの設定</div>
        <div className="font-normal text-base mt-2">
          健康診断結果と健診日を登録時に獲得できるボーナスです。
        </div>
        <VerticalTablePage
          data={[
            { title: '健康診断結果登録', content: '20' },
            { title: '検診日登録', content: '20' },
            { title: '胃がん検診', content: '20' },
            { title: '肺がん検診', content: '20' },
            { title: '大腸がん検診', content: '20' },
            { title: '子宮頸がん検診', content: '20' },
            { title: '乳がん検診', content: '20' },
            { title: '前立腺がん検診', content: '20' },
            { title: '基本健診', content: '20' },
            { title: '健康診查', content: '20' },
            { title: '骨粗鬆症健診', content: '20' },
            { title: '歯科健診', content: '20' },
          ]}
        />
        <div className="border-b border-border border-solid my-8" />
        <div className="font-bold text-xl my-6" ref={getPointRef('2')}>
          保有ポイントについて
        </div>
        <div className="font-normal text-base mt-2">
          <div className="font-bold mb-4"> A.抽選機能を利用の主催団体：</div>
          現在保有しているポイントの合計です。抽選に利用できます。年度ごとにリセットされます（毎年4月1日）。
          <div className="font-bold mb-4 mt-6">B.直接ポイント交換を利用の主催団体：</div>
          現在保有しているポイントの合計です。他社の電子マナーへの交換に利用できます。年度ごとにリセットされます（毎年4月1日）。
        </div>
        <div className="border-b border-border border-solid my-8" />
        <div className="font-bold text-xl my-6" ref={getPointRef('3')}>
          今年度獲得ポイントについて
        </div>
        <div className="font-normal text-base mt-2">
          今年度（4月〜翌年3月）に獲得した累計ポイント数です。年度が変わると自動的にリセットされます。
        </div>
        <div className="border-b border-border border-solid my-8" />
        <div className="font-bold text-xl my-6" ref={getPointRef('4')}>
          通算獲得ポイントについて
        </div>
        <div className="font-normal text-base mt-2">
          アプリ利用開始からこれまでに獲得したポイントの累計です。年度をまたいだ合計ポイントが表示されます。
        </div>
        <div className="border-b border-border border-solid my-8" />
        <div className="font-bold text-xl my-6" ref={getPointRef('5')}>
          交換可能ポイントについて
        </div>
        <div className="font-normal text-base mt-2">
          主催団体が設定した交換上限と、これまでに交換済のポイント数に応じて、現在交換できるポイント数を表示します。
        </div>
        <div className="border-b border-border border-solid my-8" />
        <div className="font-bold text-xl my-6" ref={getPointRef('6')}>
          景品選択可能ポイントについて
        </div>
        <div className="font-normal text-base mt-2">
          抽選に応募中のポイントを差し引いた、今すぐ景品応募に使える残りのポイントです。{' '}
        </div>
        <div className="border-b border-border border-solid my-8" />
      </div>
    </div>
  );
}
