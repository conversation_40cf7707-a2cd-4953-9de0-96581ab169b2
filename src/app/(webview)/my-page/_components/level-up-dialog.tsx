'use client';
// components/level-up/LevelUpDialog.tsx
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { getDialogImagePath, getLevelIconInfo, getNewIconGetLvlCnts } from '@/const/gamification';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import type { MissionListResponse } from '@/types/my-page';
import { LevelUpImage } from './lv-up-image';

export type LevelUpDialogProps = {
  data: MissionListResponse | null;
  isOpen: boolean;
  onClose: () => void;
  preventOutsideClick?: boolean;
};

export function LevelUpDialog({
  data,
  isOpen,
  onClose,
  preventOutsideClick = false,
}: LevelUpDialogProps) {
  const { currentLevel, currentLevelIcon } = data?.levelBasicInfo ?? {};
  const { speed } = data?.levelUpInfo ?? {};
  const { missionTitle } = data?.levelUpMissionCardInfo ?? {};
  const pathname = usePathname();
  const router = useRouter();

  const handleClick = () => {
    if (pathname !== '/my-page') {
      router.push('/my-page');
      onClose();
    } else {
      onClose();
    }
  };
  // アイコンデータを処理する
  const info = getLevelIconInfo(currentLevel ?? '1');
  const nextIconCnt = getNewIconGetLvlCnts(currentLevel ?? '1');

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        onInteractOutside={(e) => preventOutsideClick && e.preventDefault()}
        className="sm:max-w-[425px] w-[calc(100vw-48px)] rounded-3xl p-0 overflow-hidden [&>button]:hidden"
      >
        {/* 使用 LevelUpImage 组件 */}
        <LevelUpImage
          currentLevelImage={currentLevelIcon ?? getDialogImagePath(info.imageUrl)}
          currentLevel={currentLevel}
          newIconGetFlg={info.getNewIcon}
          iconName={info.name}
          speed={speed}
        />

        {/* 内容アリア */}
        <div className="px-8 pb-8">
          <div className="rounded-2xl bg-secondary overflow-hidden h-22 mb-2">
            <div className="px-4 py-1">
              <p className="text-base font-medium mt-3 mb-1">達成したミッション</p>
              <p className="text-base text-primary font-bold mt-2 mb-3">{missionTitle}</p>
            </div>
          </div>

          <div className="mb-4">
            {currentLevel === '1'
              ? APP_TEXT.MY_PAGE.DIALOG.FIRST_TIME
              : currentLevel === '100'
                ? APP_TEXT.MY_PAGE.DIALOG.LEVEL_UP_MAX
                : info.getNewIcon
                  ? APP_TEXT.MY_PAGE.DIALOG.GET_ICON(String(nextIconCnt) ?? '')
                  : APP_TEXT.MY_PAGE.DIALOG.LEVEL_UP(
                      String(Number(currentLevel) + nextIconCnt) ?? '0',
                    )}
          </div>

          <div className="flex flex-col gap-4">
            <Button onClick={handleClick}>{APP_TEXT.MY_PAGE.OPEN}</Button>
            <TextButton onClick={onClose} className="text-gray-600">
              {COMMON_TEXT.BUTTON.CLOSE}
            </TextButton>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
