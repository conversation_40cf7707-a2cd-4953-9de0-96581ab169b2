import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useRouter } from '@/hooks/use-next-navigation';
import { RotateCw } from 'lucide-react';

interface MissionCardProps {
  level: string;
  missionId?: string; // 新增 missionId 属性
  missionContent?: string;
  achievementCounts?: string;
  achievementCap?: string;
  appStartDate?: string;
  isDone: boolean;
  isNext: boolean;
  isPending: boolean;
  missionChangeFlg?: string;
  onChangeMission?: () => void;
}

export default function MissionCard({
  level,
  missionId,
  missionContent,
  achievementCounts,
  achievementCap,
  appStartDate,
  isDone,
  isNext,
  isPending,
  missionChangeFlg,
  onChangeMission,
}: MissionCardProps) {
  const router = useRouter();

  const handleCardClick = () => {
    // レベル1以外の場合のみナビゲーション
    if (level !== '1' && missionId) {
      router.push(`/my-page/mission-detail/${missionId}`);
    }
  };

  const renderMissionContent = () => {
    // レベル1の場合
    if (level === '1') {
      return (
        <div className="flex-1 ml-2">
          <div className="text-base font-bold line-clamp-2">本アプリ利用開始</div>
          <div className="text-xs text-muted-foreground line-clamp-2 mt-2">
            利用開始日：{appStartDate}
          </div>
        </div>
      );
    }

    // ペンディング状態の場合
    if (isPending) {
      return (
        <div className="flex h-full">
          {/* 左边：mission.png + レベル */}
          <div className="w-[60px] flex-none flex flex-col items-center justify-center">
            <div className="w-12 h-12">
              <img className="w-full" src="/images/my-page/mission.png" alt="mission-info" />
            </div>
            <div className="-mt-[6px] w-12">
              <div className="bg-card w-full rounded-full border-2 border-primary-90 h-4 flex items-center justify-center">
                <span className="text-[9px] text-black font-bold leading-none">レベル</span>
              </div>
            </div>
          </div>

          {/* 中间：内容区域 */}
          <div className="flex-1 flex flex-col justify-between">
            <div className="text-base font-bold line-clamp-2 min-h-[2.5rem]">？？？</div>
            <div className="text-sm text-muted-foreground line-clamp-1 mb-2 -mt-6">
              レベル{Number(level) - 1}達成で閲覧できます
            </div>
          </div>
        </div>
      );
    }

    // 通常のミッション表示
    return (
      <div className="flex h-full">
        {/* 左边：mission.png + レベル */}
        <div className="w-[60px] flex-none flex flex-col items-center justify-center">
          <div className="w-12 h-12">
            <img className="w-full" src="/images/my-page/mission.png" alt="mission-info" />
          </div>
          <div className="-mt-[6px] w-12">
            <div className="bg-card w-full rounded-full border-2 border-primary-90 h-4 flex items-center justify-center">
              <span className="text-[9px] text-black font-bold leading-none">レベル</span>
            </div>
          </div>
        </div>

        {/* 中间：内容区域 */}
        <div className="flex-1 flex flex-col justify-between">
          <div className="text-base font-bold line-clamp-2 min-h-[2.5rem]">{missionContent}</div>
          <div className="flex items-center -mb-1">
            <Progress
              value={(Number(achievementCounts) / Number(achievementCap)) * 100}
              className="grow h-2.5 mr-1 rounded-2xl bg-blue-50"
              indicatorClassName="bg-primary-light"
            />
            <p className="text-xs text-gray-500">
              {achievementCounts}/{achievementCap}
            </p>
          </div>
        </div>

        {/* 右边：变更按钮 */}
        {isNext && missionChangeFlg && (
          <div className="w-[60px] flex-none flex flex-col items-center justify-center -mr-2">
            <div className="w-8 h-8">
              <Button
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡到卡片点击
                  onChangeMission?.();
                }}
                className="w-full h-full rounded-full bg-primary-500 hover:bg-primary-600 flex items-center justify-center transition-colors ring-1 ring-primary"
              >
                <RotateCw className="w-6 h-6 text-primary" />
              </Button>
            </div>
            <div className="w-8 mt-1">
              <div className="w-full h-4 flex items-center justify-center">
                <span className="text-primary text-sm leading-none">変更</span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={isPending ? 'opacity-50' : ''}>
      <div
        className={`w-full h-[82px] overflow-hidden bg-card rounded-2xl py-3 pl-3 pr-3 relative z-10 ${
          level !== '1' && missionId
            ? 'cursor-pointer hover:bg-gray-25 hover:bg-opacity-30 transition-colors duration-200'
            : ''
        }`}
        onClick={handleCardClick}
      >
        {renderMissionContent()}

        {/* 達成オーバーレイ */}
        {isDone && level !== '1' && (
          <div className="absolute inset-0 rounded-2xl flex justify-end items-center pr-4">
            {/* 背景层 */}
            <div className="absolute inset-0 bg-primary opacity-50 rounded-2xl" />
            {/* 圆圈 */}
            <div className="relative z-10 w-12 h-12 bg-white rounded-full border-2 border-primary flex items-center justify-center text-primary font-semibold whitespace-nowrap">
              達成!
            </div>
          </div>
        )}

        {/* ペンディングオーバーレイ */}
        {isPending && <div className="absolute inset-0 bg-gray-200/20 rounded-2xl" />}
      </div>
    </div>
  );
}
