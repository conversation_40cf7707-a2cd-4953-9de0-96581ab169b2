import { Button } from '@/components/ui/button';

interface UserProfileCardProps {
  nickName?: string;
  userId?: string;
  currentLevel?: string;
  currentLevelIcon?: string;
  iconName?: string;
  safeAreaTop?: number;
  onProfileChange?: () => void;
}

export default function UserProfileCard({
  nickName,
  userId,
  currentLevel,
  currentLevelIcon,
  iconName,
  onProfileChange,
}: UserProfileCardProps) {
  return (
    <div className="bg-white pt-3 pl-6 pr-6 pb-3 flex-none">
      <div className="flex flex-1 gap-2">
        <div className="h-16 w-16 flex-none overflow-hidden rounded-full">
          <div className="h-full w-full bg-primary-30 flex items-center justify-center">
            <img src={currentLevelIcon} alt="avatar" className="w-16 h-16 text-gray-800" />
          </div>
        </div>
        <div className="flex-1">
          <h2 className="text-base text-[#000000] font-bold">{nickName}</h2>
          <p className="text-xs text-[#666666]">会員番号：{userId}</p>
          <p className="flex gap-1 mt-1">
            <span className="bg-[#F6F8FF] pt-1 pb-1 pl-2 pr-2 rounded-[12px] font-bold text-[#4457D1] text-xs">
              {`Lv.${currentLevel || ''}`}
            </span>
            <span className="content-center text-[#666666] text-xs">{iconName}</span>
          </p>
        </div>
      </div>
      {/* <div className="mt-2">
        <Button className="w-full rounded-3xl h-8" onClick={onProfileChange}>
          プロフィールを変更する
        </Button>
      </div> */}
    </div>
  );
}
