import { gamificationAPI } from '@/api/modules/my-page';
import { getLevelIconInfo, getLevelIconPath, getNewIconGetLvlCnts } from '@/const/gamification';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import type { MissionHistoryList, MyPageResponse, UpdateMissionRequest } from '@/types/my-page';
import React, { useEffect, useState, useRef, useCallback } from 'react';
import CurrentMissionButton from './current-mission-button';
import MissionCard from './mission-card';
import TimelineBackground from './timeline-background';
import TimelineNode from './timeline-node';

type TimelineProps = {
  data: MyPageResponse;
  onMissionUpdate?: () => void;
};

const handleChangeMission = async (data: UpdateMissionRequest, onMissionUpdate?: () => void) => {
  try {
    await gamificationAPI.changeMission(data);

    if (onMissionUpdate) {
      onMissionUpdate();
    }
  } catch (err) {
    console.error('Error fetching exam data:', err);
  }
};

export default function LevelMissionTimeline({ data, onMissionUpdate }: TimelineProps) {
  const { currentLevel, appStartDate, missionChangeFlg, levelUpHistoryList } = data;

  // 表示対象のデータを事前にフィルタ
  const items =
    levelUpHistoryList?.filter(
      (lvl: MissionHistoryList) => Number(lvl.level) - 7 <= Number(currentLevel),
    ) ?? [];

  const [bottom, setBottom] = useState<number>(0);
  const [isNextVisible, setIsNextVisible] = useState<boolean>(true);
  const safeArea = useSafeArea();

  // refs for scroll functionality
  const containerRef = useRef<HTMLDivElement>(null);
  const nextItemRef = useRef<HTMLDivElement>(null);

  // Calculate pending items count
  const pendingItems = items.filter(
    (lvl: MissionHistoryList) => Number(lvl.level) > Number(currentLevel),
  );
  const hasSixPendingItems = pendingItems.length === 7;

  useEffect(() => {
    const menuHeight = 65;
    const menuMargin = 16;
    const bottom = safeArea.bottom + menuHeight + menuMargin;
    setBottom(bottom);
  }, [safeArea]);

  // Scroll to current mission (isNext = true)
  const scrollToDefault = useCallback(() => {
    if (nextItemRef.current) {
      nextItemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      });
    }
  }, []);

  // Check if current mission is visible
  const checkVisibility = useCallback(() => {
    if (!nextItemRef.current || !containerRef.current) return;

    const rect = nextItemRef.current.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();

    // Check if the element is within the visible area
    const isVisible = rect.top >= containerRect.top && rect.bottom <= containerRect.bottom;

    setIsNextVisible(isVisible);
  }, []);

  // Initial scroll to current mission on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      scrollToDefault();
      // Check visibility after scroll
      setTimeout(checkVisibility, 500);
    }, 100);

    return () => clearTimeout(timer);
  }, [scrollToDefault, checkVisibility]);

  // Set up intersection observer to track visibility
  useEffect(() => {
    if (!nextItemRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsNextVisible(entry.isIntersecting);
      },
      {
        threshold: 0.5, // Consider visible when 50% is in view
        rootMargin: '-180px 0px -61px 0px', // Add some margin for better UX
      },
    );

    observer.observe(nextItemRef.current);

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={containerRef} className="mt-[16px] ml-4 mr-3 pb-6 relative overflow-visible">
      {/* Timeline background lines */}
      <TimelineBackground itemsLength={items.length} currentLevel={currentLevel} items={items} />

      {/* Timeline items */}
      <div className="flex flex-col">
        {items.map((lvl: MissionHistoryList, idx) => {
          const isDone = lvl.achievementStatus === '1';
          const isNext = lvl.achievementStatus === '2';
          const isPending = lvl.achievementStatus === '3';
          const isOverLimit = hasSixPendingItems && Number(lvl.level) > Number(currentLevel) + 7;

          //　アイコンデータを処理する
          const info = getLevelIconInfo(lvl.level ?? 1);

          return (
            <div
              key={lvl.level}
              className={cn(
                'relative mt-3 mb-3',
                // 添加渐变淡出效果
                isOverLimit && 'opacity-30',
              )}
              ref={isNext ? nextItemRef : null}
            >
              {/* 居中対齐区域：左側Icon+レベル 与 右側ミッションカード */}
              <div className="flex items-center">
                {/* Timeline Node */}
                <TimelineNode
                  level={lvl.level}
                  newIcon={lvl.newIcon ?? getLevelIconPath(info.imageUrl)}
                  newIconGettingFlg={info.getNewIcon}
                  isDone={isDone}
                  isNext={isNext}
                  isPending={isPending}
                />

                {/* Mission Card */}
                <div className="flex-1 ml-2 mr-3">
                  <MissionCard
                    level={lvl.level}
                    missionId={lvl.missionId}
                    missionContent={lvl.missionContent}
                    achievementCounts={lvl.achievementCounts}
                    achievementCap={lvl.achievementCap}
                    appStartDate={appStartDate}
                    isDone={isDone}
                    isNext={isNext}
                    isPending={isPending}
                    missionChangeFlg={missionChangeFlg}
                    onChangeMission={() =>
                      handleChangeMission(
                        {
                          level: Number(currentLevel),
                          preMissionId: Number(lvl.missionId),
                        },
                        () => {
                          setTimeout(() => {
                            onMissionUpdate?.();
                          }, 1000);
                        },
                      )
                    }
                  />
                </div>
              </div>

              {/* 右侧底部中心的文字 - 独立于居中对齐 */}
              {isNext && lvl.level !== '100' && (
                <div className="ml-16">
                  <div className="text-center font-bold text-xs">
                    次のアイコン獲得まであと
                    <span className="text-primary">{getNewIconGetLvlCnts(lvl.level)}レベル</span>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* 底部渐变淡出效果 */}
        {hasSixPendingItems && (
          <div
            className="absolute bottom-0 left-0 right-0 h-28 pointer-events-none z-30"
            style={{
              background:
                'linear-gradient(to top, hsl(var(--muted)) 0%, hsl(var(--muted) / 0.95) 20%, hsl(var(--muted) / 0.8) 60%, hsl(var(--muted) / 0.3) 85%, transparent 100%)',
            }}
          />
        )}
      </div>

      {/* Current Mission Button */}
      <CurrentMissionButton isVisible={isNextVisible} bottom={bottom} onClick={scrollToDefault} />
    </div>
  );
}
