'use client';

import { gamificationAPI } from '@/api/modules/my-page';
import TopBar from '@/components/layout/top-bar';
import { type QaData, QaPage } from '@/components/shared/qa-page';
import { getLevelIconInfo, getLevelIconPath } from '@/const/gamification';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useAuthStore } from '@/store/auth';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import LevelMissionTimeline from './_components/level-mission-timeline';
import { LevelContent, MissionContent } from './_components/qa-content-components';
import UserProfileCard from './_components/user-profile-card';

const qaData: QaData[] = [
  {
    id: 'level-is',
    title: 'レベルとは',
    content: <LevelContent />,
  },
  {
    id: 'levelup-mission',
    title: 'レベルアップミッション',
    content: <MissionContent />,
  },
];

export default function MyPage() {
  const router = useRouter();
  const { user, token } = useAuthStore.getState();
  const { setSlidePage } = useSlidePage();
  const safeArea = useSafeArea();
  const safeTop = (safeArea.top ?? 0) + 48;

  const { data: myPageData, refetch } = useQuery({
    queryKey: ['myPage'], // 必須
    queryFn: () => gamificationAPI.getMyPage(),
  });

  const refreshMyPageData = () => {
    setTimeout(() => {
      refetch();
    }, 1000);
  };

  //　アイコンデータを処理する
  const info = getLevelIconInfo(myPageData?.currentLevel ?? 1);

  const handleProfileChange = () => {
    // プロフィール変更処理
    router.push('/menu/settings');
    console.log('プロフィール変更');
  };

  return (
    <>
      <TopBar
        title="マイページ"
        hideShadow={true}
        rightIcon={<img src="/images/ranking/hint.svg" alt="hint" width={20} height={20} />}
        rightIconClick={() => {
          setSlidePage(true, {
            title: 'レベルについて',
            isOverAll: true,
            content: <QaPage data={qaData} />,
            enableClose: false,
            enableBack: true,
            slideFrom: 'right',
          });
        }}
      />
      <div className="flex flex-col" style={{ height: `calc(100vh - ${safeTop}px)` }}>
        {/* User Profile Card */}
        <UserProfileCard
          nickName={myPageData?.nickName}
          userId={user?.id}
          currentLevel={myPageData?.currentLevel}
          currentLevelIcon={myPageData?.currentLevelIcon ?? getLevelIconPath(info.imageUrl)}
          iconName={info.name}
          onProfileChange={handleProfileChange}
        />

        {/* Timeline area - remain space */}
        <div className="flex-1 overflow-hidden">
          {myPageData && (
            <div className="h-full overflow-y-auto">
              <LevelMissionTimeline data={myPageData} onMissionUpdate={refreshMyPageData} />
              <div className="h-[60px]" />
            </div>
          )}
        </div>
      </div>
      {/* Dialog area */}
      {/* 悬浮按钮 */}
      {/* <Button
        className="fixed bottom-20 right-20 rounded-full w-14 h-14 shadow-lg"
        onClick={() => setIsDialogOpen(true)}
      >
        DIALOG
      </Button> */}
    </>
  );
}
