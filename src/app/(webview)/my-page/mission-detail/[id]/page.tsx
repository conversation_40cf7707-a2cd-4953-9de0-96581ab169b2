'use client';

import { gamificationAPI } from '@/api/modules/my-page';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { LevelUpProvider, useLevelUp } from '@/hooks/use-level-up';
import { useParams, useRouter } from '@/hooks/use-next-navigation';
import { useAuthStore } from '@/store/auth';
import type { MissionDetailResponse } from '@/types/my-page';
import React, { useEffect, useState } from 'react';

export default function MyPage() {
  const { user, token } = useAuthStore.getState();
  const [missionDetailData, setMissionDetailData] = useState<MissionDetailResponse>();
  const { checkLevelUp, openDialog } = useLevelUp();
  const params = useParams();
  const router = useRouter();
  const missionId = Array.isArray(params.id) ? params.id[0] : params.id;

  // ミッション詳細を取得
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await gamificationAPI.getMissionDetail({ missionId });
        setMissionDetailData(response);
      } catch (err) {
        console.log('Error fetching mission detail:', err);
      }
    };

    if (missionId) {
      fetchData();
    }
  }, [missionId]);

  // ページロード時にレベルアップ確認
  useEffect(() => {
    checkLevelUp('home'); // ✅ ここで "home" を渡す
  }, []);

  return (
    <div className="flex bg-card flex-col h-[calc(100vh-61px)]">
      <TopBar
        title="ミッション詳細"
        hideShadow={true}
        onBack={() => {
          router.back();
        }}
      />
      <div className="pt-6 px-6 pb-4">
        <div className="text-xl font-bold">{missionDetailData?.missionContent}</div>
        <div className="mt-2 whitespace-pre-line">
          {missionDetailData?.missionDetail?.replace(/\\n/g, '\n')}
        </div>

        <Button
          className="absolute bottom-[81px] left-6 w-[calc(100%-48px)]"
          onClick={() => router.push('/my-page')}
          disabled
        >
          {APP_TEXT.MY_PAGE.MISSION_DETAIL.AUTO_COMPLETION}
        </Button>
      </div>
    </div>
  );
}
