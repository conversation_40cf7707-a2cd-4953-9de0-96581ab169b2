'use client';

import { missionAPI } from '@/api/modules/mission';
import TopBar from '@/components/layout/top-bar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { APP_TEXT } from '@/const/text/app';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type {
  ExtendedQuizUpdateResponse,
  MissionListType,
  OrganizerType,
  PointInfoQuizType,
  PointInfoType,
  QuizAchieveUpdatelResponse,
  QuizMissionDetailResponse,
  QuizTypeItem,
} from '@/types/mission';
import { CircleIcon, Cross2Icon } from '@radix-ui/react-icons';
import { useEffect, useRef, useState } from 'react';
import { progressValue } from '../../_utils/data-convert';
import { CATEGORY_TYPE, ICON_TYPE, QUIZ_TYPE, point_Limit_Type } from '../../_utils/enums';
import Points from './_components/quiz-point';

import { useSafeArea } from '@/hooks/use-safe-area';
import { useMissionState } from '@/store/mission';
import { Check } from 'lucide-react';
import AlertDialogMission from '../../_components/alert-dialog-mission';
import QuizaAlertDialog from './_components/quiz-alert-dialog';
export default function MissionQuiz() {
  const { top, bottom } = useSafeArea();
  const searchParams = useSearchParams().get('data');
  const { missionInfo } = useMissionState();
  const itemMissionRef = useRef<MissionListType | undefined>();
  const router = useRouter();
  const contentTypeRef = useRef<ExtendedQuizUpdateResponse>();
  const judgmentMissionFlgRef = useRef(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [openPointDialog, setOpenPointDialog] = useState(false);
  const [organizerIdArr, setOrganizerIdArr] = useState<number[]>([]);
  const [contentDetail, setContentDetail] = useState<QuizMissionDetailResponse | undefined>();
  const categoryRef = useRef(0);
  const [quizAlertItem, setQuizAlertItem] = useState<QuizTypeItem>();
  const [isPointShow, setIsPointShow] = useState(false);
  // const [todayAchievedFlg, setTodayAchievedFlg] = useState<boolean>(false);
  const [finishFlg, setFinishFlg] = useState<boolean>(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  useEffect(() => {
    itemMissionRef.current = missionInfo;
    // setTodayAchievedFlg(missionInfo.todayAchievedFlg);
    // {/* todayAchievedFlg ||
    //         (itemMissionRef.current?.pointLimitFlg === point_Limit_Type.NOT_HAVE &&
    //           !todayAchievedFlg) */}
    if (
      missionInfo.todayAchievedFlg ||
      itemMissionRef.current?.pointLimitFlg === point_Limit_Type.HAVE
    ) {
      setFinishFlg(true);
    } else {
      setFinishFlg(false);
    }
    if (itemMissionRef.current) {
      const organizerIds: number[] = [];
      const organizerIdsAchieve: number[] = [];
      itemMissionRef.current?.organizerList?.map((element: OrganizerType) => {
        organizerIds.push(element.organizerId);
        if (!element.pointGotFlg) {
          organizerIdsAchieve.push(element.organizerId);
        }
      });
      setOrganizerIdArr(organizerIdsAchieve);
      missionAPI
        .quizMissionDetail({
          missionId: itemMissionRef.current?.missionId,
          missionOrganizerIdList: organizerIds,
        })
        .then((res) => {
          if (res) {
            setContentDetail(res);
            categoryRef.current = res.category;
            if (res.pointInfoList) {
              const allIdsZero: boolean = res.pointInfoList.every(
                (item) =>
                  (item.correctPoint === 0 || !item.correctPoint) &&
                  (item.incorrectPoint === 0 || !item.incorrectPoint),
              );
              setIsPointShow(allIdsZero);
            }
          }
        });
    }
  }, [missionInfo]);
  const getBadgeLab = () => {
    switch (categoryRef.current) {
      case CATEGORY_TYPE.CATEGORY_TYPE_IMPROVEMENT:
        return { text: APP_TEXT.MISSION_PAGE.JUDGMENT, color: 'bg-[#197A4B]' };
      case CATEGORY_TYPE.CATEGORY_TYPE_COLUMN:
        return { text: APP_TEXT.MISSION_PAGE.COLUMN, color: 'bg-[#4457D1]' };
      case CATEGORY_TYPE.CATEGORY_TYPE_VIDEO:
        return { text: APP_TEXT.MISSION_PAGE.VIDEO, color: 'bg-[#C43089]' };
      case CATEGORY_TYPE.CATEGORY_TYPE_HEALTH:
        return { text: APP_TEXT.MISSION_PAGE.HEALTH_GENERAL, color: 'bg-[#C2560E]' };
      default:
        return {};
    }
  };
  const openQuizDialog = (val: string) => {
    setQuizAlertItem({ quizType: contentDetail?.quizType, text: val });
    setOpenDialog(true);
  };

  const confirmationConfirm = () => {
    setOpenDialog(false);
    missionAPI
      .quizAchieveUpdate({
        missionId: contentDetail?.missionId,
        quizId: contentDetail?.quizId,
        selectedOption: quizAlertItem?.text,
        missionOrganizerIdList: organizerIdArr,
      })
      .then((res) => {
        if (res) {
          contentTypeRef.current = { ...res, quizType: contentDetail?.quizType };
          setOpenPointDialog(true);
        }
      });
  };

  const confirmationCancel = () => {
    setOpenDialog(false);
  };

  const pointDialogCancel = () => {
    setOpenPointDialog(false);

    if (itemMissionRef.current?.origin === 'home') {
      router.push('/home');
    } else if (itemMissionRef.current?.origin === 'mission') {
      router.push('/mission');
    } else {
      router.push(`/mission/list?data=${categoryRef.current}`);
    }
  };

  return (
    <div>
      <TopBar
        title={APP_TEXT.MISSION_PAGE.DETIAL_TITLE}
        className="shadow-none"
        onBack={() => {
          if (missionInfo.origin === 'mission') {
            router.push('/mission');
          } else if (missionInfo.origin === 'home') {
            router.push('/home');
          } else if (missionInfo.origin === 'score') {
            router.push('/health-score');
          } else {
            const str = missionInfo.origin;
            const result = str.split('-')[1] || ''; // 如果没找到 '-'，返回空字符串
            router.push(`/mission/list?data=${result}`);
          }
        }}
      />
      {finishFlg && (
        <div>
          <div
            className="text-[#666666] bg-background  w-full flex text-base font-bold py-2 z-50 justify-center fixed left-0 right-0 top-[48px] h-[40px]"
            style={{ top: `${top + 48}px` }}
          >
            <span>
              {/* このミッションはすでに達成済みです */}
              {APP_TEXT.MISSION_PAGE.ACQUIRED_AFTER_TOMORROW}
            </span>
          </div>
          <div className="w-full h-[40px]" />
        </div>
      )}

      <div className=" relative bg-white h-hull w-full" style={{ height: 'calc(100vh - 109px)' }}>
        <div
          className="flex flex-col items-start bg-white w-full pt-6 mb-18 px-6"
          // style={{ bottom: `${60 + bottom}px` }}
          style={{
            // height: `calc(100vh - ${bottom + top + 48 + 61 + 65}px)`,
            // height: 'calc(100vh - 235px)',
            overflow: 'auto',
            paddingBottom: `${70 + bottom}px`,
          }}
        >
          <div className="w-full">
            <Badge
              className={`${getBadgeLab().color} font-bold text-sm text-white px-2 py-[2px] mb-2`}
            >
              {getBadgeLab().text}
            </Badge>
            <div className="text-xl font-bold mb-2">{contentDetail?.missionTitle}</div>
            {itemMissionRef.current?.iconType === ICON_TYPE.ICON_POINTS_INACTIVE &&
              itemMissionRef.current?.pointLimitFlg !== point_Limit_Type.HAVE && (
                <div className="flex pb-4">
                  <div className="text-sm font-normal text-[#666666]">
                    {APP_TEXT.MISSION_PAGE.DETIAL_TOMORROW}
                  </div>
                  <div className="text-sm rounded-sm font-bold px-1 bg-primary-5 text-primary ml-2">
                    {APP_TEXT.MISSION_PAGE.POIONT_COUNT}
                  </div>
                </div>
              )}
            {itemMissionRef.current?.iconType !== ICON_TYPE.ICON_POINTS_NO && !isPointShow && (
              <div className="px-4 w-full bg-primary-5 rounded-2xl pt-1 pb-2 ">
                <div className=" divide-y divide-[#B2B2B2]">
                  {contentDetail?.pointInfoList?.map((item: PointInfoQuizType, i: number) => {
                    const shouldRender =
                      Number(item.correctPoint) > 0 || Number(item.incorrectPoint) > 0;
                    return (
                      shouldRender && (
                        <Points
                          key={i}
                          itemPoint={item}
                          groupFlag={contentDetail?.pointInfoList?.length === 1}
                        />
                      )
                    );
                  })}
                </div>
              </div>
            )}
            <div className="w-full flex justify-between items-center mt-2">
              <Progress
                value={progressValue(
                  itemMissionRef.current?.achievedDays,
                  itemMissionRef.current?.targetDays,
                )}
                className="bg-primary-10"
                indicatorClassName="duration-700 bg-primary-light"
              />
              <div className="text-xs text-black ml-1">
                {itemMissionRef.current?.achievedDays}/{itemMissionRef.current?.targetDays}
              </div>
            </div>
            <div
              className="mt-5 w-full bg-white "
              style={{
                overflow: 'auto',
                paddingBottom: `${contentDetail?.quizType === QUIZ_TYPE.SINGLE_CHOICE ? 120 : 70 + bottom}px`,
              }}
            >
              {contentDetail?.question && <div>{contentDetail.question}</div>}
              {contentDetail?.quizType === QUIZ_TYPE.SINGLE_CHOICE && (
                <div>
                  {contentDetail?.optionList?.map((item, i: number) => (
                    <div key={i}>
                      {/* {item.key}
                      <span className="mx-1">:</span> */}
                      <span>{item.value}</span>
                    </div>
                  ))}
                </div>
              )}
              {contentDetail?.image && (
                <div className="my-4  w-full">
                  <img
                    className="w-full"
                    src={contentDetail?.image}
                    alt="画像のロードに失敗しました"
                    width={181}
                    height={132}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
        <div
          className="w-full bg-white fixed left-0 right-0 pb-6 "
          style={{ bottom: `${60 + bottom}px` }}
        >
          {finishFlg ? (
            <div className="px-6">
              <Button
                disabled={true}
                className="bg-primary my-2 text-white  font-bold h-12  rounded-3xl w-full"
                type="button"
              >
                <div className="flex jutify-center items-center">
                  <Check className="w-5" />
                  {APP_TEXT.MISSION_PAGE.TODAY_ACHIEVED}
                </div>
              </Button>
            </div>
          ) : itemMissionRef.current?.iconType === ICON_TYPE.ICON_POINTS_INACTIVE ? (
            <>
              <div className="px-6">
                <Button
                  disabled={true}
                  className="bg-primary my-2 text-white  font-bold h-12  rounded-3xl w-full"
                  type="button"
                >
                  <div className="flex jutify-center items-center">
                    {APP_TEXT.MISSION_PAGE.ACHIEVEMENT_DESCRIBE_NO}
                  </div>
                </Button>
              </div>
            </>
          ) : (
            <div className="bg-white  bottom-0 px-3  pt-4 w-full">
              {contentDetail?.quizType === QUIZ_TYPE.SINGLE_CHOICE ? (
                <div className="grid grid-cols-2 gap-4 px-3">
                  {contentDetail?.optionList?.map((item, i: number) => (
                    <Button
                      key={i}
                      className="text-xl bg-white text-primary  font-bold h-[40px]  rounded-lg border-primary"
                      variant="outline"
                      type="button"
                      onClick={() => {
                        openQuizDialog(item.key);
                      }}
                    >
                      {item.key}
                    </Button>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4 px-3">
                  <Button
                    className="[&_svg]:size-6 text-xl bg-white text-primary  font-bold h-[40px]  rounded-lg border-primary"
                    variant="outline"
                    type="button"
                    onClick={() => {
                      openQuizDialog('1');
                    }}
                  >
                    <CircleIcon />
                  </Button>
                  <Button
                    className="[&_svg]:size-6 text-xl bg-white text-primary  font-bold h-[40px]  rounded-lg border-primary"
                    variant="outline"
                    type="button"
                    onClick={() => {
                      openQuizDialog('2');
                    }}
                  >
                    <Cross2Icon />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {openPointDialog && (
        <AlertDialogMission
          open={openPointDialog}
          popupQuizContent={contentTypeRef.current}
          onCancel={() => pointDialogCancel()}
        />
      )}
      <QuizaAlertDialog
        open={openDialog}
        quizAlertItem={quizAlertItem}
        onConfirm={() => confirmationConfirm()}
        onCancel={() => confirmationCancel()}
      />
    </div>
  );
}
