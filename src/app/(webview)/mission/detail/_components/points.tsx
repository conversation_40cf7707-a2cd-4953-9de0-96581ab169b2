'use client';

import { APP_TEXT } from '@/const/text/app';
import type { MissionPointInfoType } from '@/types/mission';
import { formatNumber } from '../../_utils/data-convert';

export default function MissionDetailPoints({
  itemPoint,
  groupFlag,
}: { itemPoint: MissionPointInfoType; groupFlag: boolean }) {
  return (
    <div className="pt-3">
      {!groupFlag && (
        <div>
          <div className="flex text-base font-normal  items-center ">
            <div className="w-[4px] text-base font-normal mr-2 h-5  bg-primary-soft" />
            {itemPoint.organizerName}
          </div>
        </div>
      )}
      <div className="py-2 flex justify-between items-center">
        <div className="flex">
          <img className="w-[20px] mr-2" src="/images/misson/vector.svg" alt="mission-vector" />
          <p className="text-base font-normal text-black">{APP_TEXT.MISSION_PAGE.BONUS_COUNT}</p>
        </div>
        <div className="flex items-center">
          <div className="flex items-center">
            <span className="text-primary text-xl font-bold pr-[2px]">
              {formatNumber(itemPoint.dailyPoint)}
            </span>
            <p>p</p>
          </div>
          {itemPoint.achievePoint > 0 && (
            <p className=" text-base ">
              <span className="mx-1">/</span>
              {APP_TEXT.MISSION_PAGE.BONUS}
            </p>
          )}
        </div>
        {itemPoint.achievePoint > 0 && (
          <div>
            <span className="text-primary text-xl font-bold pr-[2px]">
              {formatNumber(itemPoint.achievePoint)}
            </span>
            <span className="text-base ">p</span>
          </div>
        )}
      </div>
    </div>
  );
}
