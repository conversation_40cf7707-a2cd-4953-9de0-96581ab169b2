import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/store/auth';
import type { WalkingCourse } from '@/types/walking-course';
import { distanceFormat } from '@/utils/distance-format';
import { MapPin } from 'lucide-react';
import { CourseInfoTag, CoursePointInfoTag } from '../../_components/tag';

export default function WalkingCourseCard({
  walkingCourse,
  className,
  onClick,
}: { walkingCourse: WalkingCourse; className?: string; onClick?: () => void }) {
  //0:未挑戦、1:挑戦中、2:挑戦済
  const renderChallengeStatusBadge = () => {
    switch (walkingCourse.challengeStatus) {
      case 1:
        return (
          <Badge variant="default" className="font-xs px-1 h-6">
            挑戦中
          </Badge>
        );
      case 2:
        return (
          <Badge variant="default" className="font-xs px-1 h-6 bg-gray-60">
            挑戦済
          </Badge>
        );
      default:
        return null;
    }
  };

  const renderRechallengeAllowed = () => {
    return walkingCourse.rechallengeAllowed
      ? `複数回挑戦可能(${renderRechallengeInterval()})`
      : '1回のみ挑戦可能';
  };

  //0:日、1:周、2:月
  const renderRechallengeInterval = () => {
    switch (walkingCourse.rechallengeInterval) {
      case 0:
        return '1日1回';
      case 1:
        return '1週1回';
      case 2:
        return '1月1回';
      default:
        return '';
    }
  };

  const canShowPoint = () => {
    return walkingCourse.stampPoints !== 0 || walkingCourse.completeBonus !== 0;
  };

  const isMultiOrg = useAuthStore().getIsMultiOrg();
  return (
    <Card className={`py-2.5 border-b  rounded-none transition-shadow cursor-pointer ${className}`}>
      <div className="flex gap-2" onClick={onClick}>
        {/* 固定サイズの画像 */}
        <div className="w-20 flex-shrink-0 flex flex-col gap-1 items-center">
          <div className="w-[68px] h-[68px] flex  items-center justify-center rounded overflow-hidden">
            <img
              className="w-[68px] h-[68px] object-cover rounded"
              src={walkingCourse.imagePath || '/images/walking-course/walking-course-default.png'}
              alt={walkingCourse.courseName}
              onError={(e) => {
                // 画像の読み込みに失敗した場合の処理
                e.currentTarget.src = '/images/walking-course/empty.svg';
              }}
            />
          </div>
          <div className="flex items-center justify-center gap-0 text-muted-foreground text-xs">
            <MapPin className="w-3 h-3" />
            {distanceFormat(walkingCourse.distanceFromHere)}
          </div>
        </div>

        {/* 活動信息 */}
        <div className="flex-1 min-w-0 flex flex-col gap-1">
          <p className="font-bold mb-0 text-lg line-clamp-2 leading-6 min-h-[1.5rem]">
            {walkingCourse.courseName}
          </p>

          <div className="flex gap-1">
            {renderChallengeStatusBadge()}
            <div
              className={'flex items-center rounded-sm text-xs bg-primary-5 px-1 text-primary h-6'}
            >
              {renderRechallengeAllowed()}
            </div>
          </div>
          {canShowPoint() && (
            <CoursePointInfoTag walkingCourse={walkingCourse} className="text-xs text-nowrap" />
          )}
          <CourseInfoTag walkingCourse={walkingCourse} className="h-6 text-xs text-nowrap" />
        </div>
      </div>
    </Card>
  );
}
