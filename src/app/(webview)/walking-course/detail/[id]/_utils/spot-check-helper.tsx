import type { StampSpotResponse, WalkingCourseDetailResponse } from '@/types/walking-course';

export type SpotCheckStatus = {
  spotId: number;
  canCheck: boolean;
};

/**
 * WalkingCourseDetailResponse から各 spot の canCheck 状態リストを生成する関数
 */
export function getSpotCheckStatusList(data?: WalkingCourseDetailResponse): SpotCheckStatus[] {
  if (!data || !data.stampSpotList) return [];

  // stampSpotList から全ての spotId を抽出
  const allSpots: number[] = data.stampSpotList.map((spot: StampSpotResponse) => spot.spotId);

  // participations が存在しない場合は全て canCheck: true
  if (!data.participations || data.participations.length === 0) {
    return allSpots.map((spotId) => ({ spotId, canCheck: true }));
  }

  // participations が存在する場合、challengeCount が最大の participation を取得
  const maxChallengeCount = Math.max(...data.participations.map((p) => p.challengeCount));
  const latestParticipation = data.participations.find(
    (p) => p.challengeCount === maxChallengeCount,
  );
  if (!latestParticipation) {
    return allSpots.map((spotId) => ({ spotId, canCheck: true }));
  }

  // 最新の participation が完了している場合
  if (latestParticipation.isCompleted) {
    // canRechallenge が true なら全て canCheck: true
    if (data.canRechallenge) {
      return allSpots.map((spotId) => ({ spotId, canCheck: true }));
    }
    // canRechallenge が false なら全て canCheck: false
    return allSpots.map((spotId) => ({ spotId, canCheck: false }));
  }

  // 最新の participation が未完了の場合
  return allSpots.map((spotId) => {
    const stamp = latestParticipation.stampList.find((s) => s.spotId === spotId);

    // stamp が存在しない場合も canCheck: true とする
    if (!stamp) {
      return { spotId, canCheck: true };
    }

    return {
      spotId,
      canCheck: !stamp.isChecked, // isChecked が false または undefined の場合 true
    };
  });
}
