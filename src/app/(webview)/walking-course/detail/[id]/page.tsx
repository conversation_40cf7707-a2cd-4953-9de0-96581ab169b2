'use client';

import { walkingCourseAPI } from '@/api/modules/walking-course';
import SlidePage from '@/components/layout/silde-page';
import { Button } from '@/components/shared/button';
import MapComponent from '@/components/shared/map/map';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useGlobalVar } from '@/hooks/use-global-var';
import { useMap } from '@/hooks/use-map';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { type Connection, type Marker, MarkerType } from '@/types/map';
import { ChevronLeftIcon } from '@radix-ui/react-icons';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { StampSpotProvider, useStampSpotState } from '../../_context/use-stamp-spot-state';
import { CourseDetailDrawer } from './_components/course-detail';
import { FixedCourseCard } from './_components/fixed-course-card';
import { StampPointDone } from './_components/stamp-point-done';
import { StampSpotCardSlideDrawer } from './_components/stamp-spot-card-slide-drawer';
import { StampSpotCardSlideOverMap } from './_components/stamp-spot-card-slide-over-map';
import { getSpotCheckStatusList } from './_utils/spot-check-helper';

// メインコンテンツコンポーネント
const WalkingCourseDetailContent = ({ id }: { id: string }) => {
  const router = useRouter();
  const safeArea = useSafeArea();
  const { setFooterMenuSettingOptions } = useGlobalVar();
  const backButtonTop = safeArea.top + 16;
  const { mapRef } = useMap();
  const { location } = useGeolocation();
  const {
    showCourseDetail,
    showStampSpotCardSlideOverMap,
    showStampSpotCardSlideDrawer,
    showStampPointDoneSlidePage,
    stampSpotCardSlideOverMapCurrentStampId,
    setShowStampSpotCardSlideOverMap,
    setShowStampPointDoneSlidePage,
    latestStampPointCheckedTime,
    setShowCourseDetail,
    setStampSpotCardSlideOverMapCurrentStampId,
    setShowStampSpotCardSlideDrawer,
  } = useStampSpotState();

  useEffect(() => {
    setFooterMenuSettingOptions({ isShow: false });
    return () => {
      setFooterMenuSettingOptions({ isShow: true });
    };
  }, []);

  const { data: courseData } = useQuery({
    queryKey: ['walkingCourseDetail', id, latestStampPointCheckedTime],
    queryFn: () => walkingCourseAPI.walkingCourseDetail(id),
    enabled: !!id,
  });

  const handleClickMarker = (marker: Marker) => {
    console.log(marker);
    setShowCourseDetail(false);
    setStampSpotCardSlideOverMapCurrentStampId(String(marker.id));
    setShowStampSpotCardSlideOverMap(true);
  };

  const handleClickLine = () => {
    setShowCourseDetail(true);
    setShowStampSpotCardSlideOverMap(false);
    setShowStampSpotCardSlideDrawer(false);
    setStampSpotCardSlideOverMapCurrentStampId(null);
  };

  useEffect(() => {
    if (showCourseDetail) {
      document.body.style.overflow = 'hidden';
      document.body.style.height = '100vh';
      // clear vaul pointer events
      setTimeout(() => {
        document.body.style.pointerEvents = '';
      }, 200);

      console.log('mapRef', mapRef.current);
      if (mapRef.current) {
        mapRef.current.panBy(0, 200);
      }
    } else {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
      if (mapRef.current) {
        mapRef.current.panBy(0, -200);
      }
    }
    return () => {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
    };
  }, [showCourseDetail, mapRef.current]);

  const userLocationMarker = useMemo<Marker>(() => {
    return {
      id: 'user-location',
      name: '自分の位置',
      type: MarkerType.USER_LOCATION,
      latitude: location.lat.toString(),
      longitude: location.lng.toString(),
    };
  }, [location]);

  const markers = useMemo(() => {
    const list: Marker[] | undefined =
      courseData?.stampSpotList?.map((spot) => ({
        id: spot.spotId.toString(),
        name: spot.spotName,
        type: MarkerType.WALKING_COURSE,
        address: spot.address,
        latitude: spot.latitude,
        longitude: spot.longitude,
        imagePath: spot.imagePath,
        isCurrent: String(spot.spotId) === stampSpotCardSlideOverMapCurrentStampId,
      })) || [];

    return list;
  }, [courseData, stampSpotCardSlideOverMapCurrentStampId]);

  const lines = useMemo(() => {
    const list: Connection[] | undefined = courseData?.courseRouteList?.map((route) => ({
      id: String(courseData.courseId || ''),
      path: route.path.map((point) => ({
        lat: Number(point.lat),
        lng: Number(point.lng),
      })),
    }));
    return list || [];
  }, [courseData]);

  return (
    <div className="relative h-[100vh]">
      <Button
        onClick={() => router.back()}
        variant="secondary"
        size="sm"
        style={{ top: backButtonTop, boxShadow: '4px 8px 16px 0px rgba(0, 0, 0, 0.25)' }}
        className="absolute left-4 z-[1] px-6 gap-0"
      >
        <ChevronLeftIcon className="h-[22px] w-[22px]" />
        前の画面に戻る
      </Button>
      <MapComponent
        markers={markers}
        userLocationMarker={userLocationMarker}
        lines={lines}
        onMarkerClick={(marker: Marker) => {
          handleClickMarker(marker);
        }}
        onLineClick={() => {
          handleClickLine();
        }}
      />
      <CourseDetailDrawer show={showCourseDetail} data={courseData} />
      <StampSpotCardSlideOverMap
        courseName={courseData?.courseName || ''}
        show={showStampSpotCardSlideOverMap}
        data={courseData?.stampSpotList || []}
        canCheckList={getSpotCheckStatusList(courseData)}
        organizerNm={courseData?.organizerNm}
      />
      <StampSpotCardSlideDrawer
        data={courseData?.stampSpotList || []}
        canCheckList={getSpotCheckStatusList(courseData)}
        courseName={courseData?.courseName || ''}
        show={showStampSpotCardSlideDrawer}
        organizerNm={courseData?.organizerNm}
      />
      <StampPointDoneSlidePage courseId={id} />
      <FixedCourseCard />
    </div>
  );
};

// メインページコンポーネント
export default function WalkingCourseDetailPage({ params }: { params: { id: string } }) {
  return (
    <StampSpotProvider>
      <WalkingCourseDetailContent id={params.id} />
    </StampSpotProvider>
  );
}

function StampPointDoneSlidePage({ courseId }: { courseId: string }) {
  const { showStampPointDoneSlidePage, setShowStampPointDoneSlidePage } = useStampSpotState();
  return (
    <>
      <SlidePage
        title={''}
        hideTopBar={true}
        content={
          <StampPointDone
            courseId={courseId}
            onClose={() => setShowStampPointDoneSlidePage(false)}
            onGoDetail={() => {
              setShowStampPointDoneSlidePage(false);
            }}
          />
        }
        isOpen={showStampPointDoneSlidePage}
        enableClose={true}
        enableBack={false}
        slideFrom={'bottom'}
      />
    </>
  );
}
