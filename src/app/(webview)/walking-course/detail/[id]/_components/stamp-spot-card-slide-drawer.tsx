import { Button } from '@/components/shared/button';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeaderWithMoreButton,
  DrawerTitle,
} from '@/components/shared/drawer';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  useCarousel,
} from '@/components/ui/carousel';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { StampSpotResponse } from '@/types/walking-course';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useStampSpotState } from '../../../_context/use-stamp-spot-state';
import type { SpotCheckStatus } from '../_utils/spot-check-helper';
import { StampCard } from './stamp-spot-card';

interface StampSpotCardSlideDrawerProps {
  data: StampSpotResponse[];
  courseName: string;
  show: boolean;
  canCheckList: SpotCheckStatus[];
  organizerNm?: string;
}

export function StampSpotCardSlideDrawer({
  data,
  courseName,
  show,
  canCheckList,
  organizerNm,
}: StampSpotCardSlideDrawerProps) {
  const {
    setShowStampSpotCardSlideDrawer,
    setShowCourseDetail,
    setStampSpotCardSlideOverMapCurrentStampId,
  } = useStampSpotState();
  const handleClose = () => {
    setShowStampSpotCardSlideDrawer(false);
    setStampSpotCardSlideOverMapCurrentStampId(null);
  };
  const handleBack = () => {
    setShowCourseDetail(true);
    setShowStampSpotCardSlideDrawer(false);
    setStampSpotCardSlideOverMapCurrentStampId(null);
  };
  return (
    <Drawer open={show} onOpenChange={setShowStampSpotCardSlideDrawer}>
      <DrawerContent>
        <DrawerTitle className="sr-only">{courseName}</DrawerTitle>
        <DrawerDescription className="sr-only">Stamp Drawer</DrawerDescription>
        <DrawerHeaderWithMoreButton
          title={courseName}
          description={'Stamp Drawer'}
          enableBack={true}
          enableClose={true}
          onBack={handleBack}
          onClose={handleClose}
        />
        <Carousel
          opts={{
            align: 'center',
            loop: false,
          }}
          orientation="horizontal"
          className="w-full"
        >
          <Content
            data={data}
            courseName={courseName}
            canCheckList={canCheckList}
            organizerNm={organizerNm}
          />
        </Carousel>
      </DrawerContent>
    </Drawer>
  );
}

function Content({
  data,
  courseName,
  canCheckList,
  organizerNm,
}: {
  data: StampSpotResponse[];
  courseName: string;
  canCheckList: SpotCheckStatus[];
  organizerNm?: string;
}) {
  const { scrollNext, canScrollNext, scrollPrev, canScrollPrev, api } = useCarousel();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const { bottom } = useSafeArea();
  const { stampSpotCardSlideOverMapCurrentStampId, setStampSpotCardSlideOverMapCurrentStampId } =
    useStampSpotState();

  const currentIndex = useMemo(() => {
    if (!stampSpotCardSlideOverMapCurrentStampId) {
      return -1;
    }
    return data.findIndex(
      (item) => String(item.spotId) === stampSpotCardSlideOverMapCurrentStampId,
    );
  }, [data, stampSpotCardSlideOverMapCurrentStampId]);

  useEffect(() => {
    if (!api) {
      return;
    }
    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);
    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  useEffect(() => {
    if (api && currentIndex > -1) {
      api.scrollTo(currentIndex);
    }
  }, [api, currentIndex]);

  useEffect(() => {
    const handleSelect = () => {
      if (api && data.length > 0) {
        const current = api?.selectedScrollSnap();
        const currentStamp = data[current];
        if (currentStamp) {
          setStampSpotCardSlideOverMapCurrentStampId(String(currentStamp.spotId));
        }
      }
    };
    if (api) {
      api.on('select', handleSelect);
    }
    return () => {
      if (api) {
        api.off('select', handleSelect);
      }
    };
  }, [api, data, setStampSpotCardSlideOverMapCurrentStampId]);

  return (
    <>
      <CarouselContent className="w-[calc(100vw-60px)] ml-[22px] mt-4">
        {data.map((item, index) => (
          <CarouselItem key={index} className="pb-4">
            <StampCard
              className="border border-gray-30 shadow-none"
              hideIndex={true}
              data={item}
              courseName={courseName}
              index={index}
              sum={data.length}
              canCheck={canCheckList.some((s) => s.spotId === item.spotId && s.canCheck)}
              organizerNm={organizerNm}
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <div className="flex justify-center items-center gap-2 mx-[40px] pb-4">
        <Button
          variant="icon"
          className="h-6"
          onClick={() => scrollPrev()}
          disabled={!canScrollPrev}
        >
          <ChevronLeft />
        </Button>
        <span className="flex-1 text-center">
          {current}/{count}
        </span>
        <Button
          variant="icon"
          className="h-6"
          onClick={() => scrollNext()}
          disabled={!canScrollNext}
        >
          <ChevronRight />
        </Button>
      </div>
      <div style={{ height: `${bottom}px` }} />
    </>
  );
}
