'use client';

import { Slide<PERSON>romBottom, SlideFromRight } from '@/components/shared/animation';
import { Button } from '@/components/shared/button';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { StampSpotResponse } from '@/types/walking-course';
import { X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useStampSpotState } from '../../../_context/use-stamp-spot-state';
import type { SpotCheckStatus } from '../_utils/spot-check-helper';
import { StampCard } from './stamp-spot-card';

export function StampSpotCardSlideOverMap({
  data,
  courseName,
  show,
  organizerNm,
  canCheckList,
}: {
  data: StampSpotResponse[];
  courseName: string;
  show: boolean;
  organizerNm?: string;
  canCheckList: SpotCheckStatus[];
}) {
  const { setShowStampSpotCardSlideOverMap } = useStampSpotState();
  const { bottom } = useSafeArea();
  const cardHeight = 315;
  const padding = 16;
  const buttonBottom = cardHeight + bottom + padding;
  const { stampSpotCardSlideOverMapCurrentStampId, setStampSpotCardSlideOverMapCurrentStampId } =
    useStampSpotState();
  const currentIndex = useMemo(() => {
    if (!stampSpotCardSlideOverMapCurrentStampId) {
      return -1;
    }
    return data.findIndex(
      (item) => String(item.spotId) === stampSpotCardSlideOverMapCurrentStampId,
    );
  }, [data, stampSpotCardSlideOverMapCurrentStampId]);
  const [api, setApi] = useState<CarouselApi | null>(null);

  useEffect(() => {
    if (api && currentIndex > -1) {
      api.scrollTo(currentIndex);
    }
  }, [api, currentIndex]);

  useEffect(() => {
    const handleSelect = () => {
      if (api && data.length > 0) {
        const current = api?.selectedScrollSnap();
        const currentStamp = data[current];
        if (currentStamp) {
          setStampSpotCardSlideOverMapCurrentStampId(String(currentStamp.spotId));
        }
      }
    };
    if (api) {
      api.on('select', handleSelect);
    }
    return () => {
      if (api) {
        api.off('select', handleSelect);
      }
    };
  }, [api, data]);

  const handleClose = () => {
    setShowStampSpotCardSlideOverMap(false);
    setStampSpotCardSlideOverMapCurrentStampId(null);
  };

  return (
    <>
      <SlideFromRight show={show}>
        <Button
          variant="icon"
          className="fixed right-4 w-12 h-12 bg-primary-5 shadow-lg rounded-full"
          style={{
            bottom: `${buttonBottom}px`,
          }}
          onClick={handleClose}
        >
          <X className="w-6 h-6 text-primary" />
        </Button>
      </SlideFromRight>
      <SlideFromBottom show={show}>
        <div
          className="absolute left-0 right-0"
          style={{
            bottom: `${bottom}px`,
          }}
        >
          <Carousel
            opts={{
              align: 'center',
              loop: false,
            }}
            orientation="horizontal"
            className="w-full"
            setApi={setApi}
          >
            <CarouselContent className="w-[calc(100vw-60px)] ml-[22px]">
              {data.map((item, index) => (
                <CarouselItem key={index} className="pb-4">
                  <StampCard
                    data={item}
                    courseName={courseName}
                    index={index}
                    sum={data.length}
                    organizerNm={organizerNm}
                    canCheck={canCheckList.some((s) => s.spotId === item.spotId && s.canCheck)}
                  />
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </SlideFromBottom>
    </>
  );
}
