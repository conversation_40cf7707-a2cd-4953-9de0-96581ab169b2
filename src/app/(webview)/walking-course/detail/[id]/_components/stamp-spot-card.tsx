import { walkingCourseAPI } from '@/api/modules/walking-course';
import { PointIcon } from '@/app/(webview)/walking-course/_components/svg-icon';
import { Button } from '@/components/shared/button';
import { Distance } from '@/components/shared/distance';
import { TextButton } from '@/components/shared/text-button';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { useParams } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import { calculateDistance } from '@/lib/utils';
import { useAuthStore } from '@/store/auth';
import type { StampSpotResponse } from '@/types/walking-course';
import { Check, ChevronRight } from 'lucide-react';
import { useMemo } from 'react';
import { useStampSpotState } from '../../../_context/use-stamp-spot-state';
export function StampCard({
  data,
  index,
  sum,
  hideIndex,
  courseName,
  className,
  canCheck = true,
  organizerNm,
}: {
  data: StampSpotResponse;
  index: number;
  sum: number;
  courseName: string;
  hideIndex?: boolean;
  className?: string;
  canCheck?: boolean;
  organizerNm?: string;
}) {
  const params = useParams();
  const courseId = params?.id as string;

  const { setLoading } = useLoading();
  const { location } = useGeolocation();
  const isMultiOrg = useAuthStore().getIsMultiOrg();
  const isClosed = useMemo(() => {
    if (location) {
      return (
        // サーバ側では 60m 以内であれば true を返すようにしているが、
        // APP側では 50m 以内であれば true を返すようにしている
        calculateDistance(
          { lat: Number(data.latitude), lng: Number(data.longitude) },
          { lat: location.lat, lng: location.lng },
        ) < 50
      );
    }
    return false;
  }, [data.latitude, data.longitude, location]);

  const {
    setShowCourseDetail,
    setShowStampSpotCardSlideDrawer,
    setShowStampSpotCardSlideOverMap,
    setShowStampPointDoneSlidePage,
    setLatestStampPointCheckedTime,
  } = useStampSpotState();
  const handleStampSpotComplete = () => {
    setLoading(true, { text: 'データを送信中...' });
    walkingCourseAPI
      .recordStamp({
        courseId: Number.parseInt(courseId),
        spotId: data.spotId,
        latitude: data.latitude,
        longitude: data.longitude,
      })
      .then((response) => {
        console.log('API返回:', response);
        setTimeout(() => {
          setLatestStampPointCheckedTime(Date.now());
          setLoading(false);
          setShowStampPointDoneSlidePage(true);
        }, 2000);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  const handleOpenCourseDetail = () => {
    setShowStampSpotCardSlideDrawer(false);
    setShowStampSpotCardSlideOverMap(false);
    setShowCourseDetail(true);
  };

  return (
    <div className={cn('bg-white rounded-3xl shadow-lg py-4 px-6', className)}>
      {/* stamp spot info */}
      <div className="flex items-start mb-2">
        <div className="w-14 h-14 mr-3 rounded-md overflow-hidden flex items-center justify-center">
          <img
            src={data.imagePath || '/images/walking-course/stamp-spot-default.png'}
            alt={courseName}
            className="w-full"
          />
        </div>
        <div>
          <h2 className="font-bold">{data.spotName}</h2>
          <p className="text-xs text-gray-600">
            <Distance
              targetLocation={{ lat: Number(data.latitude), lng: Number(data.longitude) }}
            />
            ・{courseName}
          </p>
          <div className="inline-flex items-center mt-1 px-1 py-[1px] bg-[#FFFAF7] text-[#C2560E] text-xs rounded-md border border-[#FF945F]">
            <img
              src="/images/walking-course/stamp-spot-icon.png"
              alt="スタンプスポット"
              className="w-4 h-4 mr-1"
            />
            スタンプスポット
          </div>
        </div>
      </div>
      {/* stamp point info */}
      <div
        className={`flex bg-primary-5 rounded-2xl px-4 ${isMultiOrg ? 'flex-col' : 'items-center h-[46px]'}`}
      >
        {isMultiOrg && <div className="text-sm mt-5">{organizerNm}</div>}
        <div className={`flex items-center ${isMultiOrg ? 'justify-between mt-3 mb-4' : 'flex-1'}`}>
          <div className="flex items-center gap-2">
            <PointIcon />
            <span className="text-gray-700 text-sm">1スタンプ</span>
          </div>
          <div className={`flex items-center gap-0.5 ${isMultiOrg ? 'ml-auto' : ''}`}>
            <span className="text-primary font-bold text-xl">1</span>
            <span className="text-sm">p</span>
          </div>
        </div>
      </div>
      {/* スタンプ獲得ボタン */}
      <Button
        size="sm"
        className={`w-full mt-3 ${
          !canCheck
            ? 'opacity-50 cursor-not-allowed' // ✔スタンプ獲得済
            : !isClosed
              ? 'disabled:bg-gray-10 disabled:text-gray-60 disabled:cursor-not-allowed' // 近くにいません
              : ''
        }`}
        onClick={canCheck && isClosed ? handleStampSpotComplete : undefined}
        disabled={!canCheck || !isClosed}
      >
        {!canCheck ? (
          <span className="flex items-center gap-1">
            <Check className="w-4 h-4" />
            スタンプ獲得済
          </span>
        ) : isClosed ? (
          'スタンプを獲得する'
        ) : (
          '近くにいません'
        )}
      </Button>
      <TextButton className="w-full mt-4" onClick={handleOpenCourseDetail}>
        <span className="flex items-center justify-center gap-1">
          コースの詳細を見る
          <ChevronRight />
        </span>
      </TextButton>
      {!hideIndex && (
        <div className="text-center mt-3 text-xs">
          {index + 1}/{sum}
        </div>
      )}
    </div>
  );
}
