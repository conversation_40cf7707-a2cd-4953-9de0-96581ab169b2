'use client';

import { healthR<PERSON>ordAP<PERSON> } from '@/api/modules/health-record';
import { stepGoalAPI } from '@/api/modules/step-goal';
import { DeviceDataSyncService } from '@/app/other-connect/sycn-device-data';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import HealthRecordCreateButton from '@/components/shared/health-record-create-button';
import MenuPanel from '@/components/shared/menu-panel';
import { ScrollArea, ScrollBar } from '@/components/shared/scroll-area';
import { TextButton } from '@/components/shared/text-button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useMiniAppHealthCheckupLink } from '@/hooks/use-health-checkup-link';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { usePageVisibility } from '@/hooks/use-page-visibility';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useVirtualKeyboardEvent } from '@/hooks/use-virtual-keyboard-event';
import { type HealthRecord, HealthRecordSource, type ThumbnailData } from '@/types/health-record';
import { formatDate, yyyyMMddToDate } from '@/utils/date-format';
import { RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';
import bikuri_icon from '../../../../images/bikuri.png';
import { DateMenu } from './_components/date-menu';
import RecordSection from './_components/record-section';

export default function HealthRecordDetailPage() {
  const { openLink: openHealthCheckupLink } = useMiniAppHealthCheckupLink();
  const router = useRouter();
  const { setLoading } = useLoading();
  const { isShow, setDialog } = useMessageDialog();
  const safeArea = useSafeArea();
  const virtualKeyboardInfo = useVirtualKeyboardEvent();
  const { setVisibilityCallback } = usePageVisibility();
  const [scrollAreaHeight, setScrollAreaHeight] = useState<string>('');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [healthRecord, setHealthRecord] = useState<HealthRecord>({
    bodyFrom: HealthRecordSource.MANUAL,
    sleepFrom: HealthRecordSource.MANUAL,
    bloodPressureFrom: HealthRecordSource.MANUAL,
    bloodGlucoseFrom: HealthRecordSource.MANUAL,
  });
  const [thumbnailData, setThumbnailData] = useState<ThumbnailData>({});
  const syncService = new DeviceDataSyncService();

  const fetchHealthRecord = () => {
    const date = formatDate(currentDate, 'yyyyMMdd');
    setLoading(true, { text: 'データを通信中...' });
    healthRecordAPI
      .dailyVitalData({ measureDate: date })
      .then((res) => {
        if (res.vitalData) {
          const data = res.vitalData;
          data.date = formatDate(yyyyMMddToDate(res.measureDate), 'yyyy-MM-dd');
          setHealthRecord(data);
        }
      })
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
        }, 500);
      });
    healthRecordAPI.thumbnailData({ measureDate: date }).then((res) => {
      if (res) {
        setThumbnailData(res);
      }
    });
  };

  useEffect(() => {
    const topbarHeight = 48;
    const footerHeight = 61;
    const otherHeight = topbarHeight + safeArea.top + safeArea.bottom + footerHeight;
    setScrollAreaHeight(`${virtualKeyboardInfo.currentHeight - otherHeight}px`);
  }, [safeArea, virtualKeyboardInfo]);

  function syncHealthRecord() {
    setLoading(true, { text: 'データを同期中...' });
    syncService
      .syncAllData(1)
      .then(() => {
        setTimeout(() => {
          setLoading(false);
          fetchHealthRecord();
        }, 1500);
      })
      .catch((err) => {
        setLoading(false);
        if (err === APP_TEXT.HEALTH_RECORD_PAGE.SYNC_TIMEOUT) {
          showSyncError();
        }
        console.error('device sync fail', err);
      });
  }

  const showSyncError = () => {
    setDialog(true, {
      content: (
        <div>
          <div className="flex justify-center items-center mb-4">
            <img className="h-16 w-16" alt="desc" src={bikuri_icon.src} />
          </div>
          <div className="text-[16px] font-bold mb-2">
            {APP_TEXT.HEALTH_RECORD_PAGE.SYNC_TIMEOUT_TITLE}
          </div>
          <div className="text-[14px] text-gray-400">
            {APP_TEXT.HEALTH_RECORD_PAGE.SYNC_TIMEOUT}
          </div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="default"
            className="w-full font-bold shadow-primary! bg-primary text-white"
            onClick={() => {
              setDialog(false);
            }}
          >
            {APP_TEXT.DATA_SRCSET.PERMISSION_OK}
          </Button>
        </div>
      ),
    });
  };

  useEffect(() => {
    fetchHealthRecord();
  }, [currentDate]);

  // ページが表示された時（APP切り替え後など）にデータを再取得
  useEffect(() => {
    setVisibilityCallback(() => {
      syncHealthRecord();
    });
  }, []);

  const handleTarget = () => {
    setLoading(true, { text: 'データを通信中...' });
    stepGoalAPI
      .getStepTargetInfo()
      .then((response) => {
        setLoading(false);
        if (response.targetPlan === undefined) {
          router.push(ROUTES.GOAL.INTRO);
        } else {
          router.push(ROUTES.GOAL.GOAL);
        }
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        return Promise.reject();
      });
  };

  const OTHER_RECORD_MENU_ITEMS = [
    {
      label: APP_TEXT.HEALTH_RECORD_PAGE.HEALTH_CHECKUP,
      onClick: () => {
        openHealthCheckupLink();
      },
      href: '',
    },
    {
      label: APP_TEXT.HEALTH_RECORD_PAGE.HEALTH_CHECKUP_RESULT,
      href: '/health-checkup',
    },
    {
      label: APP_TEXT.HEALTH_RECORD_PAGE.MEDICATION_RECORD,
      href: '/medication-record',
    },
  ];

  const SETTING_MENU_ITEMS = [
    {
      label: APP_TEXT.HEALTH_RECORD_PAGE.WALKING_TARGET,
      onClick: () => {
        handleTarget();
      },
      href: '',
    },
    {
      label: APP_TEXT.HEALTH_RECORD_PAGE.DATA_SYNC_SETTING,
      href: ROUTES.DATA_CONNECT.MAIN,
    },
  ];

  return (
    <>
      <TopBar title={APP_TEXT.HEALTH_RECORD_PAGE.TITLE} />
      <ScrollArea style={{ height: scrollAreaHeight }} type="hover">
        <ScrollBar orientation="vertical" />
        <DateMenu
          className=" pt-5 pb-4"
          date={currentDate}
          onDateChange={(date) => setCurrentDate(date)}
        />
        <div className="flex justify-end px-6">
          <TextButton
            size="sm"
            onClick={() => {
              syncHealthRecord();
            }}
          >
            <RefreshCw className="w-[18px] h-[18px]" />
            {APP_TEXT.HEALTH_RECORD_PAGE.SYNC}
          </TextButton>
        </div>
        <RecordSection.Step
          className="mt-1"
          data={healthRecord}
          thumbnail={thumbnailData}
          date={formatDate(currentDate, 'yyyy-MM-dd')}
          // onClick={() => {
          //   router.push('/graph?tab=step');
          // }}
        />
        <RecordSection.Weight
          data={healthRecord}
          thumbnail={thumbnailData}
          onClick={() => {
            router.push(`/graph?tab=weightBmi&date=${formatDate(currentDate, 'yyyy-MM-dd')}`);
          }}
        />
        <RecordSection.SleepTime
          data={healthRecord}
          thumbnail={thumbnailData}
          onClick={() => {
            router.push(`/graph?tab=sleep&date=${formatDate(currentDate, 'yyyy-MM-dd')}`);
          }}
        />
        <RecordSection.BloodPressure
          data={healthRecord}
          thumbnail={thumbnailData}
          onClick={() => {
            router.push(`/graph?tab=bloodPressure&date=${formatDate(currentDate, 'yyyy-MM-dd')}`);
          }}
        />
        <RecordSection.BloodGlucose
          data={healthRecord}
          thumbnail={thumbnailData}
          className="mb-0"
          onClick={() => {
            router.push(`/graph?tab=bloodGlucose&date=${formatDate(currentDate, 'yyyy-MM-dd')}`);
          }}
        />
        {/* <MenuPanel
          title={APP_TEXT.HEALTH_RECORD_PAGE.OTHER_RECORD}
          menuItems={OTHER_RECORD_MENU_ITEMS}
        /> */}
        <MenuPanel
          className="mb-4"
          title={APP_TEXT.HEALTH_RECORD_PAGE.SETTING}
          menuItems={SETTING_MENU_ITEMS}
        />
        <div className="h-16" />
        <HealthRecordCreateButton
          date={formatDate(currentDate, 'yyyy-MM-dd')}
          onDataUpdated={() => {
            setTimeout(() => {
              fetchHealthRecord();
            }, 2000);
          }}
        />
      </ScrollArea>
    </>
  );
}
