'use client';
import TopBar from '@/components/layout/top-bar';
import { TextButton } from '@/components/shared/text-button';
import { APP_TEXT } from '@/const/text/app';
import { useSafeArea } from '@/hooks/use-safe-area';
import { ChevronDown } from 'lucide-react';
import { useRef, useState } from 'react';
import { RANKING_HINT_TITLE } from '../_const';

export default function RnkingHintPage() {
  const safeArea = useSafeArea();
  const safeTop = safeArea.top ?? 0;
  const pointRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  // Dynamically get or create ref
  const getPointRef = (value: string) => {
    return (element: HTMLDivElement | null) => {
      if (element) {
        pointRefs.current[value] = element;
      }
    };
  };

  // Scroll to the specified value element
  const scrollToPointGet = (value: string) => {
    const element = pointRefs.current[value];
    if (element) {
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - 190;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  return (
    <div>
      <TopBar title={APP_TEXT.POINT.HINT_PAGE} />

      {/* click title link to position */}
      <div className="px-6 pb-6 bg-white">
        <div className="fixed top-0 left-0 w-full bg-white pl-6" style={{ paddingTop: safeTop }}>
          <div className="mt-[48px]" style={{ height: safeTop }} />
          {RANKING_HINT_TITLE.map((item) => (
            <div key={item.value} className="flex items-center mb-4">
              <TextButton
                onClick={() => scrollToPointGet(item.value)}
                className="text-button text-[15px]"
              >
                {item.name}
                <ChevronDown className="" />
              </TextButton>
            </div>
          ))}
        </div>

        <div style={{ paddingTop: `${safeTop + 120}px` }} />
        <div className="font-bold text-xl " ref={getPointRef('1')}>
          歩数ランキング
        </div>
        <div className="font-normal text-base mt-6">
          <div className="font-bold text-base">集計期間</div>
          <div className="font-normal text-base mt-2 whit whitespace-pre-wrap">
            {'日：0:00~23:59\n週：月曜日〜日曜日'}
          </div>
        </div>
        <div className="font-bold text-base mt-6">ランキング順位算出</div>
        <div className="font-normal text-base mt-2">
          歩数の多い順
          <br />
          歩数が同じユーザーは同順位
        </div>
        {/* <div className="font-normal text-sm text-muted-foreground mt-2">
          ※歩数が同じユーザーに関しては同順位
        </div> */}
        <img src="/images/ranking/mosaic.svg" alt="hint" className="w-full mt-2" />
        <div className="font-bold text-base mt-6">グループリセットタイミング</div>
        <div className="font-normal text-base mt-2">毎週月曜日 0時</div>
        {/* <div className="font-normal text-sm text-muted-foreground mt-2">
          ※グループリセットタイミングは毎週月曜日0:00
        </div> */}
        <div className="font-bold text-base mt-6">ランキング除外基準</div>
        <div className="font-normal text-base mt-2">歩数が0のユーザ等</div>
        <div className="border-b border-border border-solid my-8" />

        <div className="font-bold text-xl mt-6" ref={getPointRef('2')}>
          ミッション達成数ランキング
        </div>
        <div className="font-normal text-base mt-6">
          <div className="font-bold text-base">集計期間</div>
          <div className="font-normal text-base mt-2 whit whitespace-pre-wrap">
            {'日：0:00~23:59\n週：月曜日〜日曜日'}
          </div>
        </div>
        <div className="font-bold text-base mt-6">ランキング順位算出</div>
        <div className="font-normal text-base mt-2">
          ミッション達成数の多い順
          <br />
          ミッション達成数が同じユーザーは同順位
        </div>
        {/* <div className="font-normal text-sm text-muted-foreground mt-2">
          ※ミッション達成数が同じユーザーに関しては同順位
        </div> */}
        <img src="/images/ranking/mosaic.svg" alt="hint" className="w-full mt-2" />
        <div className="font-bold text-base mt-6">ランキング除外基準</div>
        <div className="font-normal text-base mt-2">ミッション達成回数が0のユーザ等</div>
        <div className="border-b border-border border-solid my-8" />

        <div className="font-bold text-xl mt-6" ref={getPointRef('3')}>
          団体ランキング
        </div>
        <div className="font-normal text-base mt-6">
          <div className="font-bold text-base">集計期間</div>
          <div className="font-normal text-base mt-2 whit whitespace-pre-wrap">月曜日〜日曜日</div>
        </div>
        <div className="font-bold text-base mt-6">ランキング順位算出</div>
        <div className="font-normal text-base mt-2">
          歩数の多い順
          <br />
          歩数が同じユーザーは同順位
        </div>
        {/* <div className="font-normal text-sm text-muted-foreground mt-2">
          ※平均歩数が同じ所属団体は同順位
        </div> */}
        <img src="/images/ranking/mosaic.svg" alt="hint" className="w-full mt-2" />
        <div className="font-bold text-base mt-6">ランキング除外基準</div>
        <div className="font-normal text-base mt-2">歩数が0のユーザ等</div>
      </div>
    </div>
  );
}
