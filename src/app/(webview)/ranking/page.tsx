'use client';
import { rankingAPI } from '@/api/modules/ranking';
import TopBar from '@/components/layout/top-bar';
import { TextButton } from '@/components/shared/text-button';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useRankingStore } from '@/store/ranking';
import type {
  CategoryList,
  CommonRequest,
  CommonResponse,
  Pagination,
  RankingList,
  UserRankingInfoList,
} from '@/types/ranking';
import { endOfWeek, format } from 'date-fns';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { isEmptyData } from '../point/_utils';
import SwipingAreaBanner from './_components/area-banner';
import CarouselContentPage from './_components/carousel-content';
import FixedArea from './_components/fixed-area';
import HistoryListPage from './_components/history-list';
import HistoryScrollList from './_components/history-scroll';
import MenuTab from './_components/menu-tab';
import TabGroupPage from './_components/tab-group';
import type { DateType } from './_components/update-date';
import { UpdateDatePage } from './_components/update-date';
import {
  ALL_GROUP_TAB,
  CATEGORY_TAB,
  CATEGORY_TYPE,
  DAY_WEEK_TAB,
  GROUP_ALL_TAB,
  MYSELF_FLG,
  RANK_EXCL_FLG,
} from './_const';

export default function RankingPage() {
  // TODO: Store data
  const {
    // activeTab,
    // setActiveTab,
    // groupAllTab,
    // setGroupAllTab,
    // dateType,
    // setDateType,
    // selectDate,
    // setSelectDate,
    myselfFlg,
    setMyselfFlg,
    pagination,
    setPagination,
  } = useRankingStore();

  const { isLoading, setIsLoading } = useLoading();
  const router = useRouter();
  const [userRankingInfoList, setUserRankingInfoList] = useState<UserRankingInfoList[]>([]);
  const [activeTab, setActiveTab] = useState<number>(CATEGORY_TYPE.STEP);
  const [dateType, setDateType] = useState<string>(DAY_WEEK_TAB.WEEK);
  const [rankingInfo, setRankingInfo] = useState<CommonResponse>({});
  const [updatedAt, setUpdatedAt] = useState<string>('');
  const [rankExclFlg, setRankExclFlg] = useState<number>(RANK_EXCL_FLG.OUTSIDE);
  const [groupAllTab, setGroupAllTab] = useState<string>(GROUP_ALL_TAB.GROUP);
  const [organizationIndex, setOrganizationIndex] = useState(0);
  const [isMyselfFlg, seIsMyselfFlg] = useState<boolean>(false);
  // const [myselfFlg, setMyselfFlg] = useState<MYSELF_FLG>(MYSELF_FLG.DEFAULT);
  const [selectDate, setSelectDate] = useState<DateType>({});
  const [rankingList, setRankingList] = useState<RankingList[]>([]);
  // const [pagination, setPagination] = useState<Pagination>({
  //   total: 0,
  //   page: 1,
  //   limit: 30,
  //   pages: 1,
  // });

  const [loadingStatus, setLoadingStatus] = useState({
    isLoadingPrev: false,
    isLoadingNext: false,
  });

  const [hasMore, setHasMore] = useState({
    hasPrevPage: false,
    hasNextPage: false,
  });
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  useEffect(() => {
    getUserRankingInfoList();
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (userRankingInfoList?.length > 0) {
      fetchData({
        page: 1,
      });
    }
  }, [userRankingInfoList, organizationIndex, activeTab, groupAllTab, dateType, selectDate]);

  // Switch between different API
  const fetchData = async (params: CommonRequest) => {
    let result: CommonResponse = {};
    // setApiParam always returns CommonRequest, never undefined
    const paramList: CommonRequest = setApiParam(params);

    // ページネーションの安全な比較
    const currentPage = paramList.page ?? 1;
    const paginationPage = pagination?.page ?? 1;

    const isLoadingPrev = currentPage < paginationPage;
    const isLoadingNext = currentPage > paginationPage;

    setLoadingStatus((prev) => ({
      ...prev,
      isLoadingPrev,
      isLoadingNext,
    }));

    setIsLoading(true);
    try {
      if (
        activeTab === CATEGORY_TYPE.STEP &&
        dateType === DAY_WEEK_TAB.DAY &&
        groupAllTab === GROUP_ALL_TAB.GROUP
      ) {
        result = await rankingAPI.getStepGroupDailyRankingList(paramList);
      } else if (
        activeTab === CATEGORY_TYPE.STEP &&
        dateType === DAY_WEEK_TAB.DAY &&
        groupAllTab === GROUP_ALL_TAB.ALL
      ) {
        result = await rankingAPI.getStepDailyRankingList(paramList);
      } else if (
        activeTab === CATEGORY_TYPE.STEP &&
        dateType === DAY_WEEK_TAB.WEEK &&
        groupAllTab === GROUP_ALL_TAB.GROUP
      ) {
        result = await rankingAPI.getStepGroupWeeklyRankingList(paramList);
      } else if (
        activeTab === CATEGORY_TYPE.STEP &&
        dateType === DAY_WEEK_TAB.WEEK &&
        groupAllTab === GROUP_ALL_TAB.ALL
      ) {
        result = await rankingAPI.getStepWeeklyRankingList(paramList);
      } else if (activeTab === CATEGORY_TYPE.MISSION && dateType === DAY_WEEK_TAB.DAY) {
        const diffResult = await rankingAPI.getMissionDailyRankingList(paramList);
        result = {
          ...diffResult,
          rankingList: diffResult?.rankingList?.map(({ missionCompCnt, ...rest }) => ({
            ...rest,
            steps: missionCompCnt,
          })),
        };
      } else if (activeTab === CATEGORY_TYPE.MISSION && dateType === DAY_WEEK_TAB.WEEK) {
        const diffResult = await rankingAPI.getMissionWeeklyRankingList(paramList);
        result = {
          ...diffResult,
          rankingList: diffResult?.rankingList?.map(({ missionCompCnt, ...rest }) => ({
            ...rest,
            steps: missionCompCnt,
          })),
        };
      } else if (activeTab === CATEGORY_TYPE.ORGANIZER) {
        const diffResult = await rankingAPI.getOrganizerRankingList(paramList);
        result = {
          ...diffResult,
          rankingList: diffResult?.rankingList?.map(({ groupName, avgSteps, ...rest }) => ({
            ...rest,
            nickName: groupName,
            steps: avgSteps,
          })),
        };
      }

      getResponseInfo(result, isLoadingPrev, isLoadingNext, params?.myselfFlg);
    } catch (error) {
      // console.log('One or more API requests failed:', error)
    } finally {
      setIsLoading(false);
      setIsInitialLoading(false);
      setLoadingStatus((prev) => ({
        ...prev,
        isLoadingPrev: false,
        isLoadingNext: false,
      }));
    }
  };

  // 利用者のランキング情報取得API
  const getUserRankingInfoList = async () => {
    setIsLoading(true);
    try {
      const result = await rankingAPI.getUserRankingInfoList();
      setUserRankingInfoList(result?.userRankingInfoList ?? []);
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (userRankingInfoList?.length > 0) {
      getRankingExclUser();
    }
  }, [userRankingInfoList, organizationIndex]);

  // ランキング対象外ユーザーを取得
  const getRankingExclUser = async () => {
    setIsLoading(true);
    try {
      const result = await rankingAPI.getRankingExclUser({
        organizerId: userRankingInfoList[organizationIndex]?.organizerId,
      });
      setRankExclFlg(result?.rankExclFlg ?? 0);
      if (result?.rankExclFlg === RANK_EXCL_FLG.OUTSIDE) {
        setGroupAllTab(GROUP_ALL_TAB.ALL);
      }
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMySelfFlg = (activeTab: number, list: UserRankingInfoList[]) => {
    let num: number | undefined = 0;
    num = list[organizationIndex]?.categoryList
      ?.filter((item: CategoryList) => item.category === activeTab)
      .find((cat) => cat.category === activeTab)?.order;
    return num ? num > 0 : 0;
  };

  // ALL api response
  const getResponseInfo = (
    result: CommonResponse,
    isLoadingPrev: boolean,
    isLoadingNext: boolean,
    myselfFlg: MYSELF_FLG | undefined,
  ) => {
    const newResult = result?.rankingList ?? [];
    setRankingInfo(result);
    // Merge data based on loading direction
    let newList = [];
    if (isLoadingPrev) {
      // prev page -- handleLoadPrev
      newList = [...newResult, ...rankingList];
      setRankingList((prev) => [...newResult, ...prev]);
    } else if (isLoadingNext) {
      // next page -- handleLoadNext
      newList = [...rankingList, ...newResult];
      setRankingList((prev) => [...prev, ...newResult]);
    } else {
      // first page
      newList = newResult;
      setRankingList((prev) => [...newResult]);
    }

    // const uniqueArray = [...new Map(newList?.map((item) => [item.order, item])).values()];
    // setRankingList(uniqueArray);
    // setMyselfFlg(myselfFlg === MYSELF_FLG.SELF ? MYSELF_FLG.NORMAL : MYSELF_FLG.DEFAULT);
    setPagination({
      ...result?.pagination,
    });
    const currentPage = result?.pagination?.page ?? 1;
    const totalPages = result?.pagination?.pages ?? 1;
    // is have more pages
    setHasMore({
      hasPrevPage: currentPage > 1,
      hasNextPage: currentPage < totalPages,
    });
  };

  const setApiParam = (params: CommonRequest) => {
    return {
      organizerId: userRankingInfoList[organizationIndex]?.organizerId,
      statDateFrom: setDateParam(selectDate?.startDate)?.startDate,
      statDateTo: setDateParam(selectDate?.endDate)?.endDate,
      myselfFlg: params?.myselfFlg,
      page: noPageParamFlg ? undefined : setPageParam(params),
      limit: noPageParamFlg ? undefined : 30,
    };
  };

  // 歩数+グループ，no have pagination
  const noPageParamFlg = useMemo(() => {
    if (activeTab === CATEGORY_TYPE.STEP && groupAllTab === GROUP_ALL_TAB.GROUP) {
      return true;
    }
    return false;
  }, [activeTab, groupAllTab]);

  const setPageParam = (params: Pagination) => {
    return params?.page ? params.page : pagination?.page;
  };

  const setDateParam = (v: string | number | Date | undefined) => {
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    let date: any = {};
    if (activeTab === CATEGORY_TYPE.ORGANIZER) {
      const today = new Date();
      const sunday = endOfWeek(today, { weekStartsOn: 1 });
      date = {
        endDate: format(v ?? sunday, 'yyyy/MM/dd'),
      };
    } else {
      if (!v) return date;
      if (activeTab !== CATEGORY_TYPE.ORGANIZER && dateType === DAY_WEEK_TAB.WEEK) {
        date = {
          endDate: format(v, 'yyyy/MM/dd'),
        };
      } else {
        date = {
          startDate: format(v, 'yyyy/MM/dd'),
        };
      }
    }
    return date;
  };

  const handleTabChange = (tab: number) => {
    setActiveTab(tab);
    if (tab === CATEGORY_TYPE.ORGANIZER) {
      setDateType(DAY_WEEK_TAB.WEEK);
    }
  };

  const changeDateType = (tab: string) => {
    setDateType(tab);
    setSelectDate({});
  };

  const changeGroupAllTab = (tab: string) => {
    setGroupAllTab(tab);
  };

  const handleSlideChange = (index: number) => {
    const selectOrgan = userRankingInfoList[index];
    setOrganizationIndex(index);
  };

  const rightIconClick = () => {
    router.push('/ranking/hint');
  };

  const selectDayWeek = (v: DateType) => {
    setSelectDate(v);
  };

  // 自分を表示 scroll to selfflg= 1
  const scrollToSelfItem = async () => {
    if (noPageParamFlg) {
      setMyselfFlg(MYSELF_FLG.NORMAL);
    } else {
      seIsMyselfFlg(true);
      await fetchData({
        myselfFlg: MYSELF_FLG.SELF,
        // page: 1,
      });
      setMyselfFlg(MYSELF_FLG.NORMAL);
    }
    // setTimeout(() => {
    //   setMyselfFlg(MYSELF_FLG.DEFAULT);
    // }, 1000);
  };

  const handleLoadPrev = useCallback(() => {
    if (pagination?.page && pagination?.total) {
      const prevPage = pagination?.page - 1;
      // console.log('prev====', prevPage, selectDate, pagination);

      if (prevPage >= 1 && rankingList?.length < pagination?.total) {
        // TODO: wait add scroll
        // fetchData({
        //   page: prevPage,
        // });
      }
    }
  }, [pagination, rankingList]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const handleLoadNext = useCallback(() => {
    if (pagination?.page && pagination?.total && pagination?.pages) {
      const nextPage = pagination?.page + 1;
      if (nextPage <= pagination?.pages && rankingList?.length < pagination?.total) {
        fetchData({
          page: nextPage,
        });
      }
    }
  }, [pagination, rankingList]);

  const renderRankingItem = (item: any, index: number) => {
    return <HistoryListPage list={item} activeTab={activeTab} />;
  };

  return (
    <div className="">
      <TopBar
        title="ランキング"
        rightIcon={<img src="/images/ranking/hint.svg" alt="hint" width={20} height={20} />}
        rightIconClick={rightIconClick}
      />
      <div className="bg-white pb-4">
        {userRankingInfoList?.length > 1 ? (
          <SwipingAreaBanner
            slides={userRankingInfoList}
            initialIndex={organizationIndex}
            onSlideChange={handleSlideChange}
          />
        ) : (
          <div className="px-6 pt-4 text-center">
            {userRankingInfoList && userRankingInfoList?.length > 0 ? (
              <CarouselContentPage {...userRankingInfoList[0]} showOrganizerName={false} />
            ) : (
              '-'
            )}
          </div>
        )}
      </div>

      {/* 1:歩数、2:ミッション、3:団体 */}
      <FixedArea>
        <MenuTab activeTab={activeTab} onTabChange={handleTabChange} />
      </FixedArea>

      <div className="px-6 space-y-4 mt-4">
        {/* 日和週 */}
        {activeTab !== CATEGORY_TYPE.ORGANIZER && (
          <TabGroupPage
            className="w-full"
            tabsTriggerClassName="text-black"
            activeTab={dateType}
            tabGroup={CATEGORY_TAB}
            onTabChange={changeDateType}
          />
        )}

        {/* 日期组件 */}
        <UpdateDatePage
          updatedAt={rankingInfo.upstatDatedAt}
          dateType={dateType}
          onSelect={selectDayWeek}
        />

        {/* グループ/全ユーザー*/}
        <div
          className={`grid grid-cols-[1fr_75px] ${getMySelfFlg(activeTab, userRankingInfoList) ? '' : '!mt-2'}`}
        >
          <div>
            {activeTab === CATEGORY_TYPE.STEP && rankExclFlg === RANK_EXCL_FLG.INSIDE && (
              <TabGroupPage
                className="w-[176px] text-sm"
                tabsTriggerClassName="text-[#333333]"
                activeTab={groupAllTab}
                tabGroup={ALL_GROUP_TAB}
                onTabChange={changeGroupAllTab}
              />
            )}
          </div>
          {getMySelfFlg(activeTab, userRankingInfoList) ? (
            <TextButton className="pt-[6px] text-[15px]" onClick={scrollToSelfItem}>
              自分を表示
            </TextButton>
          ) : (
            ''
          )}
        </div>

        {/* 履历 */}
        <HistoryScrollList
          items={rankingList}
          pagination={pagination}
          loadingStatus={loadingStatus}
          hasMore={hasMore}
          isInitialLoading={isInitialLoading}
          onLoadPrev={handleLoadPrev}
          onLoadNext={handleLoadNext}
          renderItem={renderRankingItem}
          // emptyText=""
          isSelfScroll={myselfFlg}
        />
      </div>
    </div>
  );
}
