'use client';
import TopBar from '@/components/layout/top-bar';
import { But<PERSON> } from '@/components/shared/button';
import { Checkbox } from '@/components/shared/checkbox';
import MenuPanel3 from '@/components/shared/menu-panel3';
import Link from '@/components/shared/router-link';
import { Textarea } from '@/components/ui/textarea';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useGlobalVar } from '@/hooks/use-global-var';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { usePostStore } from '@/store/post';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { useEffect, useRef, useState } from 'react';
import Slider, { type Settings } from 'react-slick';
import CheckBoxCards from '../_components/checkbox-cards';
import ImageButton from '../_components/image-button';

export default function PostConfirmPage() {
  const { setLoading } = useLoading();
  const router = useRouter();
  const geocodingLib = useMapsLibrary('geocoding');
  const [selectedCategoryValues, setSelectedCategoryValues] = useState<string[]>(['']);
  const [postId, setPostId] = useState<number | undefined>(undefined);
  const SliderComponent = Slider as unknown as React.ComponentType<Settings>;
  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    customPaging: () => (
      <div className="mt-2">
        <div className="w-3 h-3 rounded-full bg-gray-300 mx-1" />
      </div>
    ),
    dotsClass: 'slick-dots flex justify-center mt-4',
    autoplay: false,
    arrows: false,
  };
  const {
    confirmPosts,
    setConfirmPosts,
    postAddress,
    setPostAddress,
    setPostLat,
    setPostLng,
    postLat,
    postLng,
    setPostComment,
    postComment,
    setPostCategory,
    postCategory,
    onMap,
    setOnMap,
  } = usePostStore();
  const { isShow, setDialog } = useMessageDialog();
  const { setFooterMenuSettingOptions } = useGlobalVar();

  useEffect(() => {
    setSelectedCategoryValues([postCategory || '']);
    setFooterMenuSettingOptions({ isShow: false });

    if (postId !== undefined && postLat !== undefined && postLng !== undefined) {
      getAddressFromLatLng(Number(postLat), Number(postLng));
    }

    getAddressFromLatLng(35.6895, 139.6917);

    return () => {
      setFooterMenuSettingOptions({ isShow: true });
    };
  }, [postCategory]);

  const getAddressFromLatLng = (lat: number, lng: number) => {
    try {
      if (geocodingLib !== null) {
        const geocoder = new geocodingLib.Geocoder();

        const latLng = new window.google.maps.LatLng(lat, lng);

        geocoder.geocode({ location: latLng }, (results, status) => {
          if (status === 'OK' && results !== null && results !== undefined) {
            if (results[0]) {
              setPostAddress(results[0].formatted_address);
            }
          }
        });
      }
    } catch (error) {
      console.error('Geocoding error:', error);
    } finally {
    }
  };

  const handlePostClick = async () => {
    setLoading(true);

    sendMessageToNative({
      type: 'send-post-toserver',
      data: {
        postId: postId || '',
        categoryCd: postCategory,
        postComment: postComment,
        latitude: postLat,
        longitude: postLng,
        onMap: onMap || '0',
      },
      callback: (data) => {
        if (data?.success) {
          showFinishDialog();
        }
        setLoading(false);
      },
    });
  };

  const selectCategory = (categories: string[]) => {
    if (categories.length === 0) {
      setSelectedCategoryValues(categories);
    } else {
      const filteredArray = categories.filter((item) => !selectedCategoryValues.includes(item));
      setSelectedCategoryValues(filteredArray);
    }
    if (categories.length === 1) {
      setPostCategory(categories[0]);
    } else if (categories.length === 2) {
      setPostCategory(categories[1]);
    }
  };

  //初めてマップにチェックを入れた場合
  const showFirstTimeDialog = () => {
    setDialog(true, {
      content: (
        <div>
          <div className="text-[16px]">
            {APP_TEXT.POST_CONFIRM_PAGE.MAP_POPUP_TEXT1}
            <br />
            {APP_TEXT.POST_CONFIRM_PAGE.MAP_POPUP_TEXT2}
            <br />
            {APP_TEXT.POST_CONFIRM_PAGE.MAP_POPUP_TEXT3}
          </div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="default"
            className="w-full bg-primary text-white"
            onClick={() => {
              setDialog(false);
            }}
          >
            {APP_TEXT.DATA_SRCSET.CONFIRM_CLOSE}
          </Button>
        </div>
      ),
    });
  };

  //送信成功時場合
  const showFinishDialog = () => {
    setDialog(true, {
      title: APP_TEXT.POST_CONFIRM_PAGE.FINISH_TITLE,
      content: (
        <div className="text-[14px] text-gray-500">{APP_TEXT.POST_CONFIRM_PAGE.FINISH_CONTENT}</div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="default"
            className="w-full bg-primary text-white"
            onClick={() => {
              setDialog(false);
              setPostId(undefined);
              setPostAddress(undefined);
              setPostLat(undefined);
              setPostLng(undefined);
              setPostComment(undefined);
              setPostCategory(undefined);
              //S010_他ユーザーの写真投稿一覧画面に遷移する
              router.push(ROUTES.POST.MY_PHOTO_SUBMISSIONS);
            }}
          >
            {APP_TEXT.POST_CONFIRM_PAGE.FINISH_BUTTON}
          </Button>
        </div>
      ),
    });
  };

  const handleBackClick = () => {
    setDialog(false);
    setPostId(undefined);
    setPostAddress(undefined);
    setPostLat(undefined);
    setPostLng(undefined);
    setPostComment(undefined);
    setPostCategory(undefined);
    router.back();
  };

  const SETTING_MENU_ITEMS = [
    {
      label: postAddress || APP_TEXT.POST_CONFIRM_PAGE.LOCATION_SUB,
      onClick: () => {
        router.push(ROUTES.POST.MAP);
      },
      href: '',
    },
  ];

  const goEditPage = (index: number) => {
    //Nativeの編集画面へ遷移する
    //新規
    sendMessageToNative({
      type: 'show-edit-page',
      data: {
        images: postId || '', //編集の場合のみ設定する
        index: index,
      },
      callback: (data) => {},
    });
  };

  return (
    <>
      <TopBar onBack={handleBackClick} title={APP_TEXT.POST_CONFIRM_PAGE.TITLE} />
      <div className="flex-col flex">
        <div className="bg-white slider-container relative">
          <SliderComponent {...settings}>
            {confirmPosts.map((img, i) => (
              <div key={i} className="w-screen aspect-square overflow-hidden relative">
                <img
                  src={`${img.url}`}
                  alt={`slide-${i}`}
                  className="w-full h-full object-cover object-center"
                  draggable={false}
                />
                <ImageButton
                  index={i}
                  imgSrc="/images/post/icon-round-pen.png"
                  onClick={goEditPage}
                />
              </div>
            ))}
          </SliderComponent>
        </div>
        {/* コメント */}
        <div className="ml-4 mr-4 mt-6">
          <div>
            <span className="text-[16px] font-medium">{APP_TEXT.POST_CONFIRM_PAGE.COMMENT}</span>
            <span className="text-[16px] font-medium text-gray-400">
              {APP_TEXT.POST_CONFIRM_PAGE.COMMENT_SUB}
            </span>
          </div>
          <Textarea
            maxLength={140}
            value={postComment || ''}
            placeholder=""
            className="min-h-[300px] resize-none mt-2 bg-white"
            onChange={(e) => setPostComment(e.target.value)}
          />
        </div>
        <MenuPanel3
          className="mb-4 bg-transparent border-2 border-gray-300 font-medium"
          title={APP_TEXT.POST_CONFIRM_PAGE.LOCATION}
          menuItems={SETTING_MENU_ITEMS}
        />
        <div className="flex items-center justify-start gap-2 ml-6 mr-6">
          <Checkbox
            checked={onMap === '1'}
            onCheckedChange={() => {
              if (onMap === undefined) {
                showFirstTimeDialog();
              }
              setOnMap(onMap !== '1' ? '1' : '0');
            }}
            className="border-border ring-none shadow-none bg-white border-2 border-gray-400"
          />
          <span className="text-[15px] text-text-primary">
            {APP_TEXT.POST_CONFIRM_PAGE.LOCATION_CHECK}
          </span>
        </div>
        <div className="px-6 pt-6">
          <div className="pb-2 font-medium">{APP_TEXT.POST_CONFIRM_PAGE.CATEGORY}</div>
          <CheckBoxCards
            selectedValues={selectedCategoryValues}
            onChange={selectCategory}
            labels={APP_TEXT.POST_CONFIRM_PAGE.CATEGORYS}
          />
        </div>
        <Link
          target="_blank"
          className="text-primary mt-3 mb-4 ml-6 mr-4 font-bold"
          href={ROUTES.POST.POLICY}
        >
          {APP_TEXT.POST_CONFIRM_PAGE.POLICY}
        </Link>
        <Button
          className="bg-primary disabled:bg-primary-light text-white  font-bold h-12   rounded-[25px] w-[88vw]  ml-6 mr-6 mb-6"
          type="button"
          disabled={
            !(
              confirmPosts.length > 0 &&
              postAddress !== undefined &&
              postLat !== undefined &&
              postLng !== undefined &&
              selectedCategoryValues.length > 0 &&
              selectedCategoryValues[0] !== ''
            )
          }
          onClick={handlePostClick}
        >
          <span className="text-[16px] text-white">{APP_TEXT.POST_CONFIRM_PAGE.POST}</span>
        </Button>
      </div>
    </>
  );
}
