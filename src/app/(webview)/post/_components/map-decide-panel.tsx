import { Button } from '@/components/shared/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/shared/drawer';
import { APP_TEXT } from '@/const/text/app';
import { Plus, X } from 'lucide-react';
export default function MapDecidePanel({
  isOpen,
  address,
  onClose,
}: { isOpen: boolean; address: string; onClose: () => void }) {
  const openCamera = () => {
    onClose();
  };
  const openPhoto = () => {
    onClose();
  };
  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerTrigger asChild>
        <img src="/images/post/icon-post-detail.png" alt="" className="w-7 h-7 ml-auto mt-[-2px]" />
      </DrawerTrigger>
      <DrawerContent className="height-auto-important pb-5">
        <DrawerHeader className="relative">
          <DrawerTitle className="font-bold text-[18px]">
            {APP_TEXT.POST_MAP_PAGE.COMMENT_SUB}
          </DrawerTitle>
          <DrawerDescription className="sr-only" />
          <DrawerClose asChild>
            <button type="button" className="absolute right-5 top-3">
              <X size={24} />
            </button>
          </DrawerClose>
        </DrawerHeader>
        {/* 自分 */}
        <>
          {/* カメラ */}
          <div className="bg-card h-full flex flex-col">
            <div className="text-xl m-6">{address}</div>
            <Button
              className="bg-primary disabled:bg-primary-light text-white  font-bold h-12   rounded-[25px] w-[88vw]  ml-6 mr-6 mb-6"
              type="button"
              onClick={onClose}
            >
              <span className="text-[16px] text-white">{APP_TEXT.POST_MAP_PAGE.CONFIRM}</span>
            </Button>
          </div>
        </>
      </DrawerContent>
    </Drawer>
  );
}
