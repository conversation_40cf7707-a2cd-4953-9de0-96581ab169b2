'use client';
import { Button } from '@/components/shared/button';
import { COLORS } from '@/const/colors';
import { APP_TEXT } from '@/const/text/app';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import React, { useEffect, useState } from 'react';

const TopButton = ({
  showTopButton,
  distance = 0,
  className,
  scrollToTop,
}: { showTopButton: boolean; distance?: number; className?: string; scrollToTop?: () => void }) => {
  const [bottom, setBottom] = useState<number>(0);
  const safeArea = useSafeArea();
  const clickButton = () => {
    if (scrollToTop) {
      scrollToTop();
    } else {
      window?.scrollTo({ behavior: 'smooth', top: 0 });
    }
  };

  useEffect(() => {
    const menuHeight = 61;
    const menuMargin = 16;
    const bottom = safeArea.bottom + menuHeight + menuMargin + distance;
    setBottom(bottom);
  }, [safeArea, distance]);
  return (
    <Button
      type="button"
      className={cn(
        `bg-white text-primary  w-10 h-10 rounded-full shadow-xl flex flex-col items-center fixed px-0 gap-0 z-10 right-4 transition-opacity duration-300 ${className}`,
        showTopButton ? 'opacity-100' : 'opacity-0 pointer-events-none',
      )}
      style={{ bottom, border: `1px solid ${COLORS.primary.DEFAULT}` }}
      onClick={clickButton}
    >
      <img src="/images/post/icon-post-top.png" alt="" className="w-4 h-4 text-gray-800" />
      <span className="mt-[-2px] text-xs text-primary">{APP_TEXT.POST_PAGE.TOP}</span>
    </Button>
  );
};

export default TopButton;
