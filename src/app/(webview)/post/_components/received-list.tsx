'use client';
import { postAPI } from '@/api/modules/post';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import type { likeUsers } from '@/types/post';
import { formatDate } from '@/utils/date-format';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import PhotoEmpty from './photo-empty';
import TopButton from './top-button';
const ReceivedList = () => {
  const [likeItems, setItems] = useState<likeUsers[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [showTopButton, setShowTopButton] = useState(false);
  const observer = useRef<IntersectionObserver | null>(null);
  const listRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const lastItemRef = useCallback(
    (node: HTMLElement | null) => {
      if (loading) return;

      if (observer.current) {
        observer.current.disconnect();
      }

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          loadLikeData();
        }
      });

      if (node) {
        observer.current.observe(node);
      }
    },
    [loading, hasMore],
  );
  const loadLikeData = async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      try {
        const res = await postAPI.getReceivedList({ page, limit: 15 });
        if (res.likeUsers) {
          setItems((prev) => [...prev, ...res.likeUsers]);
          res?.pagination?.page >= res.pagination?.pages ? setHasMore(false) : setHasMore(true);
          setPage((prev) => prev + 1);
        }
      } catch (error) {
        console.error('Error fetching like data:', error);
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching like data:', error);
    } finally {
      setLoading(false);
    }
  };
  const scrollToTop = () => {
    listRef.current?.scrollTo({ behavior: 'smooth', top: 0 });
  };

  // S010_他ユーザーの写真投稿一覧画面に遷移する。
  const goOtherPhotoList = (post: likeUsers) => {
    router.push(`${ROUTES.POST.OTHER_PHOTO_SUBMISSIONS}?queryUserId=${post.userId}`);
  };
  // S006_投稿詳細画面に遷移する。
  const goPhotoDetail = (post: likeUsers) => {
    router.push(`${ROUTES.POST.DETAIL}?queryUserId=${post.userId}&postId=${post.postId}`);
  };
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    loadLikeData();
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  useEffect(() => {
    const listElement = listRef.current;
    if (!listElement) return;

    const handleScroll = () => {
      if (listElement.scrollTop > 50) {
        setShowTopButton(true);
      } else {
        setShowTopButton(false);
      }
    };

    listElement.addEventListener('scroll', handleScroll);
    return () => {
      listElement.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <>
      <div
        className={`flex-1 overflow-auto ${likeItems.length > 0 ? 'bg-white ' : ''}`}
        ref={listRef}
        style={{
          height: 'calc(100vh - 61px - 48px - 64px)',
        }}
      >
        {/* データがない場合の表示 */}
        {likeItems.length === 0 && !loading && (
          <PhotoEmpty>
            <div className="w-[200px] text-center text-[#666666] text-lg mt-4 font-bold">
              {APP_TEXT.POST.RECEIVED_NO_DATA}
            </div>
          </PhotoEmpty>
        )}
        {likeItems.length > 0 && (
          <div className="p-6">
            {likeItems.map((item, index) => (
              <div
                ref={likeItems.length === index + 1 ? lastItemRef : null}
                key={item.userId}
                className="py-2 flex items-center gap-2 border-b-[1px] border-gray-300"
              >
                <div
                  className="w-12 h-12"
                  onClick={() => goOtherPhotoList(item)}
                  onKeyDown={() => goOtherPhotoList(item)}
                >
                  <img
                    className="rounded-full w-12 h-12 justify-self-center"
                    src={item.avatar}
                    alt=""
                  />
                  {/* <div className="text-[#4457D1] font-bold flex items-center justify-center w-12 h-5 border-2 border-[#A7B9FC]">
                    Lv.99
                  </div> */}
                </div>
                <div className="flex-1 text-[#000000] ">
                  <div className="text-[#666666]">
                    {formatDate(item.likeAt, 'yyyy年M月d日 HH:mm')}
                  </div>
                  <div className="font-bold">
                    {item.nickName}
                    <span className="font-normal">{APP_TEXT.POST.TEXT1}</span>
                  </div>
                  <div>{APP_TEXT.POST.TEXT2}</div>
                </div>
                <div
                  className="w-[56px] h-[56px] rounded-xl"
                  onClick={() => goPhotoDetail(item)}
                  onKeyDown={() => goPhotoDetail(item)}
                >
                  <img
                    className="w-[56px] h-[56px] rounded-[4px]"
                    src={item.thumbnailImage}
                    alt="like"
                  />
                </div>
              </div>
            ))}
            {/* {loading && <div className="text-center">{COMMON_TEXT.MESSAGE.LOADING}</div>}
            {!hasMore && <div className="text-center">{COMMON_TEXT.MESSAGE.NO_DATA}</div>} */}
            <TopButton showTopButton={showTopButton} scrollToTop={scrollToTop} />
          </div>
        )}
      </div>
    </>
  );
};

export default ReceivedList;
