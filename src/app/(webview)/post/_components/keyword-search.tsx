'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useSearchHistoryStore } from '@/store/search-history-store';

import { useRouter } from '@/hooks/use-next-navigation';
import { Clock, Search, Tent, X } from 'lucide-react';
import { useEffect, useState } from 'react';
/**
 * イベントキーワード検索コンポーネントのProps
 */
interface EventKeywordProps {
  /** 初期キーワード */
  initialKeyword?: string;
  /** キーワード選択時のコールバック関数 */
  onKeywordSelect: (keyword: string, lat: number, lng: number) => void;
  /** 閉じるボタンのコールバック関数 */
  onClose: () => void;
  /** 機能タイプ（デフォルト: 'event'） */
  functionType?: string;
  functionTypeName?: string;
  /** キーワード検索候補取得関数 */
  keywordSearchFunction?: (searchTerm: string) => Promise<SearchDataProps | undefined>;
  suggestionsIcon?: React.ReactNode;
  /** 詳細ページのURL（例: "/event/[eventId]"） */
  detailUrl?: string;
}

interface SearchDataProps {
  address?: string[];
  lat?: number;
  lng?: number;
}

/**
 * イベントキーワード検索コンポーネント
 * 検索候補表示と検索履歴管理機能を提供
 */
export default function PostKeyword({
  initialKeyword = '',
  onKeywordSelect,
  onClose,
  functionType = 'event',
  functionTypeName,
  keywordSearchFunction,
  suggestionsIcon = <Search className="h-6 w-6 text-muted-foreground" />,
}: EventKeywordProps) {
  const router = useRouter();
  // 現在の入力キーワード
  const [keyword, setKeyword] = useState(initialKeyword);
  // 検索候補リスト
  const [keywordList, setKeywordList] = useState<string[]>([]);

  const [lat, setLat] = useState<number>(0);
  const [lng, setLng] = useState<number>(0);

  // ローディング状態
  const [isLoading, setIsLoading] = useState(false);

  // 検索履歴ストアから必要な関数を取得
  const { getSearchHistory, addSearchHistory, removeSearchHistoryItem } = useSearchHistoryStore();
  // 現在の機能タイプの検索履歴を取得
  const searchHistory = getSearchHistory(functionType);

  /**
   * キーワード検索候補を取得する関数
   * @param searchTerm 検索語
   */
  const searchKeyword = async (searchTerm: string) => {
    // 空文字の場合は候補リストをクリア
    if (!searchTerm.trim()) {
      setKeywordList([]);
      return;
    }

    setIsLoading(true);
    try {
      // カスタム検索関数が渡されている場合はそれを使用
      const searchFunction = keywordSearchFunction;
      if (searchFunction !== undefined && searchFunction !== null) {
        const data = await searchFunction(searchTerm);
        if (data !== undefined && data.address !== undefined) {
          setKeywordList(data.address);
        }
        if (data !== undefined && data.lat !== undefined) {
          setLat(data.lat);
        }
        if (data !== undefined && data.lng !== undefined) {
          setLng(data.lng);
        }
      }
    } catch (error) {
      console.error('キーワード検索に失敗しました:', error);
      setKeywordList([]);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 入力値変更時の処理
   * @param e 入力イベント
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);
    // リアルタイムで検索候補を取得
    searchKeyword(value);
  };

  /**
   * キーボードイベント処理（Enterキーで検索実行）
   * @param e キーボードイベント
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && keyword.trim()) {
      handleKeywordSelect(keyword.trim());
    }
  };

  /**
   * キーワード選択時の処理
   * @param selectedKeyword 選択されたキーワード
   */
  const handleKeywordSelect = (selectedKeyword: string) => {
    // 検索履歴に追加
    addSearchHistory(functionType, selectedKeyword);
    // 親コンポーネントにコールバック
    onKeywordSelect(selectedKeyword, lat, lng);
    onClose();
  };

  /**
   * 検索履歴項目選択時の処理
   * @param historyKeyword 履歴のキーワード
   */
  const handleHistorySelect = (historyKeyword: string) => {
    setKeyword(historyKeyword);
    handleKeywordSelect(historyKeyword);
    onClose();
  };

  /**
   * 検索候補項目選択時の処理（詳細ページへの遷移）
   * @param item 選択された候補項目
   */
  const handleSuggestionSelect = (item: string) => {
    // 従来の検索処理
    setKeyword(item);
    handleKeywordSelect(item);
    onClose();
  };

  /**
   * 検索履歴項目削除処理
   * @param historyKeyword 削除するキーワード
   * @param e マウスイベント
   */
  const handleRemoveHistory = (historyKeyword: string, e: React.MouseEvent) => {
    // イベントバブリングを防止
    e.stopPropagation();
    removeSearchHistoryItem(functionType, historyKeyword);
  };

  return (
    <div className="bg-card h-full flex flex-col">
      {/* 検索入力ボックス */}
      <div className="p-4 border-b border-border">
        <div className="relative">
          {/* 検索アイコン */}
          <Search className="absolute left-3 top-4 h-4 w-4 text-muted-foreground pointer-events-none z-10" />
          {/* 検索入力フィールド */}
          <Input
            value={keyword}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="駅、都市、キーワードを入力"
            autoFocus
            className="pl-10 pr-10 w-full h-12"
          />
          {/* クリアボタン（入力がある場合のみ表示） */}
          {keyword && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setKeyword('');
                setKeywordList([]);
              }}
              className=" absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            >
              <img src="/images/event/close-icon.svg" alt="" />
            </Button>
          )}
        </div>
      </div>

      {/* メインコンテンツエリア */}
      <div className="flex-1 overflow-y-auto">
        {keyword.trim() && (
          // 検索候補表示エリア
          <div className="p-4">
            {isLoading ? (
              // ローディング表示
              <div className="text-center py-8 text-muted-foreground">検索中...</div>
            ) : (
              keywordList.length > 0 && (
                // 検索候補がある場合
                <div className="space-y-2">
                  {/* 検索候補キーワードリスト */}
                  {keywordList.map((item, index) => (
                    <div
                      key={index}
                      onClick={() => handleSuggestionSelect(item)}
                      className="border-b flex items-center gap-3 p-3 hover:bg-muted cursor-pointer transition-colors"
                    >
                      <div className="p-1 bg-background rounded-full">
                        <Search className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <span className="flex-1 ">{item}</span>
                    </div>
                  ))}
                </div>
              )
            )}
          </div>
        )}
      </div>
    </div>
  );
}
