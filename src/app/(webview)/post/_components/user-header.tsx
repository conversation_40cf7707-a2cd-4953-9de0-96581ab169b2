import { isOneselfType } from '@/const/app';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import type { PostClassifyListResponse } from '@/types/post';
import React from 'react';

const UserHeader = ({
  className,
  userPost,
}: { className?: string; userPost: PostClassifyListResponse }) => {
  const safeArea = useSafeArea();
  const safeTop = safeArea.top ?? 0;
  return (
    <>
      <div className="mt-[48px]" style={{ height: safeTop }} />
      <div className={cn('flex gap-2 px-6 py-3 bg-white', className)}>
        <div className="w-11 h-11">
          {userPost.avatar && (
            <img className="w-11 h-11 rounded-full" src={userPost.avatar} alt="" />
          )}
        </div>
        <div className="flex-1 text-black">
          <div className="font-bold ">
            {userPost.isFriend === 1 && (
              <span className="w-14 h-[18px] px-1 text-sm font-normal  bg-[#EDF1FE] rounded-sm text-[#4457D1]">
                フレンド
              </span>
            )}
            {userPost.nickName}
          </div>
          <div className="text-[13px] flex gap-2 ">
            <span>投稿数 {userPost?.postStatistics?.postCount || 0}件</span>
            {userPost.isOneself === isOneselfType.self && (
              <span>
                （うち審査中:{userPost?.postStatistics?.inReview || 0}件・
                <span className="text-[#CE0000]">
                  却下:{userPost?.postStatistics?.reject || 0}件
                </span>{' '}
                ）
              </span>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default UserHeader;
