'use client';

import { APP_TEXT } from '@/const/text/app';
import type { PostInterface } from '@/types/post';
import Slider, { type Settings } from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { HeartAnimation } from '@/components/shared/animation';
import { PostStatusType, isLikeType, isOneselfType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import { formatDate } from '@/utils/date-format';
import { ExpandableText } from './expandable-text';
interface PostListWrapperProps {
  postsList: PostInterface[];
  hasMore?: boolean;
  loading?: boolean;
  isSelf?: boolean;
  loadMore: () => void;
  onLike: (post: PostInterface) => void;
  children?: React.ReactNode;
}

export default function OverviewLayout({
  postsList,
  hasMore,
  loading,
  isSelf = false,
  loadMore,
  children,
  onLike,
}: PostListWrapperProps) {
  const SliderComponent = Slider as unknown as React.ComponentType<Settings>;
  const router = useRouter();
  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    customPaging: () => (
      <div className="mt-2">
        <div className="w-3 h-3 rounded-full bg-gray-300 mx-1" />
      </div>
    ),
    dotsClass: 'slick-dots flex justify-center mt-4',
    autoplay: false,
    arrows: false,
  };
  const { lastItemRef } = useInfiniteScroll({
    hasMore,
    loading,
    onLoadMore: loadMore,
    rootMargin: '0px',
  });
  const pathname = usePathname();
  const goDetail = (post: PostInterface) => {
    if (pathname === ROUTES.POST.DETAIL) return;
    router.push(`${ROUTES.POST.DETAIL}?queryUserId=${post.userId}&postId=${post.postId}`);
  };
  const goPhotoView = (post: PostInterface) => {
    if (post.isOneself === isOneselfType.self) {
      router.push(`${ROUTES.POST.MY_PHOTO_SUBMISSIONS}`);
    } else {
      router.push(`${ROUTES.POST.OTHER_PHOTO_SUBMISSIONS}?queryUserId=${post.userId}`);
    }
  };
  return (
    <div>
      {postsList?.map((post, index) => (
        <div className="mb-2" key={index} ref={index === postsList.length - 1 ? lastItemRef : null}>
          <div key={index} className="flex gap-2 bg-white px-6 pt-4 pb-2">
            <div
              className="h-10 w-10 overflow-hidden rounded-full flex items-center justify-center"
              onClick={() => goPhotoView(post)}
              onKeyDown={() => goPhotoView(post)}
            >
              <img src={post.avatar} alt="" className="w-10 h-10 text-gray-800" />
            </div>
            <div className="flex-1">
              <div className="flex gap-2 items-center">
                {post.isFriend === 1 ? (
                  <span className="bg-[#EDF1FE] text-sm text-[#4457D1] px-1 rounded-[2px]">
                    {APP_TEXT.POST_PAGE.FRIEND}
                  </span>
                ) : (
                  ''
                )}
                <span className="text-[#000000] font-bold">{post.nickName}</span>
              </div>
              <div className="flex">
                <span className="flex flex-1 items-center text-sm text-[#666666]">
                  {formatDate(post.createdAt, 'yyyy年M月d日 HH:mm')}
                </span>
                <div className="w-18 flex-none border border-[#B2B2B2] px-2 py-1/2 rounded-full">
                  <span className="text-sm">
                    {APP_TEXT.POST_PAGE.CATEGORYS[post.categoryCd - 1]}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-white slider-container relative">
            {isSelf &&
              post.isOneself === isOneselfType.self &&
              post.postStatus !== PostStatusType.finish && (
                <div
                  className="w-full h-full z-10 top-0 absolute bg-black/40"
                  onClick={() => {
                    goDetail(post);
                  }}
                  onKeyDown={() => {
                    goDetail(post);
                  }}
                >
                  <div className="h-[38px] py-1 px-4 flex items-center text-xl font-bold justify-center absolute z-12 top-3 left-3 text-white bg-black/60 rounded-3xl">
                    {(post.postStatus === PostStatusType.pending ||
                      post.postStatus === PostStatusType.review) && (
                      <>
                        <img className="w-6 h-6" src="/images/post/icon-funnel.png" alt="" />
                        {APP_TEXT.POST.REVIEW_TEXT}
                      </>
                    )}
                    {post.postStatus === PostStatusType.reject && (
                      <>
                        <img className="w-6 h-6" src="/images/post/icon-ban.png" alt="" />
                        {APP_TEXT.POST.REJECT_TEXT}
                      </>
                    )}
                  </div>
                </div>
              )}
            <SliderComponent {...settings}>
              {post.images?.map((img, i) => (
                <div
                  key={i}
                  className="w-screen aspect-square overflow-hidden"
                  onClick={() => {
                    goDetail(post);
                  }}
                  onKeyDown={() => {
                    goDetail(post);
                  }}
                >
                  <img
                    src={img.image}
                    alt={`slide-${i}`}
                    className="w-full h-full object-cover object-center"
                    draggable={false}
                  />
                </div>
              ))}
            </SliderComponent>
          </div>
          <div className="bg-white px-6 py-4">
            <div className="flex flex-1 gap-1 mt-4">
              <HeartAnimation>
                <span
                  className="flex gap-1"
                  onClick={() => onLike(post)}
                  onKeyDown={() => onLike(post)}
                >
                  {post.isLike === isLikeType.like ? (
                    <img
                      src="/images/post/icon-post-is-favorite.png"
                      alt=""
                      className="w-6 h-6 text-gray-800"
                    />
                  ) : (
                    <img
                      src="/images/post/icon-post-favorite.png"
                      alt=""
                      className="w-6 h-6 text-gray-800"
                    />
                  )}
                  <span>{post.likeCount}</span>
                </span>
              </HeartAnimation>
              {post.isOneself === isOneselfType.self && (
                <>
                  <img src="/images/post/icon-eye.png" alt="" className="w-6 h-6 text-gray-800" />
                  <span>{post.viewCount}</span>
                </>
              )}

              {children}
            </div>
            <ExpandableText text={post.postComment} maxLines={2} />
            <div className="flex flex-1 mt-2">
              <a className="flex gap-1  items-center" href="/map">
                <img
                  src="/images/post/icon-post-location.png"
                  alt=""
                  className="w-4 h-4 text-gray-800"
                />
                <span className="flex items-center text-[#4457D1] text-sm">
                  {APP_TEXT.POST_PAGE.MAP_TEXT}
                </span>
              </a>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
