import React, { useEffect, useRef, useState } from 'react';
interface ExpandableTextProps {
  text: string;
  maxLines?: number;
}
export const ExpandableText = ({ text, maxLines = 2 }: ExpandableTextProps) => {
  const [expanded, setExpanded] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        const isOverflow = textRef.current.scrollHeight > textRef.current.clientHeight;
        setIsOverflowing(isOverflow);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => {
      window.removeEventListener('resize', checkOverflow);
    };
  }, []);
  return (
    <div className="mt-2 relative">
      <div ref={textRef} className={expanded ? '' : `line-clamp-${maxLines}`}>
        {text}
      </div>
      {isOverflowing && (
        <span
          onClick={() => setExpanded(!expanded)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              setExpanded(!expanded);
            }
          }}
          className="text-[#666666] bg-white text-sm cursor-pointer absolute right-1 bottom-[2px]"
        >
          {expanded ? '收起' : '...続きを読む'}
        </span>
      )}
    </div>
  );
};
