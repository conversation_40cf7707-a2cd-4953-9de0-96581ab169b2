'use client';
import { CheckboxCards } from '@radix-ui/themes';

interface CheckBoxCardsProps {
  labels: string[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
}

const checkedStyle = 'bg-[#F6F8FF] border-[#4457D1] text-[#4457D1]';
const defultedStyle = 'bg-[#FFFFFF] border-[#666666] text-[#666666]';

export default function CheckBoxCards({ labels, selectedValues, onChange }: CheckBoxCardsProps) {
  return (
    <>
      <CheckboxCards.Root
        value={selectedValues}
        onValueChange={onChange}
        className="flex flex-wrap"
      >
        {labels.map((item, index) => {
          return (
            <div
              key={index}
              className={`border rounded px-3 py-2 w-fit mr-2 mb-2 ${selectedValues.includes(index.toString()) ? checkedStyle : defultedStyle}`}
            >
              <CheckboxCards.Item
                key={index}
                value={`${index}`}
                className={`flex ${selectedValues.includes(index.toString()) ? checkedStyle : defultedStyle}`}
              >
                <div key={index} className="checkbox-label order-[1] ml-1 text-[14px]">
                  {item}
                </div>
              </CheckboxCards.Item>
            </div>
          );
        })}
      </CheckboxCards.Root>
    </>
  );
}
