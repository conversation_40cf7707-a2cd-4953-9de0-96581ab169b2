import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import type React from 'react';

interface PostListWrapperProps {
  children: React.ReactNode;
  hasMore: boolean;
  loading: boolean;
  loadMore: () => void;
}

export default function PostListWrapper({
  children,
  hasMore,
  loading,
  loadMore,
}: PostListWrapperProps) {
  const { lastItemRef } = useInfiniteScroll({
    hasMore,
    loading,
    onLoadMore: loadMore,
    rootMargin: '100px',
  });

  return (
    <div className="relative">
      {children}
      {/* 👇 哨兵 div，不可见但用于触发加载 */}
      {/* <div ref={lastItemRef} className="h-[100px] bg-red-100" /> */}
    </div>
  );
}
