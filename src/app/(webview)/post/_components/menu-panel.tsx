import { postAPI } from '@/api/modules/post';
import { But<PERSON> } from '@/components/shared/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  Drawer<PERSON>itle,
  DrawerTrigger,
} from '@/components/shared/drawer';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { PostStatusType, isOneselfType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { usePostStore } from '@/store/post';
import type { PostInterface } from '@/types/post';
import { Plus, X } from 'lucide-react';
import { useState } from 'react';
export default function MenuPanel({ posts }: { posts: PostInterface }) {
  const { isShow, setDialog } = useMessageDialog();
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const {
    confirmPosts,
    setConfirmPosts,
    postAddress,
    setPostAddress,
    setPostLat,
    setPostLng,
    setPostId,
    postLat,
    postLng,
    setPostComment,
    postComment,
    setPostCategory,
    postCategory,
    onMap,
    setOnMap,
  } = usePostStore();
  const editPost = () => {
    setPostLat(posts.latitude);
    setPostLng(posts.longitude);
    setPostCategory(String(posts.categoryCd));
    setPostComment(String(posts.postComment));
    setPostId(String(posts.postId));
    setOpen(false);
    //編集画面へ遷移する
    router.push(ROUTES.POST.CONFIRM);
  };
  const deletePost = () => {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-2 text-left">
          <p className="text-[#000000] font-normal">投稿を削除してよろしいですか？</p>
          <p className="text-[#666666]">※一度削除すると元に戻すことはできません</p>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col">
          <Button
            variant="destructive"
            className="w-full"
            onClick={() => {
              postAPI.deletePost(posts.postId).then(() => {
                setOpen(false);
                setDialog(false);
                router.back();
              });
            }}
          >
            削除
          </Button>
          <DialogClose asChild>
            <TextButton
              className="mt-2 w-full"
              variant="muted"
              onClick={() => {
                setOpen(false);
              }}
            >
              キャンセル
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  };
  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <img src="/images/post/icon-post-detail.png" alt="" className="w-7 h-7 ml-auto mt-[-2px]" />
      </DrawerTrigger>
      <DrawerContent className="height-auto-important pb-5">
        <DrawerHeader className="relative">
          <DrawerTitle>{APP_TEXT.POST_PAGE.DETAIL_MENUE}</DrawerTitle>
          <DrawerDescription className="sr-only" />
          <DrawerClose asChild>
            <button type="button" className="absolute right-5 top-3">
              <X size={24} />
            </button>
          </DrawerClose>
        </DrawerHeader>
        {/* 自分 */}
        {posts.isOneself === isOneselfType.self && (
          <>
            {/* 編集する */}
            {posts.postStatus === PostStatusType.finish && (
              <div
                className="h-[40px] flex items-center px-10 mb-4"
                onKeyDown={() => {}}
                onClick={() => {
                  editPost();
                }}
              >
                <img src="/images/post/icon-edit.png" alt="" className="w-5 h-5 mr-1" />
                <span className="text-[#4457D1] text-[16px] font-bold">
                  {' '}
                  {APP_TEXT.POST_PAGE.DETAIL_EDIT}
                </span>
              </div>
            )}
            {/* 削除する */}
            <div
              className="h-[40px] flex items-center px-10 mb-4"
              onKeyDown={() => {}}
              onClick={() => {
                deletePost();
              }}
            >
              <img src="/images/post/icon-delete.png" alt="" className="w-5 h-5 mr-1" />
              <span className="text-[#CE0000] text-[16px] font-bold">
                {' '}
                {APP_TEXT.POST_PAGE.DETAIL_DELETE}
              </span>
            </div>
          </>
        )}
        {posts.isOneself === isOneselfType.other && (
          <>
            {/* 報告する */}
            <div className="h-[40px] flex items-center px-10 mb-4">
              <img src="/images/post/icon-post-record.png" alt="" className="w-5 h-5 mr-1" />
              <span className="text-[#CE0000] text-[16px] font-bold">
                {' '}
                {APP_TEXT.POST_PAGE.DETAIL_RECORD}
              </span>
            </div>
          </>
        )}
      </DrawerContent>
    </Drawer>
  );
}
