'use client';
import { Button } from '@/components/shared/button';
import { Select } from '@/components/shared/select';
import { APP_TEXT } from '@/const/text/app';
import type { PostFilters } from '@/types/post';
import { useEffect, useState } from 'react';
import CheckBoxCards from '../_components/checkbox-cards';
interface FilterFormProps {
  initialFilters?: PostFilters | null;
  isShowUser?: boolean;
  onApplyFilters: (filters: PostFilters) => void;
  onClearFilters: () => void;
  onClose: () => void;
}
export default function FilterPage({
  initialFilters,
  isShowUser = true,
  onApplyFilters,
  onClearFilters,
  onClose,
}: FilterFormProps) {
  const [selectedCategoryValues, setSelectedCategoryValues] = useState<string[]>(
    initialFilters?.selectedCategoryValues || ['0'],
  );
  const [selectedUserValues, setSelectedUserValues] = useState<string[]>(
    initialFilters?.selectedUserValues || ['0'],
  );
  const [selectedDistanceValue, setSelectedDistanceValue] = useState<string>(
    initialFilters?.selectedDistanceValue || 'none',
  );

  const isDisabled =
    selectedCategoryValues.length === 1 &&
    selectedCategoryValues.includes('0') &&
    selectedUserValues.length === 1 &&
    selectedUserValues.includes('0') &&
    selectedDistanceValue === 'none';

  const SELECT_OPTIONS = [
    { value: 'none', name: '指定なし' },
    { value: '500', name: '500m' },
    { value: '1000', name: '1km' },
    { value: '2000', name: '2km' },
    { value: '5000', name: '5km' },
    { value: '10000', name: '10km' },
    { value: '20000', name: '20km' },
  ];

  const handleClearClick = () => {
    setSelectedCategoryValues(['0']);
    setSelectedUserValues(['0']);
    setSelectedDistanceValue('none');
    onClearFilters();
    onClose();
  };

  const handleDistanceChange = (value: string) => {
    setSelectedDistanceValue(value);
  };

  const handleApplyClick = () => {
    onApplyFilters({
      selectedCategoryValues,
      selectedUserValues,
      selectedDistanceValue,
    });
    onClose();
  };

  return (
    <>
      <div className="bg-[#FFFFFF] flex-col flex h-screen">
        <div className="px-6 pt-3">
          <div className="pb-2 font-bold">{APP_TEXT.POST_PAGE.CATEGORY}</div>
          <CheckBoxCards
            selectedValues={selectedCategoryValues}
            onChange={setSelectedCategoryValues}
            labels={APP_TEXT.POST_PAGE.CATEGORYS}
          />
        </div>
        {isShowUser && (
          <div className="px-6 pt-1">
            <div className="pb-2 font-bold">{APP_TEXT.POST_PAGE.USERS}</div>
            <CheckBoxCards
              selectedValues={selectedUserValues}
              onChange={setSelectedUserValues}
              labels={APP_TEXT.POST_PAGE.FILTER_USERS}
            />
          </div>
        )}

        <div className="px-6 pt-2">
          <div className="pb-2 font-bold">{APP_TEXT.POST_PAGE.DISTANCE}</div>
          <Select
            defaultValue={selectedDistanceValue}
            options={SELECT_OPTIONS}
            title={APP_TEXT.POST_PAGE.DISTANCE}
            onChange={(value) => handleDistanceChange(value)}
          />
        </div>

        <div className="px-6 fixed mb-5 left-0 right-0 bottom-3 z-[2] ">
          <Button
            disabled={isDisabled}
            onClick={handleApplyClick}
            className="flex flex-col items-center w-full"
            type="button"
          >
            <span className="text-[16px]">{APP_TEXT.POST_PAGE.APPLY_CONDITIONS}</span>
          </Button>

          <Button
            disabled={isDisabled}
            onClick={handleClearClick}
            className="mt-4 flex flex-col items-center w-full bg-[#FFFFFF] border border-[#4457D1]"
            type="button"
          >
            <span className="text-[16px] text-[#4457D1]">{APP_TEXT.POST_PAGE.CLEAR}</span>
          </Button>
        </div>
      </div>
    </>
  );
}
