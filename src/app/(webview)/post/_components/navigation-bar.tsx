'use client';

import LayoutSwitcher from '@/components/shared/post-layout-switcher';
import Link from '@/components/shared/router-link';
import { Select } from '@/components/shared/select';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useSlidePage } from '@/hooks/use-slide-page';
import { cn } from '@/lib/utils';
import { usePostStore } from '@/store/post';
import type { PostFilters } from '@/types/post';
import React, { useState } from 'react';
import FilterPage from './filter-page';

export default function NavigationBar({
  showToolbar,
  filters,
  onApplyFilters,
  onClearFilters,
}: {
  showToolbar: boolean;
  filters: PostFilters | null;
  onApplyFilters: (filters: PostFilters) => void;
  onClearFilters: () => void;
}) {
  const SELECT_OPTIONS = [
    { value: '1', name: '新着順' },
    { value: '2', name: '人気順' },
  ];
  const { sortKind, setSortKind } = usePostStore();
  const { setSlidePage } = useSlidePage();
  const router = useRouter();
  const safeArea = useSafeArea();
  const handleOptionChange = (value: number) => {
    setSortKind(value);
  };

  const openFilterSlidePage = () => {
    setSlidePage(true, {
      title: APP_TEXT.COUPON.FILTER,
      content: (
        <FilterPage
          initialFilters={filters}
          onApplyFilters={(filter) => onApplyFilters(filter)}
          onClearFilters={onClearFilters}
          onClose={() => setSlidePage(false)}
        />
      ),
      isOverAll: true,
      enableClose: false,
      enableBack: true,
      slideFrom: 'bottom',
    });
  };
  return (
    <div
      className={cn(
        'bg-[#F2F2F2] transition-transform duration-100 ease-in-out',
        showToolbar ? 'sticky top-0 z-20' : '',
      )}
      style={{ paddingTop: safeArea.top }}
    >
      <div className="flex pt-6 px-6">
        <div className="flex-1 text-2xl font-bold">{APP_TEXT.POST_PAGE.TITLE}</div>
        <span className="w-19 flex items-center flex-row flex-none gap-3">
          <Link href={ROUTES.POST.LIKE_LIST}>
            <img
              src="/images/post/icon-post-favorite.png"
              alt=""
              className="w-6 h-6 text-gray-800"
            />
          </Link>
          <div
            onClick={() => {
              router.push(ROUTES.POST.MY_PHOTO_SUBMISSIONS);
            }}
            onKeyDown={() => {
              router.push(ROUTES.POST.MY_PHOTO_SUBMISSIONS);
            }}
          >
            <img src="/images/post/icon-post-image.png" alt="" className="w-8 h-8 text-gray-800" />
          </div>
        </span>
      </div>
      <div className="flex py-4 px-6 justify-between">
        <LayoutSwitcher className="flex-1" />
        <div className="flex flex-none gap-4">
          <div
            className="flex flex-none flex-col items-center"
            onClick={() => {
              openFilterSlidePage();
            }}
            onKeyDown={() => {
              openFilterSlidePage();
            }}
          >
            <div className="mb-1 flex h-6 w-6 items-center justify-center">
              <img
                src="/images/post/icon-post-filter.png"
                alt=""
                className="w-6 h-6 text-gray-800"
              />
            </div>
            <span className="text-xs">{APP_TEXT.POST_PAGE.FILTER}</span>
          </div>
          <Select
            className="w-27 flex-none border-[#B2B2B2]"
            defaultValue={SELECT_OPTIONS[sortKind - 1].value}
            options={SELECT_OPTIONS}
            title="並び替え"
            onChange={(value: string) => handleOptionChange(Number.parseInt(value))}
          />
        </div>
      </div>
    </div>
  );
}
