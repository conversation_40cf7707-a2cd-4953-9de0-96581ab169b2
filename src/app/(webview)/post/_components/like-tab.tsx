'use client';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { likeTabType } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import React from 'react';
interface TabMenuProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}
function LikeTab({ activeTab, onTabChange }: TabMenuProps) {
  return (
    <div className="w-full flex justify-center">
      <Tabs
        defaultValue={activeTab}
        value={activeTab}
        onValueChange={(value) => onTabChange(value)}
        className="w-full max-w-md p-4"
      >
        <TabsList className="grid grid-cols-2 w-full bg-card rounded-lg text-[#000000]">
          <TabsTrigger value={likeTabType.ToMe} className="data-[state=active]:font-bold">
            {APP_TEXT.POST.TAB_LEFT}
          </TabsTrigger>
          <TabsTrigger value={likeTabType.ToOther} className="data-[state=active]:font-bold">
            {APP_TEXT.POST.TAB_RIGHT}
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
export default LikeTab;
