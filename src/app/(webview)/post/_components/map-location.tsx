'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useSearchHistoryStore } from '@/store/search-history-store';

import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import { Clock, Search, Tent, X } from 'lucide-react';
import { useEffect, useState } from 'react';
/**
 * イベントキーワード検索コンポーネントのProps
 */
interface KeywordProps {
  /** 閉じるボタンのコールバック関数 */
  onClose: () => void;
  onOK: () => void;
  address?: string;
}

/**
 * イベントキーワード検索コンポーネント
 * 検索候補表示と検索履歴管理機能を提供
 */
export default function MapLocation({ onClose, onOK, address }: KeywordProps) {
  return (
    <div className="bg-card h-full flex flex-col">
      <div className="text-xl m-6">{address}</div>
      <Button
        className="bg-primary disabled:bg-primary-light text-white  font-bold h-12   rounded-[25px] w-[88vw]  ml-6 mr-6 mb-6"
        type="button"
        onClick={onOK}
      >
        <span className="text-[16px] text-white">{APP_TEXT.POST_MAP_PAGE.CONFIRM}</span>
      </Button>
    </div>
  );
}
