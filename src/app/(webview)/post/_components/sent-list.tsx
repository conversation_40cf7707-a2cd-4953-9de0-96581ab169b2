import { postAPI } from '@/api/modules/post';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useRouter } from '@/hooks/use-next-navigation';
import type { GivePost } from '@/types/post';
import React, { useEffect, useRef, useState } from 'react';
import PhotoEmpty from './photo-empty';
import TopButton from './top-button';

const SentList = () => {
  const router = useRouter();
  const [sentList, setSentList] = useState<GivePost[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [showTopButton, setShowTopButton] = useState(false);
  const observer = useRef<IntersectionObserver | null>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const loadLikeData = async () => {
    if (loading || !hasMore) return;
    setLoading(true);
    try {
      try {
        const res = await postAPI.getGivesLikeList({ page, limit: 15 });
        if (res.posts) {
          setSentList((prev) => [...prev, ...res.posts]);
          res?.pagination?.page >= res.pagination?.pages ? setHasMore(false) : setHasMore(true);
          setPage((prev) => prev + 1);
        }
      } catch (error) {
        console.error('Error fetching like data:', error);
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching like data:', error);
    } finally {
      setLoading(false);
    }
  };

  const { lastItemRef } = useInfiniteScroll({
    hasMore,
    loading,
    onLoadMore: loadLikeData,
    rootMargin: '100px',
  });

  const scrollToTop = () => {
    listRef.current?.scrollTo({ behavior: 'smooth', top: 0 });
  };

  const goDetail = (post: GivePost) => {
    router.push(`${ROUTES.POST.DETAIL}?queryUserId=${post.userId}&postId=${post.postId}`);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    loadLikeData();
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  useEffect(() => {
    const listElement = listRef.current;
    if (!listElement) return;
    const handleScroll = () => {
      if (listElement.scrollTop > 50) {
        setShowTopButton(true);
      } else {
        setShowTopButton(false);
      }
    };

    listElement.addEventListener('scroll', handleScroll);
    return () => {
      listElement.removeEventListener('scroll', handleScroll);
    };
  }, []);
  return (
    <>
      <div
        className="flex-1 overflow-auto"
        ref={listRef}
        style={{
          height: 'calc(100vh - 61px - 48px - 64px)',
        }}
      >
        {/* データがない場合の表示 */}
        {sentList.length === 0 && !loading && (
          <PhotoEmpty>
            <div className="text-center text-[#666666] text-lg mt-4 font-bold">
              {APP_TEXT.POST.SENT_NO_DATA}
            </div>
            <div className="w-40 text-center text-[#666666] text-sm mt-2">
              {APP_TEXT.POST.SUB_MESSAGE}
            </div>
            <Button
              type="button"
              onClick={() => {
                // 投稿画面に遷移 TODO:
                router.push('/post');
              }}
              className="w-[327px] h-12 mt-4"
            >
              {COMMON_TEXT.BUTTON.VIEW_PHOTO_POST}
            </Button>
          </PhotoEmpty>
        )}

        {sentList.length > 0 && (
          <div className="grid grid-cols-3 gap-1 mt-2 max-w-2xl mx-auto">
            {sentList.map((item, index) => (
              <div
                ref={sentList.length === index + 1 ? lastItemRef : null}
                key={index}
                className="relative aspect-square"
                onClick={() => {
                  goDetail(item);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    goDetail(item);
                  }
                }}
              >
                <img src={item?.thumbnailImage} alt="" className="w-full h-full object-cover" />
                <div className="absolute w-12 h-6 px-2 py-1 bg-white bottom-2 left-2 rounded-3xl flex items-center justify-center">
                  <img
                    src="/images/post/icon-post-is-favorite.png"
                    alt=""
                    className="w-4 h-4 text-gray-800"
                  />
                  <span className="ml-1">{item.likeCount}</span>
                </div>
              </div>
            ))}
          </div>
        )}
        <TopButton showTopButton={showTopButton} scrollToTop={scrollToTop} />
      </div>
    </>
  );
};

export default SentList;
