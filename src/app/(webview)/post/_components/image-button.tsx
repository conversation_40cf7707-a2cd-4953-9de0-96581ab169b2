'use client';

import React from 'react';

const ImageButton = ({
  index,
  imgSrc,
  onClick,
}: { index: number; imgSrc: string; onClick: (index: number) => void }) => {
  return (
    <button
      type="button"
      onClick={() => onClick(index)}
      className="absolute left-3 bottom-3 items-bottom justify-left"
    >
      <div className="w-14 h-14">
        <img src={imgSrc} alt="Button icon" className="w-full h-full object-cover rounded" />
      </div>
    </button>
  );
};

export default ImageButton;
