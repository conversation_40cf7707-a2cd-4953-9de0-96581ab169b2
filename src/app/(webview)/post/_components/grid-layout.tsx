'use client';

import { isLikeType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useRouter } from '@/hooks/use-next-navigation';
import type { PostInterface } from '@/types/post';
interface PostListWrapperProps {
  postsList: PostInterface[];
  hasMore: boolean;
  loading: boolean;
  loadMore: () => void;
}

export default function GridLayout({
  postsList,
  hasMore,
  loading,
  loadMore,
}: PostListWrapperProps) {
  const router = useRouter();
  const { lastItemRef } = useInfiniteScroll({
    hasMore,
    loading,
    onLoadMore: loadMore,
    rootMargin: '0px',
  });
  const handleDetailClick = (post: PostInterface) => {
    router.push(`${ROUTES.POST.DETAIL}?queryUserId=${post.userId}&postId=${post.postId}`);
  };
  return (
    <>
      <div className="grid grid-cols-3 gap-1 mt-2 max-w-2xl mx-auto">
        {postsList.map((item, index) => (
          <div
            key={index}
            ref={index === postsList.length - 1 ? lastItemRef : null}
            className="relative aspect-square"
            onClick={() => {
              handleDetailClick(item);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleDetailClick(item);
              }
            }}
          >
            <div>
              <img src={item?.images[0]?.image} alt="" className="w-full h-full object-cover" />
            </div>
            <div className="absolute  h-6 px-2 py-1 bg-white bottom-2 left-2 rounded-3xl flex items-center justify-center">
              {item.isLike === isLikeType.like ? (
                <img
                  src="/images/post/icon-post-is-favorite.png"
                  alt=""
                  className="w-4 h-4 text-gray-800"
                />
              ) : (
                <img
                  src="/images/post/icon-post-favorite.png"
                  alt=""
                  className="w-4 h-4 text-gray-800"
                />
              )}
              <span className="ml-1">{item.likeCount}</span>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
