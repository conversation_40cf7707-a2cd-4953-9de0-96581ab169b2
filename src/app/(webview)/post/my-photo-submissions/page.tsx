'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import LayoutSwitcher from '@/components/shared/post-layout-switcher';
import Link from '@/components/shared/router-link';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useRouter } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import type { PostClassifyListRequest, PostFilters, PostInterface } from '@/types/post';
import { Plus } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import Slider, { type Settings } from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { postAPI } from '@/api/modules/post';
import { PostStatusType, isLikeType, isOneselfType } from '@/const/app';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useAuthStore } from '@/store/auth';
import { useMyPhotoSubmissionStore } from '@/store/my-photo-submission-store';
import { usePostStore } from '@/store/post';
import { formatDate } from '@/utils/date-format';
import { set } from 'date-fns';
import { ExpandableText } from '../_components/expandable-text';
import FilterPage from '../_components/filter-page';
import PhotoEmpty from '../_components/photo-empty';
import TopButton from '../_components/top-button';
import UserHeader from '../_components/user-header';

const MyPhotoSubmissions = () => {
  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    customPaging: () => (
      <div className="mt-2">
        <div className="w-3 h-3 rounded-full bg-gray-300 mx-1" />
      </div>
    ),
    dotsClass: 'slick-dots flex justify-center mt-4',
    autoplay: false,
    arrows: false,
  };
  const SliderComponent = Slider as unknown as React.ComponentType<Settings>;
  const { userPost, setPostList, resetPostList } = useMyPhotoSubmissionStore();
  const { layout, setLayout } = usePostStore();
  const router = useRouter();
  const { user } = useAuthStore();
  const [showTopButton, setShowTopButton] = useState(false);
  const [showToolbar, setShowToolbar] = useState(true);
  const lastScrollY = useRef(0);
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const { location } = useGeolocation();
  const [filters, setFilters] = useState<PostFilters | null>(null);
  const observer = useRef<IntersectionObserver | null>(null);
  const { setSlidePage } = useSlidePage();
  const goDetail = (post: PostInterface) => {
    router.push(`${ROUTES.POST.DETAIL}?queryUserId=${userPost.userId}&postId=${post.postId}`);
  };
  const loadMore = async (curentPage?: number) => {
    if (loading || !hasMore) return;
    setLoading(true);
    try {
      try {
        const limit = 15;
        const pageSize = curentPage || page;
        const params: PostClassifyListRequest = {
          queryUserId: Number(user?.id),
          currentPositionLon: String(location.lng),
          currentPositionLat: String(location.lat),
          pagination: { page: pageSize, limit },
        };
        if (filters?.selectedCategoryValues) {
          params.categoryCd = filters?.selectedCategoryValues as unknown as number[];
        }

        if (
          filters &&
          filters?.selectedDistanceValue !== 'none' &&
          filters?.selectedDistanceValue !== null
        ) {
          params.distFromCurrloc = Number(filters?.selectedDistanceValue);
        }
        if (pageSize === 1) resetPostList();
        const res = await postAPI.getPostClassifyList(params);
        if (res) {
          setPostList(res);
          pageSize >= res.pagination?.pages ? setHasMore(false) : setHasMore(true);
          setPage((prev) => prev + 1);
        }
      } catch (error) {
        console.error('Error fetching like data:', error);
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching like data:', error);
    } finally {
      setLoading(false);
    }
  };
  const { lastItemRef } = useInfiniteScroll({
    hasMore,
    loading,
    onLoadMore: loadMore,
    rootMargin: '100px',
  });

  const openFilterSlidePage = () => {
    setSlidePage(true, {
      title: APP_TEXT.COUPON.FILTER,
      content: (
        <FilterPage
          initialFilters={filters}
          isShowUser={false}
          onApplyFilters={(filter) => {
            setFilters(filter);
            setPage(1);
            setHasMore(true);
          }}
          onClearFilters={() => setFilters(null)}
          onClose={() => setSlidePage(false)}
        />
      ),
      isOverAll: true,
      enableClose: false,
      enableBack: true,
      slideFrom: 'bottom',
    });
  };
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setPage(1);
    setHasMore(true);
    loadMore(1);
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [filters]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    document.documentElement.scrollTop = 0;
  }, [layout]);
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = document.documentElement.scrollTop;
      if (currentScrollY > lastScrollY.current) {
        setShowToolbar(false);
      } else if (currentScrollY < lastScrollY.current) {
        setShowToolbar(true);
      }
      // top button
      if (currentScrollY > 50) {
        setShowTopButton(true);
      } else {
        setShowTopButton(false);
      }
      lastScrollY.current = currentScrollY;
    };

    window.addEventListener('scroll', handleScroll);
    setLayout('grid');
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [setLayout]);

  return (
    <>
      <TopBar onBack={() => router.back()} title={APP_TEXT.MY_PHOTO_SUBMISSIONS.TITLE} />
      {/* user header */}
      <UserHeader userPost={userPost} />
      {userPost.posts.length === 0 && !loading && (
        <div
          className="flex flex-col items-center justify-center"
          style={{
            height: 'calc(100vh - 61px - 48px - 68px)',
          }}
        >
          <PhotoEmpty>
            <div className="w-[200px] text-center text-[#666666] text-lg mt-4 font-bold">
              {APP_TEXT.MY_PHOTO_SUBMISSIONS.NO_DATA}
            </div>
            <div className="w-[190px] text-center text-[#666666] text-sm mt-2">
              {APP_TEXT.MY_PHOTO_SUBMISSIONS.SUB_MESSAGE}
            </div>
            <Button
              type="button"
              onClick={() => {
                // 投稿画面に遷移 TODO:
                router.push('/post');
              }}
              className="w-[327px] h-12 mt-4"
            >
              <Plus />
              {COMMON_TEXT.BUTTON.POST}
            </Button>
          </PhotoEmpty>
        </div>
      )}
      {userPost.posts.length > 0 && (
        <>
          {/*  View切り替え */}
          <div
            ref={toolbarRef}
            className={cn(
              'flex h-20 py-4 px-6  bg-[#F2F2F2] justify-between transition-transform duration-100 ease-in-out',
              showToolbar ? 'sticky top-[48px] z-20' : '',
            )}
          >
            <LayoutSwitcher />
            <div
              onClick={() => {
                openFilterSlidePage();
              }}
              onKeyDown={() => {
                openFilterSlidePage();
              }}
              className="flex flex-none flex-col items-center"
            >
              <div className="mb-1 flex h-6 w-6 items-center justify-center">
                <img
                  src="/images/post/icon-post-filter.png"
                  alt=""
                  className="w-6 h-6 text-gray-800"
                />
              </div>
              <span className="text-xs">{APP_TEXT.POST_PAGE.FILTER}</span>
            </div>
          </div>
          {layout === 'overview' ? (
            <div>
              {userPost.posts.map((post, index) => (
                <div
                  key={post.postId}
                  ref={userPost.posts.length === index + 1 ? lastItemRef : null}
                  className="mb-4  bg-white"
                >
                  <div className="flex py-3 px-6">
                    <span className="flex flex-1 items-center text-sm text-[#666666]">
                      {formatDate(post.createdAt, 'yyyy年M月d日 HH:mm')}
                    </span>
                    <div className="w-18 flex-none border border-[#B2B2B2] px-2 py-1/2 rounded-full">
                      <span className="text-sm">
                        {APP_TEXT.POST_PAGE.CATEGORYS[post.categoryCd - 1]}
                      </span>
                    </div>
                  </div>
                  <div className="bg-white slider-container relative">
                    {post.postStatus !== PostStatusType.finish && (
                      <div
                        className="w-full h-full z-10 top-0 absolute bg-black/40"
                        onClick={() => {
                          goDetail(post);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            goDetail(post);
                          }
                        }}
                      >
                        <div className="h-[38px] py-1 px-4 flex items-center text-xl font-bold justify-center absolute z-12 top-3 left-3 text-white bg-black/60 rounded-3xl">
                          {(post.postStatus === PostStatusType.pending ||
                            post.postStatus === PostStatusType.review) && (
                            <>
                              <img className="w-6 h-6" src="/images/post/icon-funnel.png" alt="" />
                              {APP_TEXT.POST.REVIEW_TEXT}
                            </>
                          )}
                          {post.postStatus === PostStatusType.reject && (
                            <>
                              <img className="w-6 h-6" src="/images/post/icon-ban.png" alt="" />
                              {APP_TEXT.POST.REJECT_TEXT}
                            </>
                          )}
                        </div>
                      </div>
                    )}
                    <SliderComponent {...settings}>
                      {post.images.map((Item, i) => (
                        <div
                          key={post.postId + post.userId}
                          className="w-screen aspect-square overflow-hidden"
                          onClick={() => {
                            goDetail(post);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              goDetail(post);
                            }
                          }}
                        >
                          <img
                            src={Item.image}
                            alt={`slide-${i}`}
                            className="w-full h-full object-cover object-center"
                            draggable={false}
                          />
                        </div>
                      ))}
                    </SliderComponent>
                  </div>
                  <div className="bg-white px-6 py-4">
                    <div className="flex flex-1 gap-1 mt-4">
                      <span className="flex gap-1">
                        {post.isLike === isLikeType.like ? (
                          <img
                            src="/images/post/icon-post-is-favorite.png"
                            alt=""
                            className="w-6 h-6 text-gray-800"
                          />
                        ) : (
                          <img
                            src="/images/post/icon-post-favorite.png"
                            alt=""
                            className="w-6 h-6 text-gray-800"
                          />
                        )}
                        <span>{post.likeCount}</span>
                      </span>
                      {post.isOneself === isOneselfType.self && (
                        <>
                          <img
                            src="/images/post/icon-eye.png"
                            alt=""
                            className="w-6 h-6 text-gray-800"
                          />
                          <span>{post.viewCount}</span>
                        </>
                      )}
                    </div>
                    <ExpandableText text={post.postComment} maxLines={2} />
                    <div className="flex flex-1 mt-2">
                      <a className="flex gap-1  items-center" href="/map">
                        <img
                          src="/images/post/icon-post-location.png"
                          alt=""
                          className="w-4 h-4 text-gray-800"
                        />
                        <span className="flex items-center text-[#4457D1] text-sm">
                          {APP_TEXT.POST_PAGE.MAP_TEXT}
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : layout === 'grid' ? (
            <div className="grid grid-cols-3 gap-1 mt-2 max-w-2xl mx-auto">
              {userPost.posts.map((item, index) => (
                <div
                  ref={userPost.posts.length === index + 1 ? lastItemRef : null}
                  key={item.postId}
                  className="relative aspect-square"
                  onClick={() => {
                    goDetail(item);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      goDetail(item);
                    }
                  }}
                >
                  {item.postStatus !== PostStatusType.finish && (
                    <div className="w-full h-full z-5 top-0 absolute bg-black/40">
                      <div className="h-[25px] py-1 px-2 flex items-center text-sm font-bold justify-center absolute z-12 top-2 left-2 text-white bg-black/60 rounded-3xl">
                        {(item.postStatus === PostStatusType.pending ||
                          item.postStatus === PostStatusType.review) && (
                          <>
                            <img className="w-5 h-5" src="/images/post/icon-funnel.png" alt="" />
                            {APP_TEXT.POST.REVIEW_TEXT}
                          </>
                        )}
                        {item.postStatus === PostStatusType.reject && (
                          <>
                            <img className="w-5 h-5" src="/images/post/icon-ban.png" alt="" />
                            {APP_TEXT.POST.REJECT_TEXT}
                          </>
                        )}
                      </div>
                    </div>
                  )}
                  <img src={item?.images[0]?.image} alt="" className="w-full h-full object-cover" />
                  {item.postStatus === PostStatusType.finish && (
                    <div className="absolute z-2  h-6 px-2 py-1 bg-white bottom-2 left-2 rounded-3xl flex items-center justify-center">
                      <img
                        src="/images/post/icon-post-is-favorite.png"
                        alt=""
                        className="w-4 h-4 text-gray-800"
                      />
                      <span>{item.likeCount}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : null}
          {/* {loading && <div className="text-center">{COMMON_TEXT.MESSAGE.LOADING}</div>}
          {!hasMore && <div className="text-center">{COMMON_TEXT.MESSAGE.NO_DATA}</div>} */}
          {/* Top Button */}
          <TopButton showTopButton={showTopButton} />
        </>
      )}
    </>
  );
};

export default MyPhotoSubmissions;
