'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useRouter } from 'next/navigation';

export default function PostPolicyPage() {
  const router = useRouter();
  const handleBack = () => {
    router.back();
  };
  return (
    <>
      <TopBar enableBack={true} title={APP_TEXT.POST_POLICY_PAGE.TITLE} />
      <div className="bg-white">
        <div className="flex-col flex ml-4 mr-4 pt-4">
          <div>{APP_TEXT.POST_POLICY_PAGE.TEXT1}</div>
          <div className="mt-6 mb-6 ml-1">
            {APP_TEXT.POST_POLICY_PAGE.POLICY.map((item, index) => (
              <div key={index} className="flex gap-1">
                <div>・</div>
                <div className="flex-1 break-words">{item}</div>
              </div>
            ))}
          </div>
          <div>{APP_TEXT.POST_POLICY_PAGE.TEXT2}</div>
          <div>{APP_TEXT.POST_POLICY_PAGE.TEXT3}</div>
          <Button
            className="bg-primary disabled:bg-primary-light text-white  font-bold h-12   rounded-[25px] w-[88vw] mt-4  mb-6"
            type="button"
            onClick={handleBack}
          >
            <span className="text-[16px] text-white">{COMMON_TEXT.BUTTON.CLOSE}</span>
          </Button>
        </div>
      </div>
    </>
  );
}
