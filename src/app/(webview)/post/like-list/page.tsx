'use client';
import TopBar from '@/components/layout/top-bar';
import { likeTabType } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import { useSafeArea } from '@/hooks/use-safe-area';
import React, { useState } from 'react';
import LikeTab from '../_components/like-tab';
import ReceivedList from '../_components/received-list';
import SentList from '../_components/sent-list';

function LikeList() {
  const [activeTab, setActiveTab] = useState<string>(likeTabType.ToMe);
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const safeArea = useSafeArea();
  return (
    <>
      <TopBar title={APP_TEXT.POST.TITLE} />
      {/* もらった おくった  tab */}
      <LikeTab activeTab={activeTab} onTabChange={handleTabChange} />
      {activeTab === likeTabType.ToMe && <ReceivedList />}
      {activeTab === likeTabType.ToOther && <SentList />}
    </>
  );
}

export default LikeList;
