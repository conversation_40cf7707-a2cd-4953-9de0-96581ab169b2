'use client';
import IconEvent from '@/components/icons/icon-event';
import TopBar from '@/components/layout/top-bar';
import { SearchInput } from '@/components/shared/search-input';
import { APP_TEXT } from '@/const/text/app';
import { useGlobalVar } from '@/hooks/use-global-var';
import { useSlidePage } from '@/hooks/use-slide-page';
import { usePostStore } from '@/store/post';
import { type LatLng, type Marker, MarkerType } from '@/types/map';
import { Map as GoogleMap, useMap, useMapsLibrary } from '@vis.gl/react-google-maps';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import MergedMarkers from '../../../../components/shared/map/merged-markders';
import PostKeyword from '../_components/keyword-search';
import MapDecidePanel from '../_components/map-decide-panel';

export default function PostConfirmPage() {
  const router = useRouter();
  const [selectedCategoryValues, setSelectedCategoryValues] = useState<string[]>(['']);
  const [keyword, setKeyWord] = useState<string>('');

  const [lat, setLat] = useState<number>(0);
  const [lng, setLng] = useState<number>(0);
  const [address, setAddress] = useState<string>('');
  const { setSlidePage } = useSlidePage();
  const geocodingLib = useMapsLibrary('geocoding');
  const map = useMap();
  const [isSearching, setIsSearching] = useState(false);
  const { setPostAddress, setPostLat, setPostLng } = usePostStore();

  const { setFooterMenuSettingOptions } = useGlobalVar();

  const [isOpenDecide, setIsOpenDecide] = useState(false);

  useEffect(() => {
    setFooterMenuSettingOptions({ isShow: false });
    return () => {
      setFooterMenuSettingOptions({ isShow: true });
    };
  }, []);

  const handleClickMarker = (marker: Marker) => {
    //表示する
    if (marker !== undefined && marker.address !== undefined) {
      openDecidePage();
    }
  };
  const handleKeywordSearch = (keyword: string, lat: number, lng: number) => {
    const list: Marker[] = [
      {
        id: '1',
        name: '',
        type: MarkerType.POST,
        address: keyword,
        latitude: String(lat),
        longitude: String(lng),
        imagePath: '',
        isCurrent: true,
      },
    ];
    setMarkers(list);
    setAddress(keyword);
  };

  const [markers, setMarkers] = useState<Marker[]>([]);

  // 住所検索処理
  const handleSearch = useCallback(
    async (searchValue: string) => {
      if (!searchValue.trim() || !geocodingLib || !map) return;

      setIsSearching(true);
      try {
        const geocoder = new geocodingLib.Geocoder();
        const result = await geocoder.geocode({ address: searchValue });

        if (result.results && result.results.length > 0) {
          const location = result.results[0].geometry.location;
          const latLng = { lat: location.lat(), lng: location.lng() };
          const formattedAddress = result.results[0].formatted_address;

          // マップを移動
          map.panTo(latLng);
          map.setZoom(15);
          const keys = { address: [formattedAddress], lat: latLng.lat, lng: latLng.lng };
          return keys;
        }
      } catch (error) {
        console.error('Geocoding error:', error);
        return {};
      } finally {
        setIsSearching(false);
      }
    },
    [geocodingLib, map],
  );

  const handleClearKeyword = () => {};
  const openKeywordSlidePage = useCallback(() => {
    setSlidePage(true, {
      title: APP_TEXT.POST_MAP_PAGE.TITLE,
      content: (
        <PostKeyword
          initialKeyword={keyword || ''}
          onKeywordSelect={handleKeywordSearch} //
          onClose={() => setSlidePage(false)}
          keywordSearchFunction={handleSearch}
          functionType="photo"
          functionTypeName={APP_TEXT.POST_MAP_PAGE.TITLE}
          suggestionsIcon={<IconEvent className="h-6 w-6 text-muted-foreground" />}
        />
      ),
      isOverAll: true,
      enableClose: false,
      enableBack: true,
      slideFrom: 'bottom',
    });
  }, [keyword, handleKeywordSearch, setSlidePage, handleSearch]);

  const openDecidePage = () => {
    setIsOpenDecide(true);
  };

  const mapCenter = { lat: 35.6762, lng: 139.6503 };
  const position = { lat: 0, lng: 0 };
  const [currentPosition, setCurrentPosition] = useState<LatLng>(
    position, // 東京駅をデフォルト位置とする
  );

  const onDecide = () => {
    //決定動作
    setSlidePage(false);
    setPostAddress(address);
    setPostLat(String(lat));
    setPostLng(String(lng));
    setIsOpenDecide(false);
    router.back();
  };

  return (
    <div className="bg-white">
      <TopBar title={APP_TEXT.POST_MAP_PAGE.TITLE} />
      <div onClick={openKeywordSlidePage} className="flex-1 ml-4 mr-4 pt-4 ">
        <SearchInput
          placeholder="駅、都市、キーワードを入力"
          value={address || ''}
          readOnly
          className="cursor-pointer  bg-white"
          showClearButton={address !== ''}
          onClear={handleClearKeyword}
        />
      </div>
      <div className="w-full h-dvh mt-4 overflow-hidden">
        <GoogleMap
          defaultCenter={mapCenter}
          defaultZoom={15}
          gestureHandling="greedy"
          disableDefaultUI={false}
          mapId="location-picker-map"
        >
          <MergedMarkers markers={markers} onMarkerClick={handleClickMarker} />
        </GoogleMap>
      </div>
      <MapDecidePanel isOpen={isOpenDecide} address={address} onClose={onDecide} />
    </div>
  );
}
