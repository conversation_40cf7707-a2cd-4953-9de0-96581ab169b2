'use client';

import { postAPI } from '@/api/modules/post';
import { Button } from '@/components/shared/button';
import { isLikeType, isOneselfType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { usePostStore } from '@/store/post';
import type { PostFilters, PostInterface, PostListRequest } from '@/types/post';
import { useEffect, useRef, useState } from 'react';
import GridLayout from './_components/grid-layout';
import NavigationBar from './_components/navigation-bar';
import OverviewLayout from './_components/overview-layout';

function useUpdateEffect(effect: React.EffectCallback, deps: React.DependencyList) {
  const isFirstRender = useRef(true);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    return effect();
  }, deps);
}

export default function Post() {
  const router = useRouter();
  const { layout, setLayout } = usePostStore();
  const { posts, setPosts } = usePostStore();
  const { sortKind, setSortKind } = usePostStore();
  const [filters, setFilters] = useState<PostFilters | null>(null);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showToolbar, setShowToolbar] = useState(false);
  const [showTopButton, setShowTopButton] = useState(false);
  const { location } = useGeolocation();
  const safeArea = useSafeArea();
  const scrollPositionRef = useRef({
    overview: {
      scrollY: 0,
      lastScrollY: 0,
    },
    grid: {
      scrollY: 0,
      lastScrollY: 0,
    },
  });
  const fetchPosts = async () => {
    if (loading || !hasMore) return;
    setLoading(true);
    try {
      const limit = 15;
      const params: PostListRequest = {
        sortKind,
        currentPositionLon: String(location.lng),
        currentPositionLat: String(location.lat),
        pagination: { page, limit },
      };
      if (filters?.selectedCategoryValues) {
        params.categoryCd = filters?.selectedCategoryValues as unknown as number[];
      }
      if (filters?.selectedUserValues) {
        params.userType = filters?.selectedUserValues as unknown as number[];
      }
      if (filters && filters?.selectedDistanceValue !== 'none') {
        params.distFromCurrloc = Number(filters?.selectedDistanceValue);
      }
      const res = await postAPI.posts(params);
      const newPosts = res?.posts ?? [];
      setTotalPages(res?.pagination.pages || 1);
      setPosts(page === 1 ? newPosts : [...posts, ...res.posts]);
      page >= res.pagination?.pages ? setHasMore(false) : setHasMore(true);
      if (newPosts.length === 0) {
        console.log('No more posts to load.');
      }
    } catch (err) {
      console.error('Failed to load posts:', err);
    } finally {
      setLoading(false);
    }
  };
  const handleScroll = () => {
    const currentScrollY = document.documentElement.scrollTop;
    if (currentScrollY > scrollPositionRef.current[layout].lastScrollY) {
      setShowToolbar(false);
    } else if (currentScrollY < scrollPositionRef.current[layout].lastScrollY) {
      setShowToolbar(true);
    }
    // top button
    if (currentScrollY > 50) {
      setShowTopButton(true);
    } else {
      setShowTopButton(false);
    }
    scrollPositionRef.current[layout].scrollY = currentScrollY;
    scrollPositionRef.current[layout].lastScrollY = currentScrollY;
  };

  const restoreScrollPosition = () => {
    document.documentElement.scrollTop = scrollPositionRef.current[layout].scrollY;
    if (document.documentElement.scrollTop > 0) {
      setShowToolbar(true);
    } else {
      setShowToolbar(false);
    }
    console.log(document.documentElement.scrollTop);
  };
  useUpdateEffect(() => {
    scrollPositionRef.current.overview.scrollY = 0;
    scrollPositionRef.current.overview.lastScrollY = 0;
    scrollPositionRef.current.grid.lastScrollY = 0;
    scrollPositionRef.current.grid.scrollY = 0;
    document.documentElement.scrollTop = 0;
    setHasMore(true);
    setPage(1);
    if (page === 1) {
      fetchPosts();
    }
  }, [sortKind, filters]);
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    fetchPosts();
  }, [page, hasMore, location]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    restoreScrollPosition();
  }, [layout]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [layout]);

  const loadMore = () => {
    if (!loading && hasMore && page <= totalPages) {
      setPage((prev) => prev + 1);
    }
  };
  const onLike = async (post: PostInterface) => {
    if (post.isLike === isLikeType.like || post.isOneself === isOneselfType.self) return;
    const newPosts = posts.map((p) =>
      p.postId === post.postId ? { ...p, likeCount: p.likeCount + 1, isLike: isLikeType.like } : p,
    );
    setPosts(newPosts);
    try {
      await postAPI.likeOrReview({
        postId: post.postId,
        updKind: 1,
        likeOrReviewUser: post.userId,
      });
    } catch (error) {}
  };
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };
  const goCreatePhotoPage = () => {
    router.push(ROUTES.POST.PHOTO_SELECT);
  };
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setLayout('overview');
  }, []);
  return (
    <div>
      <NavigationBar
        showToolbar={showToolbar}
        filters={filters}
        onApplyFilters={(filter) => {
          setFilters(filter);
        }}
        onClearFilters={() => {
          setFilters(null);
        }}
      />
      {/* リスト一覧 */}
      {layout === 'overview' ? (
        <OverviewLayout
          postsList={posts}
          hasMore={hasMore}
          loading={loading}
          onLike={onLike}
          loadMore={() => loadMore()}
        />
      ) : (
        <GridLayout
          postsList={posts}
          hasMore={hasMore}
          loading={loading}
          loadMore={() => loadMore()}
        />
      )}
      <div
        className="fixed justify-items-center right-0  pb-4 px-4"
        style={{ bottom: safeArea.bottom + 64 }}
      >
        <Button
          type="button"
          onClick={scrollToTop}
          className={cn(
            'w-10 h-10 rounded-full shadow-xl flex flex-col items-center mb-4 px-0 gap-0 bg-white border border-[#4457D1] transition-opacity duration-300',
            showTopButton ? 'opacity-100' : 'opacity-0 pointer-events-none',
          )}
        >
          <img src="/images/post/icon-post-top.png" alt="" className="w-4 h-4 text-gray-800" />
          <span className="mt-[-2px] text-xs text-[#4457D1]">{APP_TEXT.POST_PAGE.TOP}</span>
        </Button>

        <Button
          type="button"
          className="w-14 h-14 rounded-full shadow-xl flex flex-col items-center gap-0"
          onClick={() => {
            goCreatePhotoPage();
          }}
        >
          <img src="/images/post/icon-post-plus.png" alt="" className="w-5 h-5 text-gray-800" />
          <span>{APP_TEXT.POST_PAGE.POST}</span>
        </Button>
      </div>
    </div>
  );
}
