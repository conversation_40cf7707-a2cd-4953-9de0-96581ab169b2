import { useRef } from 'react';

export const useHandleDbclick = () => {
  const lastTap = useRef<number | null>(null);
  const tapCount = useRef<number>(0);
  const tapTimeout = useRef<NodeJS.Timeout | null>(null);
  const clickTimeout = useRef<NodeJS.Timeout | null>(null);
  const useHandleTouchEnd = (e: React.TouchEvent, dbClick: () => void, click: () => void) => {
    const currentTime = new Date().getTime();
    const tapLength = currentTime - (lastTap.current || 0);
    if (tapLength < 300 && tapLength > 0) {
      e.preventDefault();
      // dbclick
      dbClick();
      if (tapTimeout.current) {
        clearTimeout(tapTimeout.current);
        tapTimeout.current = null;
      }
      tapCount.current = 0;
      lastTap.current = null;
    } else {
      lastTap.current = currentTime;
      if (tapTimeout.current) {
        clearTimeout(tapTimeout.current);
      }
      tapTimeout.current = setTimeout(() => {
        // single click
        click();
        tapCount.current = 0;
        lastTap.current = null;
      }, 300);
    }
  };
  const useHandleClick = (dbClick: () => void, click: () => void) => {
    if (clickTimeout.current) {
      clearTimeout(clickTimeout.current);
      clickTimeout.current = null;
      dbClick();
    } else {
      clickTimeout.current = setTimeout(() => {
        click();
        clickTimeout.current = null;
      }, 300);
    }
  };
  return { useHandleTouchEnd, useHandleClick };
};
