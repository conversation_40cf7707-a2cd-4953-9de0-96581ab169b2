'use client';

import { postAPI } from '@/api/modules/post';
import TopBar from '@/components/layout/top-bar';
import { isLikeType, isOneselfType } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { PostInterface } from '@/types/post';
import { useEffect } from 'react';
import { useState } from 'react';
import MenuPanel from '../_components/menu-panel';
import OverviewLayout from '../_components/overview-layout';

export default function userPost() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setLoading } = useLoading();
  const [currentUserPost, setCurrentUserPost] = useState<PostInterface[]>([]);
  const [nickName, setNickName] = useState<string>('');
  const queryUserId = searchParams.get('queryUserId') || '';
  const postId = searchParams.get('postId') || '';

  // いいね閲覧記録API
  const reviewapi = async (post: PostInterface) => {
    await postAPI.likeOrReview({
      postId: post.postId,
      likeOrReviewUser: post.userId,
      updKind: 2,
    });
  };
  const getCurrentUserPost = () => {
    setLoading(true);
    try {
      postAPI
        .getCurrentUserPost({
          queryUserId: Number(queryUserId),
          postId: Number(postId),
        })
        .then((res) => {
          setCurrentUserPost([res || {}]);
          if (res.isOneself !== isOneselfType.self) {
            setNickName(res.nickName + APP_TEXT.POST_PAGE.DETAIL_TITLE);
            reviewapi(res);
          } else {
            setNickName(APP_TEXT.POST_PAGE.MY_DETAIL_TITLE);
          }
        });
    } catch (error) {
      console.error('Failed to load photos:', error);
    } finally {
      setLoading(false);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    getCurrentUserPost();
  }, []);

  const handleBackClick = () => {
    router.back();
  };
  const onLike = async (post: PostInterface) => {
    if (post.isLike === isLikeType.like || post.isOneself === isOneselfType.self) return;
    const newPosts = currentUserPost?.map((p) =>
      p.postId === post.postId ? { ...p, likeCount: p.likeCount + 1, isLike: isLikeType.like } : p,
    );
    setCurrentUserPost(newPosts);
    try {
      await postAPI.likeOrReview({
        postId: post.postId,
        updKind: 1,
        likeOrReviewUser: post.userId,
      });
    } catch (error) {}
  };
  return (
    <div>
      <TopBar onBack={handleBackClick} title={`${nickName || ''}`} />
      <OverviewLayout
        postsList={currentUserPost ?? []}
        isSelf={true}
        onLike={onLike}
        loadMore={() => {}}
      >
        <MenuPanel posts={currentUserPost[0] ?? {}} />
      </OverviewLayout>
    </div>
  );
}
