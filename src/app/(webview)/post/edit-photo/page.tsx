'use client';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';

import ReactCrop, {
  centerCrop,
  makeAspectCrop,
  type Crop,
  type PixelCrop,
  convertToPixelCrop,
} from 'react-image-crop';
import { canvasPreview } from '../_components/canvasPreview';
import { useDebounceEffect } from '../_components/useDebounceEffect';

import 'react-image-crop/dist/ReactCrop.css';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { useGlobalVar } from '@/hooks/use-global-var';
import { useRouter } from 'next/navigation';
import heathconenct from '../../../../images/heathcare.png';

// This is to demonstate how to make and center a % aspect crop
// which is a bit trickier so we use some helper functions.
function centerAspectCrop(mediaWidth: number, mediaHeight: number, aspect: number) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

export default function ImageCropperPage() {
  const [imgSrc, setImgSrc] = useState('');
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const hiddenAnchorRef = useRef<HTMLAnchorElement>(null);
  const blobUrlRef = useRef('');
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [aspect, setAspect] = useState<number | undefined>(undefined);
  const router = useRouter();

  const { setFooterMenuSettingOptions } = useGlobalVar();

  useEffect(() => {
    setFooterMenuSettingOptions({ isShow: false });
    return () => {
      setFooterMenuSettingOptions({ isShow: true });
    };
  }, []);

  function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    if (aspect) {
      const { width, height } = e.currentTarget;
      const screenWidth = window.screen.width || window.innerWidth;
      const targetHeight = (height * screenWidth) / width;
      // setAspect(width / height);
      setCrop(centerAspectCrop(screenWidth, targetHeight, width / height));
      setScale(screenWidth / width);
    }
  }

  useDebounceEffect(
    async () => {
      if (
        completedCrop?.width &&
        completedCrop?.height &&
        imgRef.current &&
        previewCanvasRef.current
      ) {
        // We use canvasPreview as it's much faster than imgPreview.
        canvasPreview(imgRef.current, previewCanvasRef.current, completedCrop, scale, rotate);
      }
    },
    100,
    [completedCrop, scale, rotate],
  );
  const goNext = () => {
    //キリカッタ画像を設定する
    router.back();
  };

  return (
    <div className="flex flex-col h-screen">
      <TopBar title={APP_TEXT.POST_IMAGE_CROP_PAGE.TITLE} />
      <div className="flex-1 flex flex-col">
        <div className="bg-gray-60 flex-1">
          {/* {!!imgSrc && ( */}
          <ReactCrop
            crop={crop}
            onChange={(_, percentCrop) => setCrop(percentCrop)}
            onComplete={(c) => setCompletedCrop(c)}
            aspect={aspect}
            minHeight={100}
          >
            <img
              ref={imgRef}
              alt="Crop me"
              className="w-screen h-auto block"
              src={heathconenct.src}
              style={{ transform: `scale(${scale}) rotate(${rotate}deg)` }}
              onLoad={onImageLoad}
            />
          </ReactCrop>
          {/* )} */}
        </div>
        <div className="h-24 bg-white">
          <Button
            className="bg-primary text-white disabled:bg-primary-light  font-bold h-12 ml-[20px] mr-[20px] rounded-3xl w-[88vw] mt-1"
            type="button"
            onClick={goNext}
          >
            {APP_TEXT.POST_IMAGE_CROP_PAGE.CONFIRM}
          </Button>
        </div>
      </div>
    </div>
  );
}
