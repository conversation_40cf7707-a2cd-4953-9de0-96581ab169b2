'use client';
import { FitBitRequest, ServiceSettingRequest, dataConnectAPI } from '@/api/modules/data-connect';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { DeviceType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import type { FitBitRedirectResponse } from '@/types/data-connect';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useCallback, useEffect, useState } from 'react';
import bikuri_icon from '../../../../images/bikuri.png';
import fail_icon from '../../../../images/fail.png';
import success_icon from '../../../../images/success.png';

function FitbitFinish() {
  const router = useRouter();
  const { setDialog } = useMessageDialog();
  const { setLoading } = useLoading();
  const [success, setSuccess] = useState('');

  const goToDataConnect = useCallback(() => {
    router.push(ROUTES.DATA_CONNECT.MAIN);
  }, [router]);

  const showFitibtUserError = useCallback(
    (response: FitBitRedirectResponse) => {
      setDialog(true, {
        content: (
          <div>
            <div className="flex justify-center items-center mb-4">
              <img className="h-16 w-16" alt="desc" src={bikuri_icon.src} />
            </div>
            <div className="text-[16px]  mb-1">{APP_TEXT.FITBIT_FINISH.CHANGE_USER_DESC1}</div>
            <div className="text-[14px] text-gray-400">
              {APP_TEXT.FITBIT_FINISH.CHANGE_USER_DESC2}
            </div>
          </div>
        ),
        outSideClickClose: false,
        footer: (
          <div className="flex-col ">
            <Button
              variant="default"
              className="w-full font-bold shadow-primary! bg-primary text-white"
              onClick={() => {
                if (response.loginedUserId !== undefined) {
                  sendToServer(
                    DeviceType.Fitbit,
                    response.accountId,
                    response.loginedUserId,
                    response.scope,
                  );
                }
                setDialog(false);
              }}
            >
              {APP_TEXT.FITBIT_FINISH.CHANGE_USER_OK}
            </Button>
            <DialogClose asChild>
              <TextButton
                className="mt-2 w-full"
                variant="muted"
                onClick={() => {
                  goToDataConnect();
                }}
              >
                {APP_TEXT.FITBIT_FINISH.CHANGE_USER_CANCEL}
              </TextButton>
            </DialogClose>
          </div>
        ),
      });
    },
    [goToDataConnect, setDialog],
  );

  useEffect(() => {
    const code = new URLSearchParams(window.location.search).get('code');

    nlog(`連携1 ${JSON.stringify(code)}`);
    if (code) {
      setLoading(true, { text: '連携認証中...' });
      const request = new FitBitRequest();
      request.code = code;
      dataConnectAPI
        .fitbitRedirect(request)
        .then((response) => {
          nlog(`連携2 ${JSON.stringify(response)}`);
          if (response.loginedUserId === undefined || response.scope === undefined) {
            setSuccess('1');
            setLoading(false);
          } else {
            setLoading(false);
            //TODO:show 他のFitbitアカウントを解消されるかの確認 popup
            showFitibtUserError(response);
          }
        })
        .catch(() => {
          setSuccess('0');
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, []);

  //デバイス連携情報更新API
  const sendToServer = async (
    deviceType: number,
    accountId: string | undefined,
    changeUserId: string,
    scope: number[] | undefined,
  ) => {
    const request = new ServiceSettingRequest();
    request.deviceType = deviceType;
    request.connectFlg = 1;
    request.accountId = accountId;
    request.scope = scope;
    request.changeUserId = changeUserId;
    setLoading(true);
    dataConnectAPI
      .serviceSetting(request)
      .then((response) => {
        setSuccess('1');
        setLoading(false);
      })
      .catch((response) => {
        setSuccess('0');
        setLoading(false);
      });
  };

  return (
    <div>
      <TopBar
        title={APP_TEXT.FITBIT_FINISH.TITLE}
        enableBack={false}
        enableClose={true}
        onClose={goToDataConnect}
      />
      <div className="flex justify-center items-center min-h-[calc(100vh-160px)]">
        {success === '1' && (
          <div className="p-4 rounded">
            <img className="h-16 w-16 mx-auto mb-2" alt="desc" src={success_icon.src} />
            <div className="text-[20px] text-center font-bold mb-2">
              {APP_TEXT.FITBIT_FINISH.SUCCESS}
            </div>
            <div className="text-[15px]">{APP_TEXT.FITBIT_FINISH.SUCCESS_DESC}</div>
          </div>
        )}

        {success === '0' && (
          <div className="p-4 rounded">
            <img className="h-16 w-16 mx-auto mb-2" alt="desc" src={fail_icon.src} />
            <div className="text-[20px] text-center font-bold mb-2">
              {APP_TEXT.FITBIT_FINISH.FAIL}
            </div>
            <div className="text-[15px]">{APP_TEXT.FITBIT_FINISH.FAIL_DESC}</div>
          </div>
        )}
      </div>
      {success !== '' && (
        <div className="fixed bottom-3 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box] mb-10">
          <button
            className="bg-primary disabled:bg-primary-light text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
            type="button"
            onClick={goToDataConnect}
          >
            {APP_TEXT.FITBIT_FINISH.BTN_DATA_CONNECT}
          </button>
        </div>
      )}
    </div>
  );
}

export default FitbitFinish;
