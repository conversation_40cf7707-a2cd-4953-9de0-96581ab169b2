'use client';

import { ServiceSettingRequest, dataConnectAPI } from '@/api/modules/data-connect';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { Alert } from '@/components/ui/alert';
import { DialogClose } from '@/components/ui/dialog';
import { DataSourceDeviceType, DeviceType, VitalTypeLabels, initialData } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useEffect, useRef, useState } from 'react';
import { HealthConnectCard } from '../_components/connect-card-props';
export default function HealthConnectPage() {
  const { isShow, setDialog } = useMessageDialog();
  const { setLoading } = useLoading();

  const [vitalTypes, setVitalTypes] = useState(initialData);
  const userPermissions = useRef('');

  const router = useRouter();
  const close = () => {
    router.back();
  };
  useEffect(() => {
    getVitaType();
  }, []);
  //必要な権限を取得する
  const getVitaType = async () => {
    const request = new ServiceSettingRequest();
    request.deviceType = DataSourceDeviceType.HealthConnect;
    setLoading(true);
    dataConnectAPI
      .getVitaType(request)
      .then((response) => {
        setLoading(false);
        //戻り値に基づいてデータを組み立てる
        const typeList = response.vitalTypeList.map((id) => ({
          id,
          label: VitalTypeLabels[id],
        }));
        const idString = typeList.map((item) => item.id).join(',');
        userPermissions.current = idString;
        setVitalTypes(typeList);
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };
  const sendToServer = async (scope: number[]) => {
    //権限がないとき
    if (scope.length === 0) {
      router.replace(`${ROUTES.DATA_CONNECT.HEATHCON_FINISH}?success=false`);
      return;
    }
    const request = new ServiceSettingRequest();
    request.deviceType = DeviceType.HealthConnect; //DeviceType.HealthConnect;
    request.connectFlg = 1;
    request.scope = scope;
    setLoading(true);
    dataConnectAPI
      .serviceSetting(request)
      .then((response) => {
        setLoading(false);
        //承認が完了し、アップロード手順が完了しました
        sendMessageToNative({
          type: 'sync-step-source',
          callback: (data) => {},
        });
        router.replace(`${ROUTES.DATA_CONNECT.HEATHCON_FINISH}?success=true`);
      })
      .catch(() => {
        setLoading(false);
        router.replace(`${ROUTES.DATA_CONNECT.HEATHCON_FINISH}?success=false`);

        return Promise.reject();
      });
  };
  //アプリのインストールを促すダイアログ
  function showInstallDialog() {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-2 text-center font-normal text-black text-base">
          <p>{APP_TEXT.CONNECT.GOOGLE_PLAY_CONTEXT}</p>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col font-bold">
          <Button
            className="w-full"
            onClick={() => {
              setDialog(false);
              //呼 native
              sendMessageToNative({
                type: 'install-app',
                data: { name: 'healthConnect', applyPermissions: userPermissions.current },
                callback: (data) => {
                  if (data?.scope) {
                    const parsedScope = JSON.parse(data?.scope);
                    sendToServer(parsedScope);
                  }
                },
              });
            }}
          >
            {}
            <div className="font-semibold text-base">{APP_TEXT.CONNECT.OPEN_GOOGLE_PLAY}</div>
          </Button>
          <DialogClose asChild>
            <div className="mt-4 w-full text-gray-600 text-center font-bold  text-base">
              {COMMON_TEXT.BUTTON.CLOSE}
            </div>
          </DialogClose>
        </div>
      ),
    });
  }
  const handleSubmit = () => {
    sendMessageToNative({
      type: 'is-installed-app',
      data: {
        name: 'healthConnect',
        applyPermissions: userPermissions.current,
        permissions: false,
      },
      callback: (data) => {
        // コールバック時にインストールされていない場合は、インストールされているかどうか直接許可を申請します
        if (data?.scope) {
          const parsedScope = JSON.parse(data?.scope);
          // alert(parsedScope);

          sendToServer(parsedScope);
        } else {
          showInstallDialog();
        }
      },
    });
  };
  return (
    <>
      <TopBar
        title={APP_TEXT.CONNECT.TITLE_BAR}
        enableBack={false}
        enableClose={true}
        onClose={close}
      />
      <HealthConnectCard
        title={APP_TEXT.CONNECT.HEALTH_CONNECT}
        dataItems={vitalTypes}
        onSubmit={() => handleSubmit()}
      />
    </>
  );
}
