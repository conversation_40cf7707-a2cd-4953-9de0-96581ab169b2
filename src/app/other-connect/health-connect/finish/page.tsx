'use client';
import TopBar from '@/components/layout/top-bar';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useCallback, useEffect, useState } from 'react';
import fail_icon from '../../../../images/fail.png';
import success_icon from '../../../../images/success.png';

function FitbitFinish() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [success, setSuccess] = useState(true);

  useEffect(() => {
    const successParam = searchParams.get('success');
    if (successParam !== null) {
      setSuccess(successParam === 'true');
    }
  }, [searchParams]);

  const goToDataConnect = async () => {
    // router.push(ROUTES.DATA_CONNECT.MAIN);
    router.back();
  };

  return (
    <>
      <TopBar
        title={APP_TEXT.HEALTH_CONNECT_FINISH.TITLE}
        enableBack={false}
        enableClose={true}
        onClose={goToDataConnect}
      />
      <div className="flex justify-center items-center min-h-[calc(100vh-160px)]">
        {success && (
          <div className="p-4 rounded">
            <img className="h-16 w-16 mx-auto mb-2" alt="desc" src={success_icon.src} />
            <div className="text-[20px] text-center font-bold mb-2">
              {APP_TEXT.HEALTH_CONNECT_FINISH.SUCCESS}
            </div>
            <div className="text-[15px]">{APP_TEXT.HEALTH_CONNECT_FINISH.SUCCESS_DESC}</div>
          </div>
        )}

        {!success && (
          <div className="p-4 rounded">
            <img className="h-16 w-16 mx-auto mb-2" alt="desc" src={fail_icon.src} />
            <div className="text-[20px] text-center font-bold mb-2">
              {APP_TEXT.HEALTH_CONNECT_FINISH.FAIL}
            </div>
            <div className="text-[15px]">{APP_TEXT.HEALTH_CONNECT_FINISH.FAIL_DESC}</div>
          </div>
        )}
      </div>
      <div className="fixed bottom-3 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]  mb-10">
        <button
          className="bg-primary disabled:bg-primary-light text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
          type="button"
          onClick={goToDataConnect}
        >
          {APP_TEXT.HEALTH_CONNECT_FINISH.BTN_DATA_CONNECT}
        </button>
      </div>
    </>
  );
}

export default FitbitFinish;
