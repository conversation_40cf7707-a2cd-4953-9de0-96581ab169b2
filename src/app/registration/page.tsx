'use client';

import { registerAPI } from '@/api/modules/register';
import { Button } from '@/components/ui/button';
import { APP_TEXT } from '@/const/text/app';
import { useGlobalInit } from '@/hooks/use-global-init';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSearchParams } from '@/hooks/use-next-navigation';
import { useRouter } from '@/hooks/use-next-navigation';
import { Sex, useAuthStore } from '@/store/auth';
import { useGlobalStore } from '@/store/global';
import { useRegisterState } from '@/store/register';
import type { AccountProfileResponse, LitaTokenResponse } from '@/types/register';
import { nlog } from '@/utils/logger';
import { getMessageToNative, sendMessageToNative } from '@/utils/native-bridge';
import { useCallback, useEffect, useRef, useState } from 'react';
import LitaDialog from './_components/lita-dialog';
import Agreement from './agreement/page';
// let organizerCode: any = [100, 1001];
// let organizerCode: any = null;
// const friendUuid = '';
// const deviceId = '100100';

export default function Registration() {
  const {
    setOrganizerInfo,
    setBusinesstoken,
    organizerInfo,
    deviceId,
    setDeviceId,
    setFriendInfo,
    setAccountProfile,
    setNewLoginFlag,
    setOrganizerSetupDetail,
    setHomeDialog,
    setUseOrganizerId,
    setGroupList,
    accountProfile,
    groupList,
    setQRGroupId,
  } = useRegisterState();
  const { setDialog } = useMessageDialog();
  const [isShow, setIsShow] = useState<boolean>(false);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const { clearGlobalStore } = useGlobalStore();

  const { isGlobalInited, init } = useGlobalInit();
  // 用户是否已存在 true 存在
  const isUserAlready = useRef(false);
  const isAlreadyToken = useRef(false);
  const organizerIdCode = useRef<(string | undefined)[] | undefined>();
  const organizerCode = useRef<any>('');
  const groupCode = useRef<string | undefined>('');
  const QRGroupId = useRef<number | undefined>(undefined);
  const deviceIdRef = useRef<string>('');
  const organizerName = useRef<string | undefined>('');
  const groupListRef = useRef<any>([]);
  const uuidCode = useRef<any>();
  const organizerIdArr = useRef<number[]>();
  // const iconUrl = useRef<string | undefined>('');
  const [iconUrl, setIconUrl] = useState<string | undefined>('');

  const loginUrl = useRef<string | undefined>('');
  const registerUrl = useRef<string | undefined>('');
  const authenticationMethod = useRef<number | undefined>();
  const { setUser, setToken, setAppData, user, appData } = useAuthStore();
  const { setLoading, isLoading } = useLoading();
  // 観光客モード
  const [isTourist, setIsTourist] = useState<boolean>(true);
  const router = useRouter();
  const searchParams = useSearchParams().get('organizerCode');
  const friendCodeParams = useSearchParams().get('friendCode');
  const groupCodeParams = useSearchParams().get('groupCode');
  const isDesktop = useSearchParams().get('isDesktop') === 'true';
  // フレンドユーザーID
  const friendUserId = useRef<string>('');

  //「団体認証情報取得 API」を呼び出し、団体認証情報を取得する
  const getOrganizerInfo = (deviceId: string) => {
    nlog(`zzz02 ${organizerCode.current}`);
    registerAPI
      .organizerInfo({ organizerCode: organizerCode.current ? [organizerCode.current] : ['DHQ1'] })
      .then((res) => {
        nlog(`zzz03 ${JSON.stringify(res)}`);
        if (res) {
          const organizerList: (string | undefined)[] | undefined = [];
          const ids: number[] = [];
          let groupId: number | undefined = undefined;
          setIconUrl(res?.organizerList ? res?.organizerList[0]?.organizerSetting?.icon : '');
          res.organizerList?.map((item) => {
            organizerList.push(item.organizerCode);
            if (item.organizerId) {
              ids.push(item.organizerId);
            }
            if (organizerCode.current === item.organizerCode && groupCode.current) {
              organizerName.current = item.organizerName;
              groupId = item.groupList?.find(
                (group) => group.groupCd === groupCode.current,
              )?.groupId;
            }
          });
          setQRGroupId(groupId);
          QRGroupId.current = groupId;
          if (ids) {
            setUseOrganizerId(String(ids[0]));
            organizerIdArr.current = ids;
          }
          setOrganizerInfo(res);
          organizerIdCode.current = organizerList;
          if (res?.organizerList) {
            setGroupList(res.organizerList[0].groupList ? res.organizerList[0].groupList : []);
            groupListRef.current = res.organizerList[0].groupList
              ? res.organizerList[0].groupList
              : [];
            loginUrl.current = res?.organizerList[0].loginUrl;
            registerUrl.current = res?.organizerList[0].registerUrl;
            organizerSetupDetail(
              res?.organizerList[0].organizerId ? res?.organizerList[0].organizerId : 0,
            );
          }
          getAccountProfile(deviceId);
        }
      })
      .catch((error) => {});
  };
  const hasExecuted = useRef(false);
  useEffect(() => {
    nlog(`zzz04 searchParams ${searchParams}`);
    if (!hasExecuted.current) {
      // setLoading(true, { text: 'データを通信中...' });
      hasExecuted.current = true;
      //団体コード取得
      console.log(searchParams);
      if (searchParams) {
        organizerCode.current = searchParams;
        if (groupCodeParams) {
          groupCode.current = groupCodeParams;
        }
      } else if (friendCodeParams) {
        uuidCode.current = friendCodeParams;
      } else {
        nlog('zzz13 addUsersOrganizer 1');
        sendMessageToNative({
          type: 'get-activity-info',
          data: {},
          callback: (data) => {
            nlog(`zzz04 11 ${JSON.stringify(data)}`);
            if (data?.fcode) {
              uuidCode.current = data?.fcode;
            }
            if (data?.gcode) {
              groupCode.current = data?.gcode;
            }
            if (data?.code) {
              organizerCode.current = data?.code;
            }
          },
        });
      }
      // sendMessageToNative({
      //   type: 'friend-uuid',
      //   data: {},
      //   callback: (data) => {
      //     if (data?.code) {
      //       uuidCode.current = data?.code;
      //     }
      //   },
      // });
      // デバイスIDの取得
      sendMessageToNative({
        type: 'uuid',
        data: {},
        callback: (data) => {
          if (data?.uuid) {
            setDeviceId(data?.uuid);
            deviceIdRef.current = data?.uuid;
            // 一時トークン取得API
            registerAPI.tempToken({ deviceId: data?.uuid }).then(async (res) => {
              setToken(res.tempToken ? res.tempToken : '');
              // フレンド招待の場合
              if (uuidCode.current !== undefined) {
                await getFriendInfo(data?.uuid);
              } else {
                // 団体QRコードスキャンの場合
                await getOrganizerInfo(data?.uuid);
              }
            });
          }
        },
      });
      if (isDesktop) {
        // organizerCode.current = 'IGQB';
        organizerCode.current = 'nttc';
        // groupCode.current = 'a1b2r4d5';
        getOrganizerInfo('705a4e6a1f69c999');
        // getFriendInfo('705a4e6a1f69c999');
      }
    }
  }, [searchParams, friendCodeParams, groupCodeParams]);

  // フレンド情報取得
  const getFriendInfo = (uuid: string) => {
    registerAPI.friendInfo({ friendUuid: uuidCode.current ? uuidCode.current : '' }).then((res) => {
      if (res) {
        setFriendInfo(res);
        friendUserId.current = res.friendUserId ? String(res.friendUserId) : '';
        // setOrganizerInfo(res);
        // setGroupList(res.groupList ? res.groupList : []);
        // groupListRef.current = res.groupList ? res.groupList : [];
        // setUseOrganizerId(res?.organizerId ? String(res?.organizerId) : '');
        organizerName.current = res.organizerName;
        loginUrl.current = res.loginUrl;
        registerUrl.current = res.registerUrl;
        organizerCode.current = res.organizerCode;
        // getAccountProfile(uuid);
        getOrganizerInfo(uuid);
      }
    });
  };

  // 団体セットアップ情報取得
  const organizerSetupDetail = (id: number) => {
    registerAPI.organizerSetup({ organizerId: id, fcnId: 11 }).then((res) => {
      if (res) {
        setOrganizerSetupDetail(res);
      }
    });
  };
  // アカウント情報取得API
  const getAccountProfile = (id: string) => {
    registerAPI
      .accountProfile({ deviceId: id })
      .then((res) => {
        setLoading(false);
        // !res.externalUserId  新規モード false login true
        // isUserAlready.current = !!res.externalUserId;
        isUserAlready.current = !!res.externalUserId;
        setAccountProfile(res);
        setIsShow(true);
        getUserToken(res);
        setNewLoginFlag(res.organizers ? res.organizers?.length === 0 : true);
        authenticationMethod.current = res.loginMethod;
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };
  //  はじめてご利用の方
  const login = () => {
    setNewLoginFlag(true);
    router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken.current}`);
    // router.push('/registration/agreement');
  };

  const addFriend = () => {
    registerAPI
      .addFriend({ uuid: uuidCode.current })
      .then((res) => {})
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };

  // キャッシュされたユーザー情報の取得 本机的用户登入信息
  const getUserToken = (accountProfileRes: AccountProfileResponse) => {
    if (isDesktop) {
      setBusinesstoken('*************');
      router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken.current}`);
      return;
    }
    const organizerCodes: string[] = [];
    const groupIds: number[] = [];
    const organizerGroupUser: { organizerCode?: string; groupId?: number }[] = [];
    if (Object.keys(accountProfileRes).length > 0) {
      accountProfileRes.organizers?.map((item) => {
        organizerGroupUser.push({
          organizerCode: item.organizerCode,
          groupId: item.groupId,
        });
        if (item?.organizerCode) {
          organizerCodes.push(item.organizerCode);
        }
        if (item?.groupId) {
          groupIds.push(item.groupId);
        }
      });
    }
    sendMessageToNative({
      type: 'get-user-info',
      data: {},
      callback: (data) => {
        nlog(`getUserToken ${JSON.stringify(data)}`);

        setAppData({
          appVersion: data?.appVersion,
          userAgent: data?.userAgent,
        });
        if (data?.token) {
          setIsTourist(true);
          setNewLoginFlag(false);
          isAlreadyToken.current = true;
          setBusinesstoken(data.token);
          if (uuidCode.current) {
            addFriend();
          }
          nlog(`getUserToken2 ${isAlreadyToken.current}`);
          if (data.organizerID) {
            const organizerUseIds = data.organizerID.split(',');
            if (
              organizerIdArr.current &&
              organizerUseIds.includes(String(organizerIdArr.current[0]))
            ) {
              setAppData({
                appVersion: data?.appVersion,
                userAgent: data?.userAgent,
              });

              const userAgent = navigator.userAgent;
              setUser({
                ...(user || { lat: 0, lng: 0 }),
                id: data?.userID,
                name: data?.nickName,
                email: data?.email,
                userAgent: userAgent,
                birthday: data?.birthDt,
                sex: Sex[String(data?.sex) as keyof typeof Sex],
                organizerID: data?.organizerID,
                useOrganizerID: data?.userOrganizerID,
              });
              setToken(data.token);
              // 業務トークンの団体コードとQRコードの団体コード一致の場合
              // スキャン情報に所属団体情報場合
              if (groupCode.current) {
                groupCdCheck(groupIds, organizerGroupUser);
              } else {
                // 業務トークンの団体コードとQRコードの団体コード一致の場合
                setHomeDialog(true);
                // router.push('/home');
                init();
              }
            } else {
              nlog(`getUserToken1 ${isAlreadyToken.current}`);
              // clearGlobalStore();
              // （２）招待先のフレンドは招待元の主催団体に所属していない、かつある主催団体に加入している場合
              friendCodeFlag();
              // router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken.current}`);
            }
          }
        } else {
          // 新規ユーザー アカウント情報取得空
          if (Object.keys(accountProfileRes).length === 0) {
            nlog('zzz13 notoken 0');
            setIsTourist(true);
            isAlreadyToken.current = false;
            setNewLoginFlag(true);
            // ツーリストモードジャンプ利用規約
            router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken.current}`);
          } else {
            nlog('zzz13 notoken 1');
            setBusinesstoken(
              accountProfileRes.externalUserId ? accountProfileRes.externalUserId : '',
            );
            // ※フレンド追加の場合、「フレンド追加API」を呼び出す
            if (uuidCode.current) {
              addFriend();
              if (organizerCodes.includes(organizerCode.current)) {
                loginFunc();
              } else {
                loginFunc(true);
              }
            } else {
              // スキャン主催団体codeの場合
              if (organizerCodes && organizerCodes.length > 0 && !organizerCode.current) {
                nlog('zzz13 loginFunc 1');
                loginFunc();
              } else if (organizerCodes.includes(organizerCode.current)) {
                // スキャン情報に所属団体情報場合
                if (groupCode.current) {
                  groupCdCheck(groupIds, organizerGroupUser);
                } else {
                  // 取得したアカウント情報において、ユーザーが加入している主催団体情報にスキャンで取得した主催団体情報が含まれる場合、ログインAPIを呼び出して認証を行う。
                  nlog('zzz13 loginFunc 2');
                  loginFunc();
                }
              } else {
                nlog('zzz13 loginFunc 3');
                loginFunc(true);
              }
            }
            // ログイン済み
          }
        }
      },
    });
  };
  // スキャン情報に所属団体情報
  const groupCdCheck = (
    groupIds: number[],
    organizerGroupUser: { organizerCode?: string; groupId?: number }[],
  ) => {
    // if (groupIds.includes(Number(QRGroupId.current))) {
    // 当該主催団体の所属団体情報がスキャンで取得した所属団体情報と一致する場合、ログインAPIを呼び出して認証を行う
    const findGroup: { organizerCode?: string; groupId?: number } | undefined =
      organizerGroupUser.find(
        (item) =>
          item?.organizerCode === organizerCode.current && item?.groupId === QRGroupId.current,
      );
    if (findGroup) {
      nlog('zzz13 notoken 2');
      // ログインAPI
      loginFunc();
    } else {
      // 当該主催団体の所属団体情報がスキャンで取得した所属団体情報と一致しない場合
      setDialog(true, {
        content: (
          <div className="text-base font-normal">
            <div className="max-h-64 break-all">
              {`すでに${organizerName.current}団体に参加済みのため、同一主催団体内の他の所属団体に参加することはできません。`}
            </div>
          </div>
        ),
        footer: (
          <>
            <Button type="button" className="rounded-full" onClick={() => loginFunc()}>
              閉じる
            </Button>
          </>
        ),
      });
    }
    // }
  };

  // ログインAPI
  const loginFunc = (addOrg?: boolean) => {
    setDialog(false);
    setNewLoginFlag(false);
    // ログインAPI
    registerAPI
      .authLogin({
        deviceId: deviceIdRef.current,
        // organizerId: organizerIdArr.current ? Number(organizerIdArr.current[0]) : 0,
        // groupId: groupListRef.current[0].groupId,
        loginMethod: String(authenticationMethod.current),
      })
      .then((res) => {
        if (res) {
          setToken(res.accessToken ? res.accessToken : '');
          setBusinesstoken(res.accessToken ? res.accessToken : '');
          const ids: number[] = [];
          const idsCode: string[] = [];
          const useOrganizers: number[] = [];
          res.organizers?.map((item) => {
            if (item.organizerId && item.organizerCode) {
              ids.push(item.organizerId);
              idsCode.push(item.organizerCode);
            }
            if (item.isDefault === 1 && item.organizerId) {
              useOrganizers.push(item.organizerId);
            }
          });
          // res 转 str
          const resStr = JSON.stringify(res);
          nlog(`res userId:${resStr}`);
          const userAgent = navigator.userAgent;
          setUser({
            ...(user || { lat: 0, lng: 0 }),
            id: res.userId ? String(res.userId) : '',
            name: '',
            email: '',
            userAgent: userAgent,
            birthday: '',
            sex: Sex.UNKNOWN,
            organizerID: ids ? String(ids.join(',')) : '',
            organizerCode: idsCode ? String(idsCode.join(',')) : '',
            useOrganizerID: useOrganizers ? String(useOrganizers[0]) : '',
          });
          sendMessageToNative({
            type: 'user-info',
            data: {
              userID: String(res?.userId ?? 0),
              token: res?.accessToken ?? '',
              organizerCode: idsCode ? String(idsCode.join(',')) : '',
              organizerID: ids ? String(ids.join(',')) : '',
              userOrganizerID: useOrganizers ? String(useOrganizers[0]) : '',
            },
          });
          nlog(`zzz13 addorg ${addOrg}`);
          if (addOrg) {
            // （２）招待先のフレンドは招待元の主催団体に所属していない、かつある主催団体に加入している場合
            friendCodeFlag();
          } else {
            setHomeDialog(true);
            // router.push('/home');
            init();
          }
        }
      })
      .catch((err) => {
        return Promise.reject();
      });
  };

  const friendCodeFlag = () => {
    // （２）招待先のフレンドは招待元の主催団体に所属していない、かつある主催団体に加入している場合
    if (uuidCode.current) {
      setDialog(true, {
        content: (
          <div className="text-base font-normal">
            <div className="max-h-64 break-all">
              {`${organizerName.current}の情報を受け取りますか？`}
            </div>
          </div>
        ),
        footer: (
          <>
            <Button
              type="button"
              className="rounded-full"
              onClick={() => {
                setDialog(false);
                router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken.current}`);
              }}
            >
              確認
            </Button>
            <Button
              variant="outline"
              type="button"
              className="rounded-full"
              onClick={() => {
                setDialog(false);
                setHomeDialog(true);
                // router.push('/home');
                init();
              }}
            >
              キャンセル
            </Button>
          </>
        ),
      });
    } else {
      router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken.current}`);
    }
  };

  // アカウントをお持ちの方
  const haveAnAccount = () => {
    setNewLoginFlag(false);
    setOpenDialog(true);
  };

  return (
    <div>
      {!isLoading && (
        <div>
          {!isTourist && (
            <div className="flex flex-col items-center justify-center w-full h-screen">
              {iconUrl === '' ? (
                <img className="w-40" src="/images/friend/empty.png" alt="" />
              ) : (
                <img className="w-40" src={iconUrl} alt="" />
              )}
              {isShow && (
                <div className="flex flex-col mt-16">
                  {/* このデバイスIDがすでにLita IDに登録されている場合は、「登録」ボタンを表示しません。true */}
                  {/* {!isUserAlready.current && ( */}
                  <Button variant="default" className="rounded-full mb-4" onClick={login}>
                    はじめてご利用の方
                  </Button>
                  {/* )} */}
                  <Button
                    variant="outline"
                    className="rounded-full bg-transparent"
                    onClick={haveAnAccount}
                  >
                    アカウントをお持ちの方
                  </Button>
                </div>
              )}
              <div className="w-full text-right px-6 fixed bottom-10 right-0">
                {appData?.appVersion}
              </div>
              {openDialog && (
                <LitaDialog
                  trigger={openDialog}
                  isAlreadyToken={isAlreadyToken.current}
                  popupContent={
                    'このアカウントは他のデバイスでログイン実績があります。ログインを続けてもよろしいでしょうか。'
                  }
                />
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
