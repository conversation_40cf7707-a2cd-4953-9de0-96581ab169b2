'use client';
import TopBar from '@/components/layout/top-bar';
import { TextButton } from '@/components/shared/text-button';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { APP_TEXT } from '@/const/text/app';
import { useMessageDialog } from '@/hooks/use-message-dialog';

import { registerAPI } from '@/api/modules/register';
import { COMMON_TEXT } from '@/const/text/common';
import { useHealthSetting } from '@/hooks/use-health-setting';

import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { Sex, useAuthStore } from '@/store/auth';
import { useRegisterState } from '@/store/register';
import type {
  AuthLoginRequest,
  LableType,
  LooseObject,
  LooseObjectItem,
  OrganizerType,
} from '@/types/register';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useEffect, useRef, useState } from 'react';
import { number } from 'zod';
import { CardInfoPage, CardInfoPage2, type UserInfoItemType } from '../../_components/card-info';
import LitaDialog from '../../_components/lita-dialog';
import { generate, getBaseSetting } from '../../_utils/data-convert';
import { AUTH_MODE_TYPE, LOGIN_FLG_TYPE, LOGIN_METHOD_TYPE, YES_NO_TYPE } from '../../_utils/enums';

export default function PointHintPage({
  dataConfirm,
  editFromClick,
}: {
  dataConfirm?: LooseObject[];
  editFromClick: (item: unknown) => void;
}) {
  const { sendHealthSetting, allPermissionArrary } = useHealthSetting();
  const { setDialog } = useMessageDialog();
  const [isOpen, setIsOpen] = useState(true);
  const [useListInfo, setUseListInfo] = useState<LooseObject[]>([]);
  const {
    newLoginFlag,
    businesstoken,
    organizerInfo,
    accountProfile,
    litaUserInfo,
    organizerSetupDetail,
    groupAdditionFlag,
    deviceId,
    useOrganizerId,
    litaOrganizerIds,
    setHomeDialog,
    setLitaUserInfo,
  } = useRegisterState();
  const [authMode, setAuthMode] = useState<LableType>();
  const { setUser, setToken, user } = useAuthStore();
  const [personalInfoCollection, setPersonalInfoCollection] = useState<boolean>();
  const [organizerList, setOrganizerList] = useState<OrganizerType[]>([]);
  const [trigger, setTrigger] = useState<boolean>(false);
  const router = useRouter();
  useEffect(() => {
    if (organizerSetupDetail) {
      const base = getBaseSetting('baseSetting', 'authMode');
      const personal = getBaseSetting('baseSetting', 'personalInfoCollection');
      if (base) {
        setAuthMode(base);
      }
      if (personal) {
        setPersonalInfoCollection(personal.value === YES_NO_TYPE.YES);
      }
    }
  }, [organizerSetupDetail]);
  // const [generateCode, setGenerateCode] = useState<string>();
  const generateCode = useRef<string>();
  const effectRan = useRef(false);
  const loginMethod = useRef(false);
  const organizerIds = useRef('');

  useEffect(() => {
    if (litaOrganizerIds) {
      organizerIds.current = litaOrganizerIds;
    }
    if (organizerInfo?.organizerList) {
      setOrganizerList(organizerInfo.organizerList);
    }
    if (!effectRan.current) {
      effectRan.current = true;
      // if (accountProfile?.externalUserId && organizerInfo?.organizerList) {
      //   const code = generate();
      //   generateCode.current = code;
      //   sendMessageToNative({
      //     type: 'start-other-link',
      //     data: {
      //       link: `${organizerInfo.organizerList[0].loginUrl}&state=${code}`,
      //       callbackLink: 'https://stg3-portal.kenko-mileage.com',
      //       title: 'registration',
      //     },
      //     callback: (data) => {
      //       getLitaToken(data?.url);
      //     },
      //   });
      // }
    }
  }, [organizerInfo, accountProfile, litaOrganizerIds]);

  useEffect(() => {
    setUseListInfo(dataConfirm ? dataConfirm : []);
  }, [dataConfirm]);

  const getLitaToken = (urlString: string) => {
    const url = new URL(urlString);
    const searchParams = url.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    if (state !== generateCode.current) return;
    if (code) {
      registerAPI.litaToken({ code: code }).then(async (res) => {
        if (res) {
          setLitaUserInfo(res);
        }
      });
    }
  };

  const transformData = (data: LooseObject[]) => {
    return data.reduce<Record<string, string | number>>((acc, item) => {
      item.info?.map((infoItem) => {
        if (infoItem.key !== undefined && typeof infoItem.key !== 'symbol') {
          acc[infoItem.key] = infoItem.value !== undefined ? infoItem.value : ''; // 处理可能的 undefined value
        }
      });
      return acc;
    }, {});
  };

  const editClick = (item: LooseObject) => {
    const infoArr: LooseObject[] = [];
    if (useListInfo && useListInfo.length > 0) {
      useListInfo.map((its: LooseObject) => {
        if (item.step === 3 && its.step === 3) {
          infoArr.push(its);
        }
      });
    }
    if (infoArr.length === 0) {
      infoArr.push(item);
    }
    editFromClick({
      step: item.step,
      info: transformData(infoArr),
    });
  };

  // sections: LooseObject[]
  const transformDataSub = () => {
    if (dataConfirm) {
      const flatObject = dataConfirm.reduce<Record<string, string>>((acc, section) => {
        if (section.info) {
          for (const item of section.info) {
            if (item.key !== undefined && typeof item.key === 'string') {
              if (item.key === 'groupId') {
                (acc as Record<string, string>)[item.key] = item.index || '';
              } else {
                (acc as Record<string, string>)[item.key] = item.value || '';
              }
            }
          }
        }
        return acc;
      }, {});
      return flatObject;
    }
  };

  // 新規ユーザー作成API
  const registerWithAuth = () => {
    const subData = transformDataSub();
    const id = subData?.groupId;

    const resData = {
      ...subData,
      deviceId: deviceId,
      // organizerId: organizerInfo.organizerList[0].organizerId,
      loginMethod: authMode?.value,
      litaId: litaUserInfo.litaId,
      litaAccessToken: litaUserInfo.litaAccessToken,
      litaRefreshToken: litaUserInfo.litaRefreshToken,
      groupId: Number(subData?.groupId),
      organizerId: organizerInfo.organizerList
        ? Number(organizerInfo.organizerList[0].organizerId)
        : 0,
      sex: subData?.sex ? Number(subData.sex) : 0,
      height: Number.parseFloat(String(subData?.height)),
      weight: Number.parseFloat(String(subData?.weight)),
      pace: Number.parseFloat(String(subData?.pace)),
      level: Number(subData?.level),
    };
    if (!subData) return;
    registerAPI
      .registerWithAuth({
        ...resData,
      })
      .then((res) => {
        if (res) {
          setToken(res.accessToken ? res.accessToken : '');
          sendMessageToNative({
            type: 'user-info',
            data: {
              userName: litaUserInfo.nickName ? litaUserInfo.nickName : '',
              userID: String(res?.userId ?? 0),
              token: res?.accessToken ?? '',
              sex: Sex[String(litaUserInfo.sex) as keyof typeof Sex],
              organizerID: organizerIds.current,
              //todo： organizerCode
              userOrganizerID: useOrganizerId,
            },
            callback: (data) => {},
          });
          // sendMessageToNative({
          //   type: 'user-info',
          //   data: {
          //     userID: res?.userId ?? 0,
          //     token: res?.accessToken ?? '',

          //   },
          //   callback: (data) => {},
          // });
          // 団体の認証方式＝ 3.Smart-LiTA認証_SMS認証ありの場合
          if (authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS) {
            // A008_認証コード送信画面
          }
          // .団体の認証方式＝2.Smart-LiTA認証_SMS認証なしの場合
          // . 団体の認証方式＝  1:JPKI認証以外の場合
          if (
            authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS_NO ||
            authMode?.value === AUTH_MODE_TYPE.LOGIN_JPKI
          ) {
            // 「A009_ホーム画面」
            setHomeDialog(true);
            router.push('/home');
          }
        }
      });
  };
  //  ユーザー所属団体追加API
  const userOrganizers = (groupId: string) => {
    if (organizerInfo?.organizerList) {
      const id = Number(organizerInfo?.organizerList[0].organizerId);
      registerAPI.userOrganizers({ organizerId: id, groupId: Number(groupId) }).then((res) => {
        if (res) {
          // ログインモード
          if (!newLoginFlag) {
            // 「A003_アクセス許可画面」に遷移する。
            sendMessageToNative({
              type: 'show-location-permission',
              data: {},
              callback: (data) => {
                // alert(`「A003_アクセス許可画面」に遷移する。${JSON.stringify(data)}`);
                const hasHealtch = data?.health === '1';
                const titleStr = hasHealtch
                  ? APP_TEXT.REGISTRATION_CREATE.CONNECT_FINISH_TITLE
                  : APP_TEXT.REGISTRATION_CREATE.CONNECT_DISABLE_TITLE;

                const contextStr = hasHealtch
                  ? ''
                  : APP_TEXT.REGISTRATION_CREATE.CONNECT_DISABLE_CONTEXT;

                setDialog(true, {
                  title: titleStr,
                  content: (
                    <div className="text-base font-normal">
                      {/* <img src="/images/create/finish.svg" alt="complete" className="mb-2" />
                       */}
                      {contextStr}
                    </div>
                  ),
                  outSideClickClose: true,
                  footer: (
                    <Button
                      className="w-full rounded-full h-12"
                      variant="default"
                      onClick={() => {
                        sendHealthSetting(allPermissionArrary); // 传入具体设备类型
                        setHomeDialog(true);
                        router.push('/home');
                      }}
                    >
                      <div className="font-semibold text-base font-bold">
                        {COMMON_TEXT.BUTTON.CLOSE}
                      </div>
                    </Button>
                  ),
                });
              },
            });
          }
        }
      });
    }
  };

  const userLogin = (params: Record<string, string> | AuthLoginRequest | undefined) => {
    if (organizerInfo?.organizerList && params) {
      registerAPI
        .authLogin({
          ...params,
          deviceId,
          loginMethod: loginMethod.current ? '0' : authMode?.value,
          litaId: litaUserInfo.litaId,
          litaAccessToken: litaUserInfo.litaAccessToken,
          litaRefreshToken: litaUserInfo.litaRefreshToken,
          groupId: Number(params?.groupId),
          organizerId: Number(organizerInfo.organizerList[0].organizerId),
          sex: params.sex ? Number(params.sex) : 0,
          height: Number.parseFloat(String(params.height)),
          weight: Number.parseFloat(String(params?.weight)),
          pace: Number.parseFloat(String(params?.pace)),
          level: Number(params?.level),
        })
        .then((res) => {
          setToken(res.accessToken ? res.accessToken : '');
          setUser({
            ...(user || { lat: 0, lng: 0 }),
            id: res.userId ? String(res.userId) : '',
            name: litaUserInfo.nickName ? litaUserInfo.nickName : '',
            email: litaUserInfo.email ? litaUserInfo.email : '',
            birthday: litaUserInfo.birthDt ? litaUserInfo.birthDt : '',
            sex: Sex[String(litaUserInfo.sex) as keyof typeof Sex],
            organizerID: litaUserInfo.organizers
              ? String(litaUserInfo.organizers[0].organizerId)
              : '',
            useOrganizerID: litaUserInfo.organizers
              ? String(litaUserInfo.organizers[0].organizerId)
              : '',
          });
          if (res) {
            setToken(res.accessToken ? res.accessToken : '');
            sendMessageToNative({
              type: 'user-info',
              data: {
                userName: litaUserInfo.nickName ? litaUserInfo.nickName : '',
                userID: String(res?.userId ?? 0),
                token: res?.accessToken ?? '',
                sex: Sex[String(litaUserInfo.sex) as keyof typeof Sex],
                organizerID: organizerIds.current,
                userOrganizerID: useOrganizerId,
              },
              callback: (data) => {},
            });
            // 新規モード 業務トークンあり
            if (newLoginFlag && businesstoken) {
              if (authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS) {
                // LOGIN_SMS = '3', // 3：Smart-LiTA認証_SMS認証あり
                // alert(' 「A008_認証コード送信画面」に遷移する。');
                setHomeDialog(true);
                router.push('/home');
                // TODO:  「A008_認証コード送信画面」に遷移する。
              } else if (authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS_NO) {
                // LOGIN_SMS_NO = '2', // 2：Smart-LiTA認証_SMS認証なし
                // alert('「本人確認」機能から個人情報登録の場合、「本人確認」機能画面に遷移する。');
                setHomeDialog(true);
                router.push('/home');
              } else {
                // JPKI認証の場合、
                // 、アクセス許可後業務トークンで「A009_ホーム画面」に遷移する。
                setHomeDialog(true);
                router.push('/home');
              }
            }
          }
        });
    }
  };
  const confirmClick = () => {
    const resData = transformDataSub();
    // 新規モード
    if (accountProfile.externalUserId === '') {
      // 業務トークンなし
      if (!businesstoken) {
        // 新規ユーザー作成 API ユーザーを新規作成する。
        registerWithAuth();
      } else {
        // 業務トークンあり
        // 団体追加フラグ＝１の場合 団体追加 API」を呼び出
        // alert('新規モード 業務トークンあり');
        if (groupAdditionFlag && resData) {
          userOrganizers(resData.groupId);
        }

        userLogin(resData);
      }
    } else {
      // ログインモード
      if (businesstoken) {
        loginMethod.current = true;
      }

      // 業務トークンあり /業務トークンなしlitaUserInfo
      userLogin(resData);
      // 「団体追加 API」を呼び出し、新団体をアカウントに関連付けます。
      if (resData) {
        userOrganizers(resData.groupId);
      }
    }
  };
  // 利用規約
  const linkClick = (name: string | undefined) => {
    setDialog(true, {
      title: `${name}の利用規約`,
      content: (
        <div className="text-base font-normal">
          <div style={{ width: '100%', height: '70vh' }}>
            <iframe
              src="https://mileage-dev-fde-cnbbbdgqedceb0gz.a01.azurefd.net/blob/uploadfile/individual_file_3_1752482486182.html"
              title="利用規約"
              width="100%"
              height="100%"
            />
          </div>
        </div>
      ),
    });
  };

  return (
    <div className="bg-background">
      {/* <TopBar title={APP_TEXT.REGISTRATION_CREATE.TITLE} /> */}
      <div className="pt-4 px-6">
        {useListInfo.map((item, index) => (
          <CardInfoPage key={index} useInfo={item} editClick={editClick} />
        ))}

        {/* 利用規約 */}
        <div className="text-base font-normal ">
          {APP_TEXT.REGISTRATION_CREATE.CONFIRM_AGREEMENT}
        </div>
        <div className="py-4">
          {organizerList?.map((item, index) => (
            <div className="text-base font-normal mb-1" key={index}>
              <TextButton onClick={() => linkClick(item.organizerName)}>
                {item.organizerName}の利用規約
              </TextButton>
            </div>
          ))}
        </div>
        <Button className="w-full rounded-3xl h-12 mb-2" onClick={confirmClick}>
          登録する
        </Button>
      </div>
    </div>
  );
}
