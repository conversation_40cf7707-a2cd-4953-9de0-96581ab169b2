import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/shared/drawer';
import { COMMON_TEXT } from '@/const/text/common';
import { cn } from '@/lib/utils';
import { Disabled } from '@/story/components/checkbox.stories';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { Button } from './button';

type WheelDatePickerProps = {
  defaultValue?: string;
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  showCalendar?: boolean;
  yearRange?: [number, number];
  className?: string;
  style?: React.CSSProperties;
  formatDate?: string;
};

const WheelDatePicker = forwardRef<HTMLDivElement, WheelDatePickerProps>(
  (
    {
      value,
      defaultValue,
      yearRange = [1945, new Date().getFullYear()],
      showCalendar,
      style,
      className,
      onChange,
      formatDate = 'yyyy/MM/dd',
    },
    ref,
  ) => {
    const [showWheelPicker, setShowWheelPicker] = useState(false);
    const [disabled, setDisabled] = useState(!!defaultValue);

    const [selectedYear, setSelectedYear] = useState<number>();
    const [selectedMonth, setSelectedMonth] = useState<number>();
    const [selectedDay, setSelectedDay] = useState<number>();

    const yearRef = useRef<HTMLDivElement>(null);
    const monthRef = useRef<HTMLDivElement>(null);
    const dayRef = useRef<HTMLDivElement>(null);

    // ぼうしゅうタイマ
    const scrollTimer = useRef<NodeJS.Timeout>();
    const isInitialMount = useRef(true);
    const [selectedDateValue, setSelectedDateValue] = useState<string>(
      defaultValue ? defaultValue : '',
    );
    const getDefaultDate = () => {
      console.log('value', value);
      console.log('defaultValue', defaultValue);
      //   if (value) return value;
      if (defaultValue) {
        return new Date(defaultValue);
      }
      //   if (initialDate) return initialDate;
      return new Date('1970-01-01');
    };
    const [selectedDate, setSelectedDate] = useState<Date>(getDefaultDate());

    // スクロール位置の初期化
    useEffect(() => {
      if (showWheelPicker) {
        scrollToSelected();
        if (selectedDate) {
          setSelectedYear(selectedDate.getFullYear());
          setSelectedMonth(selectedDate.getMonth());
          setSelectedDay(selectedDate.getDate());
        }
      }
    }, [showWheelPicker, selectedDate]);

    // 選択したアイテムが垂直方向に中央になるようにスクロール位置を計算して設定する
    const scrollToSelected = () => {
      setTimeout(() => {
        const itemHeight = 48;
        const paddingItems = 2;
        const offset = paddingItems * itemHeight;

        // 滚动年份
        if (yearRef.current) {
          const yearIndex = years.findIndex((y) => y === selectedDate.getFullYear());
          if (yearIndex >= 0) {
            yearRef.current.scrollTop = yearIndex * itemHeight;
          }
        }

        if (monthRef.current) {
          monthRef.current.scrollTop = selectedDate.getMonth() * itemHeight;
        }

        if (dayRef.current) {
          dayRef.current.scrollTop = (selectedDate.getDate() - 1) * itemHeight;
        }
      }, 100);
    };

    // 現在の中央にあるアイテムの取得
    const getCenteredItem = (
      ref: React.RefObject<HTMLDivElement>,
      items: any[],
      type: 'year' | 'month' | 'day',
    ) => {
      if (!ref.current) return;

      const itemHeight = 48;
      const scrollPosition = ref.current.scrollTop;
      const selectedIndex = Math.round(scrollPosition / itemHeight);

      const safeIndex = Math.min(Math.max(0, selectedIndex), items.length - 1);

      if (type === 'year') {
        return years[safeIndex];
      }
      if (type === 'month') {
        return months[safeIndex].value;
      }
      return days[safeIndex];
    };

    // 処理のスクロール終了
    const handleScrollEnd = (
      ref: React.RefObject<HTMLDivElement>,
      items: any[],
      type: 'year' | 'month' | 'day',
    ) => {
      if (scrollTimer.current) {
        clearTimeout(scrollTimer.current);
      }
      scrollTimer.current = setTimeout(() => {
        const centeredValue = getCenteredItem(ref, items, type);
        if (centeredValue !== undefined) {
          handleDateChange(type, centeredValue);
        }
      }, 200);
    };
    // 生成年範囲
    const years = Array.from(
      { length: yearRange[1] - yearRange[0] + 1 },
      (_, i) => yearRange[0] + i,
    );

    // 月データ
    const months = Array.from({ length: 12 }, (_, i) => ({
      value: i,
      name: new Date(0, i).toLocaleString('default', { month: 'short' }),
    }));

    // 取得当月日数
    const getDaysInMonth = (year: number, month: number) => new Date(year, month + 1, 0).getDate();

    // 日付配列の生成
    const days = Array.from(
      { length: getDaysInMonth(selectedDate.getFullYear(), selectedDate.getMonth()) },
      (_, i) => i + 1,
    );

    const handleDateChange = (type: 'year' | 'month' | 'day', value: number) => {
      const newDate = new Date(selectedDate);
      if (type === 'year') {
        newDate.setFullYear(value);
      } else if (type === 'month') {
        newDate.setMonth(value);
      } else if (type === 'day') {
        newDate.setDate(value);
      }
      // setSelectedDateValue(newDate);
      setSelectedDate(newDate);
      // if (!value) {
      //   setSelectedDate(newDate);
      //   // selectedDateValue
      // }
    };

    const handleWheelScroll = (
      e: React.WheelEvent<HTMLDivElement>,
      type: 'year' | 'month' | 'day',
    ) => {
      e.preventDefault();
      const delta = Math.sign(e.deltaY);

      if (type === 'year') {
        const currentIndex = years.findIndex((y) => y === selectedDate.getFullYear());
        const newIndex = Math.min(Math.max(0, currentIndex + delta), years.length - 1);
        handleDateChange('year', years[newIndex]);
      } else if (type === 'month') {
        const currentMonth = selectedDate.getMonth();
        const newMonth = Math.min(Math.max(0, currentMonth + delta), 11);
        handleDateChange('month', newMonth);
      } else if (type === 'day') {
        const currentDay = selectedDate.getDate();
        const maxDay = getDaysInMonth(selectedDate.getFullYear(), selectedDate.getMonth());
        const newDay = Math.min(Math.max(1, currentDay + delta), maxDay);
        handleDateChange('day', newDay);
      }
    };

    // 外部valueが変化すると内部状態を更新する
    useEffect(() => {
      if (value) {
        setSelectedDate(value);
      }
    }, [value]);
    const dateClick = () => {
      onChange?.(selectedDate);
      setSelectedDateValue(format(selectedDate, formatDate));
      setShowWheelPicker(false);
    };
    return (
      <div>
        <Button
          variant={'outline'}
          type="button"
          className={cn(
            'text-black text-base h-[48px] border-border  border rounded-lg py-3 bg-white h-18 pl-4 w-full ',
          )}
          onClick={() => setShowWheelPicker(!showWheelPicker)}
          disabled={disabled}
        >
          {selectedDateValue ? (
            <span>{selectedDateValue}</span>
          ) : (
            <span className="text-muted-foreground">選択する</span>
          )}
          <CalendarIcon className="ml-auto h-6 w-6  text-black" />
        </Button>

        <Drawer
          open={showWheelPicker}
          onOpenChange={setShowWheelPicker}
          modal={true}
          dismissible={false}
        >
          <DrawerContent className="bg-white">
            <DrawerHeader>
              <DrawerTitle>日付の選択</DrawerTitle>
            </DrawerHeader>
            <div>
              <div
                className={`flex flex-col items-center font-sans max-w-md mx-auto ${className}`}
                style={style}
              >
                {showWheelPicker && (
                  <div className="w-full ">
                    <div className="flex w-full h-52 mb-4 border-0 overflow-hidden relative bg-white">
                      {/* トップマスクとボトムマスク */}
                      <div className="absolute top-0 left-0 right-0 h-1/2 bg-gradient-to-b from-white to-transparent pointer-events-none z-10" />
                      <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-white to-transparent pointer-events-none z-10" />

                      {/* 中間選択ガイド */}
                      <div className="absolute top-1/2 left-0 border-y right-0 h-12 transform -translate-y-1/2 z-20 flex pointer-events-none">
                        {/* 年份区域指示器 */}
                        <div className="flex-1 h-full bg-primary-50/5" />
                        {/* 月份区域指示器 */}
                        <div className="flex-1 h-full bg-primary-50/5" />
                        {/* 日期区域指示器 */}
                        <div className="flex-1 h-full bg-primary-50/5" />
                      </div>

                      {/* 年ローラ */}
                      <div
                        ref={yearRef}
                        className="flex-1 overflow-y-scroll snap-y snap-mandatory h-full scrollbar-hide [-ms-overflow-style:none] [scrollbar-width:none] touch-pan-y"
                        onWheel={(e) => handleWheelScroll(e, 'year')}
                        onScroll={() => handleScrollEnd(yearRef, years, 'year')}
                        style={{ touchAction: 'pan-y' }}
                      >
                        {Array.from({ length: 2 }).map((_, i) => (
                          <div key={`year-pad-top-${i}`} className="h-12" />
                        ))}

                        {years.map((year) => (
                          <div
                            key={year}
                            className={`h-12 flex items-center justify-center snap-center cursor-pointer transition-all duration-200 select-none
                    ${year === selectedYear ? 'text-primary text-xl font-bold' : 'text-gray-700'}`}
                            onClick={() => handleDateChange('year', year)}
                          >
                            {year}
                          </div>
                        ))}

                        {Array.from({ length: 2 }).map((_, i) => (
                          <div key={`year-pad-bottom-${i}`} className="h-12" />
                        ))}
                      </div>

                      {/* つきローラ */}
                      <div
                        ref={monthRef}
                        className="flex-1 overflow-y-scroll snap-y snap-mandatory h-full scrollbar-hide [-ms-overflow-style:none] [scrollbar-width:none] touch-pan-y"
                        onWheel={(e) => handleWheelScroll(e, 'month')}
                        onScroll={() => handleScrollEnd(monthRef, months, 'month')}
                        style={{ touchAction: 'pan-y' }}
                      >
                        {Array.from({ length: 2 }).map((_, i) => (
                          <div key={`month-pad-top-${i}`} className="h-12" />
                        ))}

                        {months.map((month) => (
                          <div
                            key={month.value}
                            className={`h-12 flex items-center justify-center snap-center cursor-pointer transition-all duration-200 select-none
                    ${month.value === selectedMonth ? 'text-primary text-xl font-bold' : 'text-gray-700'}`}
                            onClick={() => handleDateChange('month', month.value)}
                          >
                            {month.name}
                          </div>
                        ))}

                        {Array.from({ length: 2 }).map((_, i) => (
                          <div key={`month-pad-bottom-${i}`} className="h-12" />
                        ))}
                      </div>

                      {/* 日付ローラ */}
                      <div
                        ref={dayRef}
                        className="flex-1 overflow-y-scroll snap-y snap-mandatory h-full scrollbar-hide [-ms-overflow-style:none] [scrollbar-width:none] touch-pan-y"
                        onWheel={(e) => handleWheelScroll(e, 'day')}
                        onScroll={() => handleScrollEnd(dayRef, days, 'day')}
                        style={{ touchAction: 'pan-y' }}
                      >
                        {Array.from({ length: 2 }).map((_, i) => (
                          <div key={`day-pad-top-${i}`} className="h-12" />
                        ))}

                        {days.map((day) => (
                          <div
                            key={day}
                            className={`h-12 flex items-center justify-center snap-center cursor-pointer transition-all duration-200 select-none
                    ${day === selectedDay ? 'text-primary text-xl font-bold' : 'text-gray-700'}`}
                            onClick={() => handleDateChange('day', day)}
                          >
                            {day}
                          </div>
                        ))}

                        {Array.from({ length: 2 }).map((_, i) => (
                          <div key={`day-pad-bottom-${i}`} className="h-12" />
                        ))}
                      </div>
                    </div>
                    <DrawerFooter>
                      <Button onClick={dateClick}>{COMMON_TEXT.BUTTON.CONFIRM}</Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowWheelPicker(false);
                          // if (defaultValue) {
                          //   setSelectedDateValue(format(defaultValue, formatDate));
                          //   // onChange?.(defaultValue);
                          // } else {
                          //   setSelectedDateValue('');
                          //   // onChange?.(undefined);
                          // }
                        }}
                      >
                        {COMMON_TEXT.BUTTON.CANCEL}
                      </Button>
                    </DrawerFooter>
                  </div>
                )}
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      </div>
    );
  },
);

WheelDatePicker.displayName = 'WheelDatePicker';

export { WheelDatePicker };
