'use client';
import TopBar from '@/components/layout/top-bar';
import { But<PERSON> } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { Plus, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import Slider, { type Settings } from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { ROUTES } from '@/const/routes';
import { COMMON_TEXT } from '@/const/text/common';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { usePostStore } from '@/store/post';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import CreateMenuPanel from '../_components/create-menu-panel';
import PhotoCreate from '../_components/photo-create';

const ImageSelector = () => {
  const [images, setImages] = useState<{ id: number; url: string }[]>([]);
  const [open, setOpen] = useState(false);
  const handleClose = () => setOpen(false);
  const { isShow, setDialog } = useMessageDialog();
  const {
    setPostId,
    setConfirmPosts,
    setPostAddress,
    setPostLat,
    setPostLng,
    setPostComment,
    setPostCategory,
  } = usePostStore();
  const isDisabled = false;
  const router = useRouter();
  useEffect(() => {
    sendMessageToNative({
      type: 'clear-native-images',
      data: {},
      callback: (data) => {},
    });
  }, []);
  const SliderComponent = Slider as unknown as React.ComponentType<Settings>;
  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    customPaging: () => (
      <div className="mt-2">
        <div className="w-3 h-3 rounded-full bg-gray-300 mx-1" />
      </div>
    ),
    dotsClass: 'slick-dots flex justify-center mt-4',
    autoplay: false,
    arrows: false,
  };

  // 写真を追加
  const handleAddImage = () => {
    setOpen(true);
  };

  // 写真選択/写真撮る
  const setPhotoDialog = (vitalType: string) => {
    sendMessageToNative({
      type: 'contribution',
      data: {
        vitalType: vitalType, // アルバム選択 photo 写真撮る camera
      },
      callback: (data) => {
        if (data?.imageUris.length > 0) {
          nlog(`contribution imageUris length:${data?.imageUris.length}`);
          const addImages = [];
          for (let i = 0; i < data?.imageUris.length; i++) {
            const newImage = {
              id: Date.now() + i,
              url: `data:image/jpeg;base64,${data?.imageUris[i]}`,
            };
            addImages.push(newImage);
          }
          setImages(images.concat(addImages));
        }
      },
    });
    // setDialog(false, {
    //   content: (
    //     <div className="flex flex-col gap-2 ">
    //       <p className="text-center text-xl font-bold">
    //         {APP_TEXT.POST_SELECT_PAGE.CAMERA_POLICY_TITLE1} <br />
    //         {APP_TEXT.POST_SELECT_PAGE.CAMERA_POLICY_TITLE2}
    //       </p>
    //       <p className="text-base">{APP_TEXT.POST_SELECT_PAGE.CAMERA_POLICY_COMMENT}</p>
    //     </div>
    //   ),
    //   outSideClickClose: false,
    //   footer: (
    //     <div className="flex-col ">
    //       <Button
    //         className="w-full"
    //         onClick={() => {
    //           setDialog(false);
    //         }}
    //       >
    //         {COMMON_TEXT.BUTTON.SETTING}
    //       </Button>
    //       <DialogClose asChild>
    //         <TextButton className="mt-2 w-full" variant="muted">
    //           {COMMON_TEXT.BUTTON.CLOSE}
    //         </TextButton>
    //       </DialogClose>
    //     </div>
    //   ),
    // });
  };

  const setCameraDialog = () => {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-2 ">
          <p className="text-center text-xl font-bold">
            {APP_TEXT.POST_SELECT_PAGE.PHOTO_POLICY_TITLE1} <br />
            {APP_TEXT.POST_SELECT_PAGE.PHOTO_POLICY_TITLE2}
          </p>
          <p className="text-base">{APP_TEXT.POST_SELECT_PAGE.PHOTO_POLICY_COMMENT}</p>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            className="w-full"
            onClick={() => {
              setDialog(false);
            }}
          >
            {COMMON_TEXT.BUTTON.SETTING}
          </Button>
          <DialogClose asChild>
            <TextButton className="mt-2 w-full" variant="muted">
              {COMMON_TEXT.BUTTON.CLOSE}
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  };
  // 写真を削除
  const handleDeleteImage = (id: number) => {
    const targetIndex = images.findIndex((image) => image.id === id);
    sendMessageToNative({
      type: 'remove-post-image',
      data: {
        index: targetIndex,
      },
      callback: (data) => {
        setImages(images.filter((image) => image.id !== id));
      },
    });
  };

  // 最後のアイテムか判断する
  const isLastItem = (index: number) => {
    return index === images.length - 1 && images.length === 3;
  };
  // 現状選択中文言
  const getSelectedText = () => {
    return `${images.length}${APP_TEXT.POST.NUM_SELECT}`;
  };

  const handlePostClick = async () => {
    setPostId(undefined);
    setPostAddress(undefined);
    setPostLat(undefined);
    setPostLng(undefined);
    setPostComment(undefined);
    setPostCategory(undefined);
    setConfirmPosts(images);
    router.push(ROUTES.POST.CONFIRM);
  };
  return (
    <div className="flex min-h-screen flex-col">
      <TopBar title={APP_TEXT.POST.NEW_POST} />
      {images.length === 0 ? (
        <PhotoCreate photoCall={setPhotoDialog} cameraCall={setPhotoDialog} />
      ) : (
        <>
          <div className="bg-white slider-container relative">
            <SliderComponent {...settings}>
              {images.map((img, i) => (
                <div
                  key={i}
                  className="w-screen aspect-square overflow-hidden"
                  onClick={() => {}}
                  onKeyDown={() => {}}
                >
                  <img
                    src={`${img.url}`}
                    alt={`slide-${i}`}
                    className="w-full h-full object-cover object-center"
                    draggable={false}
                  />
                </div>
              ))}
            </SliderComponent>
          </div>
          {/* 选中数量显示 */}
          <div className="flex justify-between items-center px-4 py-2 bg-white border-b border-gray-200">
            <span className="text-lg  font-bold text-black">{getSelectedText()}</span>
            <span className="text-sm text-gray-500">{APP_TEXT.POST.MAX_PHOTO_NUM}</span>
          </div>
          <div className="flex w-full">
            {images.map((image, index) => (
              <div
                key={image.id}
                className="relative"
                style={{
                  flex: '0 0 calc(33.333% - 4px)',
                  marginRight: isLastItem(index) ? '0' : '4px',
                }}
              >
                <img
                  src={`${image.url}`}
                  alt="Selected"
                  className="w-full aspect-square object-cover"
                />
                <Button
                  onClick={() => handleDeleteImage(image.id)}
                  className="absolute top-1 right-1 bg-gray-600 w-6 h-6 p-1 rounded-full"
                >
                  <X className="w-6 h-6 text-white" />
                </Button>
              </div>
            ))}

            {images.length < 3 && (
              <div
                className="bg-gray-10"
                style={{
                  flex: '0 0 calc(33.333% - 4px)',
                  aspectRatio: '1',
                  marginLeft: images.length > 0 ? '4px' : '0',
                }}
              >
                <Button
                  onClick={handleAddImage}
                  className="w-full h-full bg-gray-10 flex items-center justify-center"
                >
                  <Plus className="w-6 h-6 text-black" />
                </Button>
              </div>
            )}
          </div>
          <div className="mt-12 mx-6 mb-6">
            <Button
              className="w-full bg-primary disabled:bg-primary-light text-white  font-bold h-12   rounded-[25px]  "
              type="button"
              disabled={isDisabled}
              onClick={handlePostClick}
            >
              <span className="text-[16px] text-white">{COMMON_TEXT.BUTTON.NEXT}</span>
            </Button>
          </div>
        </>
      )}
      <CreateMenuPanel isOpen={open} onClose={handleClose} openNative={setPhotoDialog} />
    </div>
  );
};

export default ImageSelector;
