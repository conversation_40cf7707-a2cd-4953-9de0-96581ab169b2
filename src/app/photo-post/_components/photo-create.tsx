import PhotoEmpty from '@/app/(webview)/post/_components/photo-empty';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useSafeArea } from '@/hooks/use-safe-area';
import { sendMessageToNative } from '@/utils/native-bridge';
import type React from 'react';
type PhotoCreateProps = {
  photoCall: (vitalType: string) => void;
  cameraCall: (vitalType: string) => void;
};
const PhotoCreate: React.FC<PhotoCreateProps> = ({ photoCall, cameraCall }) => {
  const { bottom } = useSafeArea();

  return (
    <div className="flex flex-col flex-1">
      <div className="flex-1 flex items-center justify-center h-[500px]">
        <PhotoEmpty>
          <div className="text-center text-[#666666] text-lg mt-4 font-bold">
            {APP_TEXT.POST_SELECT_PAGE.TEXT1}
          </div>
          <div className="w-full px-7 text-center text-[#666666] text-sm mt-2">
            {APP_TEXT.POST_SELECT_PAGE.TEXT2}
          </div>
        </PhotoEmpty>
      </div>
      <div className="w-full px-5 box-border" style={{ paddingBottom: bottom + 60 }}>
        <Button
          type="button"
          onClick={() => {
            cameraCall('camera');
          }}
          className="w-full h-12 mt-4"
        >
          {APP_TEXT.POST_SELECT_PAGE.BUTTON_CAMERA}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => {
            photoCall('photo');
          }}
          className="w-full h-12 mt-4"
        >
          {APP_TEXT.POST_SELECT_PAGE.BUTTON_PHOTO_SELECT}
        </Button>
      </div>
    </div>
  );
};

export default PhotoCreate;
