import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerHeader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@/components/shared/drawer';
import { APP_TEXT } from '@/const/text/app';
import { Plus, X } from 'lucide-react';
export default function CreateMenuPanel({
  isOpen,
  onClose,
  openNative,
}: { isOpen: boolean; onClose: () => void; openNative: (vitalType: string) => void }) {
  const openCamera = () => {
    openNative('camera');
    onClose();
  };
  const openPhoto = () => {
    openNative('photo');
    onClose();
  };
  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerTrigger asChild>
        <img src="/images/post/icon-post-detail.png" alt="" className="w-7 h-7 ml-auto mt-[-2px]" />
      </DrawerTrigger>
      <DrawerContent className="height-auto-important pb-5">
        <DrawerHeader className="relative">
          <DrawerTitle className="font-bold text-[18px]">
            {APP_TEXT.POST_SELECT_PAGE.DETAIL_MENU}
          </DrawerTitle>
          <DrawerDescription className="sr-only" />
          <DrawerClose asChild>
            <button type="button" className="absolute right-5 top-3">
              <X size={24} />
            </button>
          </DrawerClose>
        </DrawerHeader>
        {/* 自分 */}
        <>
          {/* カメラ */}
          <div
            className="h-[40px] flex items-center px-10 mb-4"
            onKeyDown={() => {}}
            onClick={() => {
              openCamera();
            }}
          >
            <img src="/images/post/icon-camera.png" alt="" className="w-5 h-5 mr-1" />
            <span className="text-[#4457D1] text-[16px] font-bold">
              {' '}
              {APP_TEXT.POST_SELECT_PAGE.BUTTON_CAMERA}
            </span>
          </div>
          {/* 写真選択 */}
          <div
            className="h-[40px] flex items-center px-10 mb-4"
            onKeyDown={() => {}}
            onClick={() => {
              openPhoto();
            }}
          >
            <img src="/images/post/icon-photo.png" alt="" className="w-5 h-5 mr-1" />
            <span className="text-[#4457D1] text-[16px] font-bold">
              {' '}
              {APP_TEXT.POST_SELECT_PAGE.BUTTON_PHOTO_SELECT}
            </span>
          </div>
        </>
      </DrawerContent>
    </Drawer>
  );
}
