import type { PostClassifyListResponse, PostInterface } from '@/types/post';
import { create } from 'zustand';

interface MyPhotoSubmissionState {
  userPost: PostClassifyListResponse;
  setPostList: (userPost: PostClassifyListResponse) => void;
  resetPostList: () => void;
  updatePostList: (groupList: PostInterface[]) => void;
}

export const useMyPhotoSubmissionStore = create<MyPhotoSubmissionState>()((set, get) => ({
  userPost: {
    userId: 0,
    nickName: '',
    isFriend: 0,
    avatar: '',
    isOneself: 0,
    posts: [],
    pagination: {
      total: 0,
      page: 1,
      limit: 15,
      pages: 0,
    },
    postStatistics: {
      postCount: 0,
      inReview: 0,
      reject: 0,
    },
  },
  resetPostList: () => {
    set(() => ({
      userPost: {
        userId: 0,
        nickName: '',
        isFriend: 0,
        avatar: '',
        isOneself: 0,
        posts: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 15,
          pages: 0,
        },
        postStatistics: {
          postCount: 0,
          inReview: 0,
          reject: 0,
        },
      },
    }));
  },
  updatePostList: (postList: PostInterface[]) => {
    set(() => ({
      userPost: {
        ...get().userPost,
        posts: postList,
      },
    }));
  },
  setPostList: (userPost: PostClassifyListResponse) => {
    const { posts } = get().userPost;
    set(() => ({
      userPost: {
        ...userPost,
        posts: [...posts, ...userPost.posts],
      },
    }));
  },
}));
