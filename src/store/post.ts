import { create } from 'zustand';

import type { ConfirmPost, PostInterface } from '@/types/post';
type Layout = 'overview' | 'grid';
interface PostState {
  posts: PostInterface[];
  confirmPosts: ConfirmPost[];
  sortKind: number;
  layout: Layout;
  postId?: string;
  postAddress?: string;
  postLat?: string;
  postLng?: string;
  postComment?: string;
  postCategory?: string;
  onMap?: string;
  setPosts: (posts: PostInterface[]) => void;
  setConfirmPosts: (posts: ConfirmPost[]) => void;
  setSortKind: (sortKind: number) => void;
  setLayout: (layout: Layout) => void;
  setPostAddress: (postAddress?: string) => void;
  setPostLat: (lat?: string) => void;
  setPostId: (postId?: string) => void;
  setPostLng: (lng?: string) => void;
  setPostComment: (postComment?: string) => void;
  setOnMap: (onMap?: string) => void;
  setPostCategory: (postCategory?: string) => void;
}

export const usePostStore = create<PostState>((set) => ({
  posts: [],
  confirmPosts: [],
  sortKind: 1,
  layout: 'overview',
  postId: undefined,
  postAddress: undefined,
  postLat: undefined,
  postLng: undefined,
  postComment: undefined,
  postCategory: undefined,
  onMap: undefined,
  setPosts: (posts) => set({ posts: posts }),
  setConfirmPosts: (posts) => set({ confirmPosts: posts }),
  setSortKind: (order) => set({ sortKind: order }),
  setLayout: (str) => set({ layout: str }),
  setPostAddress: (postAddress) => set({ postAddress: postAddress }),
  setPostId: (postId) => set({ postId: postId }),
  setPostLat: (lat) => set({ postLat: lat }),
  setPostLng: (lng) => set({ postLng: lng }),
  setPostComment: (postComment?: string) => set({ postComment: postComment }),
  setPostCategory: (postCategory?: string) => set({ postCategory: postCategory }),
  setOnMap: (onMap?: string) => set({ onMap: onMap }),
}));
