// hooks/use-service-setting.ts
import { ServiceSettingRequest, dataConnectAPI } from '@/api/modules/data-connect';
import { DeviceType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { sendMessageToNative } from '@/utils/native-bridge';

export const useHealthSetting = () => {
  //   const { setLoading } = useLoading();
  //   const router = useRouter();
  const allPermissionArrary = [1, 2, 3, 5]; //   DeviceType.VitalType

  // サーバーに送信する関数
  const sendHealthSetting = async (
    scope: number[],
    deviceType: DeviceType = DeviceType.HealthConnect,
  ) => {
    // 権限がないときの処理
    if (scope.length === 0) {
      //       router.replace(`${ROUTES.DATA_CONNECT.HEATHCON_FINISH}?success=false`);
      return;
    }

    const request = new ServiceSettingRequest();
    request.deviceType = deviceType; // デバイスタイプの設定
    request.connectFlg = 1; // 接続フラグを設定
    request.scope = scope; // スコープの設定

    //     setLoading(true);

    try {
      // サービス設定APIの呼び出し
      await dataConnectAPI.serviceSetting(request);
      //       setLoading(false);

      // 承認が完了し、アップロード手順が完了しました
      sendMessageToNative({
        type: 'sync-step-source',
        callback: () => {},
      });

      // 成功時の遷移
    } catch (error) {
      // エラー時の処理
      //       setLoading(false);
      //       throw error;
    }
  };

  return { sendHealthSetting, allPermissionArrary };
};
