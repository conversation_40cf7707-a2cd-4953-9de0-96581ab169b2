import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * ページの可視性を監視するカスタムフック
 * タブの切り替えやウィンドウのフォーカス変更を検知する
 *
 * @returns {Object} ページの可視性情報とイベントハンドラ
 */
export const usePageVisibility = () => {
  // ページが現在表示されているかどうかの状態
  const [isVisible, setIsVisible] = useState<boolean>(!document.hidden);
  // コールバック関数を保持するRef（useEffectの再実行を避けるため）
  const callbackRef = useRef<(() => void) | null>(null);
  // 最後にコールバックが実行された時刻（デバウンス用）
  const lastExecutionRef = useRef<number>(0);

  useEffect(() => {
    /**
     * ページの可視性が変更された時のイベントハンドラ
     * 重複実行を防ぐロジックを含む
     */
    const handleVisibilityChange = () => {
      const visible = !document.hidden;
      const now = Date.now();

      // 状態更新
      setIsVisible(visible);

      // 重複実行を防ぐ条件チェック
      // 1. ページが表示されている場合
      // 2. 100ms以内の連続実行を防ぐ（デバウンス）
      // 3. コールバックが設定されている
      if (visible && callbackRef.current && now - lastExecutionRef.current > 100) {
        lastExecutionRef.current = now;
        callbackRef.current();
      }
    };

    /**
     * ウィンドウフォーカス時のハンドラ
     * visibilitychangeと重複しないよう条件を調整
     */
    const handleFocus = () => {
      const now = Date.now();

      setIsVisible(true);

      // Page Visibility APIが対応していない古いブラウザでのフォールバック
      // または、visibilitychangeが発火しない特殊なケースの対応
      if (callbackRef.current && now - lastExecutionRef.current > 100) {
        lastExecutionRef.current = now;
        callbackRef.current();
      }
    };

    const handleBlur = () => {
      setIsVisible(false);
    };

    // イベントリスナーの登録
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    // クリーンアップ関数
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, []); // 依存配列を空にしてイベントリスナーの重複登録を防ぐ

  /**
   * ページが表示された時に実行するコールバックを設定
   * useCallbackで最適化し、Refに保存して重複実行を防ぐ
   *
   * @param callback - 実行するコールバック関数
   */
  const setVisibilityCallback = useCallback((callback: () => void) => {
    callbackRef.current = callback;
  }, []);

  return {
    isVisible,
    setVisibilityCallback,
  };
};
