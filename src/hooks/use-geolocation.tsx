'use client';
// import { orgGeoLocationAPI } from '@/api/modules/org-geo-location';
import { usePathname } from '@/hooks/use-next-navigation';
import { useAuthStore } from '@/store/auth';
import { useDevModeStore } from '@/store/dev-mode';
import type { OrganizerGeoLocationResponse } from '@/types/org-geo-location';
import { sendMessageToNative } from '@/utils/native-bridge';
// import { useQuery } from '@tanstack/react-query';
import { createContext, useContext, useEffect, useRef, useState } from 'react';

type LatLng = { lat: number; lng: number };

type GeolocationContextValue = {
  location: LatLng;
  setLocation: (latlng: LatLng) => void;
  orgGeoLocationData: OrganizerGeoLocationResponse;
  // ホーム画面での組織の位置情報を設定
  setOrgGeoLocationData: (orgGeoLocationData: OrganizerGeoLocationResponse) => void;
  triggerLocationUpdate: () => void;
};

const GeolocationContext = createContext<GeolocationContextValue | null>(null);

// type GetLocationResponse = {
//   permission: boolean;
//   latitude?: string;
//   longitude?: string;
// };

/** 位置を共有するProvider */
export function GeolocationProvider({ children }: { children: React.ReactNode }) {
  const [location, setLocation] = useState<LatLng>({ lat: 35.681236, lng: 139.767125 });
  const [orgGeoLocationData, setOrgGeoLocationData] = useState<OrganizerGeoLocationResponse>({
    resultList: [],
  });
  const pathname = usePathname();
  const { log: nlog } = useDevModeStore();
  const [time, setTime] = useState(0);
  const { user } = useAuthStore();
  const updateTimeExpiredAt = useRef(0);
  // const { data: orgGeoLocationData } = useQuery({
  //   queryKey: ['orgGeoLocation'],
  //   queryFn: () => orgGeoLocationAPI.organizerLocations(),
  //   enabled: false,
  // });

  const triggerLocationUpdate = () => {
    sendMessageToNative({
      type: 'get-user-location',
      callback: (data) => {
        nlog(`use-geolocation: get-user-location triggerLocationUpdate:${JSON.stringify(data)}`);
        if (data?.permission && data.latitude) {
          setLocation({ lat: Number(data.latitude), lng: Number(data.longitude) });
          nlog(`[${pathname}] location: ${data.latitude}, ${data.longitude}`);
        }
      },
    });
  };

  useEffect(() => {
    nlog(
      `use-geolocation: orgGeoLocationAPI.organizerLocations: ${JSON.stringify(orgGeoLocationData)}`,
    );
    if (orgGeoLocationData?.resultList?.length && orgGeoLocationData.resultList.length > 0) {
      nlog(`use-geolocation: user?.useOrganizerID: ${user?.useOrganizerID}`);
      const organizerId = user?.useOrganizerID;
      const organizerLocation = orgGeoLocationData.resultList.find(
        (item) => item.organizerId === organizerId,
      );
      nlog(
        `use-geolocation: orgGeoLocationData.resultList.find: ${JSON.stringify(organizerLocation)}`,
      );
      if (organizerLocation) {
        nlog('use-geolocation: set orgGeoLocationData success!');
        setLocation({
          lat: Number(orgGeoLocationData.resultList[0].latitude),
          lng: Number(orgGeoLocationData.resultList[0].longitude),
        });
      }
    }
  }, [orgGeoLocationData, user?.useOrganizerID]);

  useEffect(() => {
    nlog(`use-geolocation: useEffect1: ${pathname}`);
    triggerLocationUpdate();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setTime((prev) => prev + 1);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // nlog(`use-geolocation: useEffect2: ${pathname}`);
    if (
      (pathname.includes('map') ||
        pathname.includes('walking-course') ||
        pathname.includes('event') ||
        pathname.includes('coupon')) &&
      updateTimeExpiredAt.current < new Date().getTime()
    ) {
      nlog(`use-geolocation: get-user-location callback2: ${pathname}`);
      updateTimeExpiredAt.current = new Date().getTime() + 5000;
      triggerLocationUpdate();
    }
  }, [pathname, time]);

  // // 例: ブラウザのwatchPositionを一元管理（必要なら）
  // useEffect(() => {
  //   if (!navigator.geolocation) return;
  //   const watchId = navigator.geolocation.watchPosition(
  //     (pos) => setLocation({ lat: pos.coords.latitude, lng: pos.coords.longitude }),
  //     () => {}
  //   );
  //   return () => navigator.geolocation.clearWatch(watchId);
  // }, []);

  return (
    <GeolocationContext.Provider
      value={{
        location,
        setLocation,
        orgGeoLocationData,
        setOrgGeoLocationData,
        triggerLocationUpdate,
      }}
    >
      {children}
    </GeolocationContext.Provider>
  );
}

/** 共有位置を取得するhook */
export const useGeolocation = () => {
  const ctx = useContext(GeolocationContext);
  if (!ctx) throw new Error('useGeolocation must be used within GeolocationProvider');
  return ctx;
};
