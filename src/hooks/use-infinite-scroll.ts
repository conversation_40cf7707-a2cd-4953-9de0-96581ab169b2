import { useCallback, useEffect, useRef } from 'react';

interface UseInfiniteScrollProps {
  hasMore?: boolean;
  loading?: boolean;
  onLoadMore: () => void;
  rootMargin: string;
}

export const useInfiniteScroll = ({
  hasMore,
  loading,
  onLoadMore,
  rootMargin = '0px',
}: UseInfiniteScrollProps) => {
  const observerRef = useRef<IntersectionObserver | null>(null);

  const lastItemRef = useCallback(
    (node: HTMLElement | null) => {
      if (loading || !hasMore) return;

      if (observerRef.current) observerRef.current.disconnect();

      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            onLoadMore();
          }
        },
        {
          rootMargin,
        },
      );

      if (node) observerRef.current.observe(node);
    },
    [hasMore, loading, onLoadMore, rootMargin],
  );
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);
  return { lastItemRef };
};
