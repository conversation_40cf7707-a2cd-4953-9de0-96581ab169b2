'use client';

import { gamificationAPI } from '@/api/modules/my-page';
import { LevelUpDialog } from '@/app/(webview)/my-page/_components/level-up-dialog';
import type { MissionListResponse } from '@/types/my-page';
import React, { useCallback, useState } from 'react';

interface LevelUpContextType {
  checkLevelUp: (pageName?: string, callback?: (msg: 'levelUp' | '') => void) => void;
  openDialog: () => void;
  currentLevel: string;
}

const LevelUpContext = React.createContext<LevelUpContextType>({
  checkLevelUp: async () => {},
  openDialog: () => {},
  currentLevel: '1',
});

export function LevelUpProvider({ children }: { children: React.ReactNode }) {
  const [dialogState, setDialogState] = useState(false);
  const [levelUpData, setLevelUpData] = useState<MissionListResponse | null>(null);
  const [currentLevel, setCurrentLevel] = useState('1');

  const openDialog = () => {
    setDialogState(true);
  };

  const checkLevelUp = (pageName?: string, callback?: (msg: 'levelUp' | '') => void) => {
    gamificationAPI
      .lvUpMission({
        homeUseFlg: pageName === 'home' ? 1 : 0,
      })
      .then((res) => {
        setCurrentLevel(res?.levelBasicInfo?.currentLevel ?? '1');
        if (res?.levelUpInfo?.levelUpFlg === '1') {
          setLevelUpData(res);
          setDialogState(true);
          if (callback) {
            callback('levelUp');
          }
        } else {
          if (callback) {
            callback('');
          }
        }
      })
      .catch((err) => {
        if (callback) {
          callback('');
        }
        console.log('Error checking level up:', err);
      });
  };

  const closeDialog = useCallback(() => {
    setDialogState(false);
    setLevelUpData(null);
  }, []);

  return (
    <LevelUpContext.Provider
      value={{
        checkLevelUp,
        openDialog,
        currentLevel,
      }}
    >
      {children}

      {/* Dialog 直接在 Provider 中渲染 */}
      <LevelUpDialog
        data={levelUpData}
        isOpen={dialogState}
        onClose={closeDialog}
        preventOutsideClick={false}
      />
    </LevelUpContext.Provider>
  );
}

export const useLevelUp = () => React.useContext(LevelUpContext);
