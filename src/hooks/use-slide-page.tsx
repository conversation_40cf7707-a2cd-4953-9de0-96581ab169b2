'use client';

import SlidePage from '@/components/layout/silde-page';
import { usePathname } from '@/hooks/use-next-navigation';
import React, { useCallback, useEffect, useState } from 'react';

interface SlidePageData {
  title: string;
  content: React.ReactNode;
  isOverAll?: boolean;
  enableClose?: boolean;
  enableBack?: boolean;
  slideFrom?: 'bottom' | 'right';
  hideTopBar?: boolean;
  onClose?: () => void;
  height?: string;
}

const SlidePageContext = React.createContext<{
  isOpen: boolean;
  data?: SlidePageData;
  setSlidePage: (open: boolean, data?: SlidePageData) => void;
}>({
  isOpen: false,
  setSlidePage: () => {},
});

export function SlidePageProvider({ children }: { children: React.ReactNode }) {
  const [isOpenState, setIsOpenState] = useState(false);
  const [data, setData] = useState<SlidePageData>();
  const pathname = usePathname();

  useEffect(() => {
    setIsOpenState(false);
  }, [pathname, setIsOpenState]);

  const setSlidePage = useCallback((open: boolean, data?: SlidePageData) => {
    if (open) {
      setIsOpenState(true);
      setData(data);
    } else {
      setIsOpenState(false);
    }
  }, []);

  const handleClose = () => {
    setIsOpenState(false);
    data?.onClose?.();
  };

  return (
    <SlidePageContext.Provider value={{ isOpen: isOpenState, setSlidePage }}>
      {children}
      <SlidePage
        title={data?.title}
        hideTopBar={data?.hideTopBar ?? false}
        content={data?.content}
        isOpen={isOpenState}
        isOverAll={data?.isOverAll}
        height={data?.height}
        enableClose={data?.enableClose}
        enableBack={data?.enableBack}
        slideFrom={data?.slideFrom}
        onClose={handleClose}
      />
    </SlidePageContext.Provider>
  );
}

export const useSlidePage = () => React.useContext(SlidePageContext);
