import { homePageAPI } from '@/api/modules/home-page';
import { useMiniAppHealthCheckupLink } from '@/hooks/use-health-checkup-link';
import { useAuthStore } from '@/store/auth';
import { useDevModeStore } from '@/store/dev-mode';
import type { MenuButton } from '@/types/home-data';
import { createContext, useContext, useState } from 'react';

const DynamicMenuContext = createContext<{
  refreshButtons: () => Promise<void>;
  buttonList: MenuButton[];
  clearButtonList: () => void;
}>({
  refreshButtons: () => Promise.resolve(),
  buttonList: [],
  clearButtonList: () => {},
});

export function DynamicMenuProvider({ children }: { children: React.ReactNode }) {
  const [buttonList, setButtonList] = useState<MenuButton[]>([]);
  const { openLink: openHealthCheckupLink } = useMiniAppHealthCheckupLink();
  const { user } = useAuthStore.getState();
  const { log } = useDevModeStore();

  const order = [32, 22, 42, 31, 41, 44, 46, 33, 24, 25, 26, 13, 47];
  const menuButtonItem1: MenuButton[] = [
    { id: 32, label: 'グラフ', url: '/graph', iconKey: 'graph' },
    { id: 22, label: '健康記録', url: '/health-record', iconKey: 'score' },
    { id: 42, label: 'ミッション', url: '/mission', iconKey: 'mission' },
    { id: 31, label: 'ランキング', url: '/ranking', iconKey: 'ranking' },
    { id: 41, label: 'ウォーキングコース', url: '/walking-course/search', iconKey: 'walking' },
    { id: 44, label: 'イベント', url: '/event', iconKey: 'event' },
    { id: 46, label: 'クーポン', url: '/coupon', iconKey: 'coupon' },
    // { id: 13, label: 'プロフィール変更', url: '', iconKey: 'userSetting' },
  ];

  const menuButtonItem2: MenuButton[] = [
    { id: 33, label: 'ヘルスチェックAI', url: '/health-score', iconKey: 'healthCheck' },
    { id: 24, label: '健康診断結果登録', url: '/health-checkup', iconKey: 'result' },
    {
      id: 25,
      label: '健診・検診日の記録',
      callback: () => openHealthCheckupLink(),
      iconKey: 'record',
    },
  ];

  const newButtonList: MenuButton[] = [
    { id: 32, label: 'グラフ', url: '/graph', iconKey: 'graph' },
    { id: 22, label: '健康記録', url: '/health-record', iconKey: 'score' },
    { id: 42, label: 'ミッション', url: '/mission', iconKey: 'mission' },
    { id: 33, label: 'ヘルスチェックAI', url: '/health-score', iconKey: 'healthCheck' },
  ];

  const refreshButtons = async () => {
    try {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'dev') {
        const organizerUseCodes = user?.organizerCode ? user?.organizerCode.split(',') : [];
        if (organizerUseCodes.length === 0) {
          setButtonList(menuButtonItem1);
          log('测试111');
          log('DynamicMenuProvider: refreshButtons organizerUseCodes is empty');
          log(`DynamicMenuProvider: refreshButtons: ${JSON.stringify(user)}`);
          return;
        }
        const result = await homePageAPI.getOrganizerInfo({ organizerCode: organizerUseCodes });
        const enabledIds = (result?.optionUseStatus ?? [])
          .filter((item) => item.useSts === 1)
          .map((item) => item.fcnId);

        const someButtonList = menuButtonItem2.filter((btn) => enabledIds.includes(btn.id));
        const allButtons = [...menuButtonItem1, ...someButtonList];

        const sortedButtons: MenuButton[] = order
          .map((id) => allButtons.find((btn) => btn.id === id))
          .filter(Boolean) as MenuButton[];

        setButtonList(sortedButtons);
      } else {
        setButtonList(newButtonList);
      }
    } catch (error) {
      console.error('refreshButtons error:', error);
    }
  };

  const clearButtonList = () => {
    setButtonList([]);
  };

  return (
    <DynamicMenuContext.Provider value={{ refreshButtons, buttonList, clearButtonList }}>
      {children}
    </DynamicMenuContext.Provider>
  );
}

export const useDynamicMenu = () => useContext(DynamicMenuContext);
