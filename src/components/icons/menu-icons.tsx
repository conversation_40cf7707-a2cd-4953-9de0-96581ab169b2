import { COLORS } from '@/const/colors';
import type React from 'react';

interface SvgIconProps {
  size?: number;
  className?: string;
  fill?: string;
}
export const MenuGraphIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <path
          d="M26.975 26.5V8H21.975V26.5H20.475V13H15.475V26.5H13.975V18H8.975V26.5H7V28H29V26.5H26.975ZM23.475 9.5H25.475V26.5H23.475V9.5ZM16.975 14.5H18.975V26.5H16.975V14.5ZM10.475 19.5H12.475V26.5H10.475V19.5Z"
          fill="currentColor"
        />
      </svg>
    </div>
  );
};
export const MenuScoreIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <path
          d="M17.0218 10.25C16.1668 10.25 15.8268 10.955 15.6668 11.31C15.5118 10.95 15.1668 10.25 14.3118 10.25C13.3318 10.25 12.8318 11.2 12.9318 12.055C13.0168 12.805 13.5518 13.525 14.0568 14.055C14.5418 14.52 15.0818 14.92 15.6668 15.25C16.2518 14.92 16.7918 14.515 17.2768 14.05C17.7768 13.525 18.3118 12.8 18.4018 12.05C18.5018 11.195 18.0018 10.25 17.0218 10.25Z"
          fill="currentColor"
        />
        <path
          d="M11.4167 27.25V8.75H25.9167V17.08L27.4167 15.58V7.25H9.91675V28.75H17.4217L17.7217 27.25H11.4167Z"
          fill="currentColor"
        />
        <path
          d="M18.9617 28.705L19.6017 25.515L26.9267 18.19L29.4767 20.74L22.1517 28.065L18.9617 28.705ZM20.9817 26.255L20.8717 26.79L21.4067 26.68L27.3517 20.735L26.9217 20.305L20.9767 26.25L20.9817 26.255Z"
          fill="currentColor"
        />
        <path d="M23.9167 16.76H12.9167V18.26H23.9167V16.76Z" fill="currentColor" />
        <path d="M19.9167 19.775H12.9167V21.275H19.9167V19.775Z" fill="currentColor" />
      </svg>
    </div>
  );
};
export const MenuMissionIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g transform="translate(7, 7)">
          <path
            d="M12.4982 10.44L11.1932 8.315L9.91821 9.095L11.9632 12.435L17.8932 9.215L17.1782 7.895L12.4982 10.44Z"
            fill="currentColor"
          />
          <path
            d="M22.9383 5.61C22.7533 5.42 22.4833 5.34 22.2283 5.4C19.7333 5.985 17.6733 4.805 15.4883 3.55C13.1483 2.21 10.7233 0.82 7.65829 1.545C7.41329 1.605 7.21829 1.785 7.13329 2.015H7.12829L7.11829 2.055C7.11829 2.06 7.11329 2.07 7.10829 2.075L4.17829 12.87C4.15829 12.945 4.15329 13.015 4.15329 13.09L1.62329 22.515L3.07329 22.905L5.53829 13.71C7.82329 13.365 9.76329 14.475 11.8083 15.65C13.7083 16.735 15.6583 17.855 17.9633 17.855C18.5033 17.855 19.0583 17.795 19.6383 17.66C19.9083 17.595 20.1183 17.39 20.1883 17.125L23.1183 6.33C23.1883 6.075 23.1183 5.805 22.9333 5.615L22.9383 5.61ZM18.8683 16.275C16.5683 16.64 14.6133 15.52 12.5583 14.34C10.6583 13.255 8.70829 12.135 6.40329 12.135C6.25829 12.135 6.10829 12.14 5.95829 12.15L8.43829 2.915C10.7383 2.555 12.6883 3.67 14.7433 4.85C16.7733 6.01 18.8583 7.205 21.3783 7.04L18.8733 16.275H18.8683Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </div>
  );
};
export const MenuRankingIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        {' '}
        <g transform="translate(7, 6)">
          <path
            d="M20.4999 3.785C19.6699 3.785 18.9999 4.455 18.9999 5.285C18.9999 5.685 19.1599 6.05 19.4149 6.32L14.6899 10.83L11.5249 3.62C12.0549 3.39 12.4249 2.86 12.4249 2.25C12.4249 1.42 11.7549 0.75 10.9249 0.75C10.0949 0.75 9.42488 1.42 9.42488 2.25C9.42488 2.865 9.79488 3.39 10.3249 3.62L7.19988 10.745L2.58488 6.32C2.84488 6.05 3.00488 5.685 3.00488 5.285C3.00488 4.455 2.33488 3.785 1.50488 3.785C0.674883 3.785 0.00488281 4.455 0.00488281 5.285C0.00488281 6.115 0.674883 6.785 1.50488 6.785C1.57488 6.785 1.63988 6.775 1.70488 6.765L3.39988 19.25H18.6049L20.2999 6.765C20.3649 6.775 20.4299 6.785 20.4999 6.785C21.3299 6.785 21.9999 6.115 21.9999 5.285C21.9999 4.455 21.3299 3.785 20.4999 3.785ZM17.2899 17.75H4.70988L3.56488 9.345L6.15988 11.83L7.70988 13.315L8.56988 11.35L10.9249 5.985L13.3149 11.435L14.1749 13.395L15.7249 11.915L18.4349 9.33L17.2899 17.75Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </div>
  );
};
export const MenuWalkingIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g transform="translate(6, 6)">
          <path
            d="M13.3551 5.71C14.5895 5.71 15.5901 4.70936 15.5901 3.475C15.5901 2.24064 14.5895 1.24 13.3551 1.24C12.1208 1.24 11.1201 2.24064 11.1201 3.475C11.1201 4.70936 12.1208 5.71 13.3551 5.71Z"
            fill="currentColor"
          />
          <path
            d="M9.1699 22.215L7.2849 21.55L9.7949 14.435L10.5649 9.47L9.2649 10.375L8.6099 13.625L6.6499 13.23L7.3849 9.585C7.4349 9.335 7.5799 9.11 7.7949 8.965L11.3449 6.49C11.6749 6.26 12.1049 6.25 12.4449 6.46C12.7849 6.67 12.9649 7.065 12.9049 7.46L11.7599 14.83C11.7499 14.89 11.7349 14.95 11.7149 15.01L9.1749 22.215H9.1699Z"
            fill="currentColor"
          />
          <path
            d="M16.075 22.085L14.075 21.985L14.29 17.64L11.415 15.595L12.575 13.965L15.895 16.33C16.175 16.53 16.33 16.855 16.315 17.195L16.075 22.085Z"
            fill="currentColor"
          />
          <path
            d="M13.1283 9.45126L11.9058 11.0341L16.0291 14.2187L17.2516 12.6359L13.1283 9.45126Z"
            fill="currentColor"
          />
          <path
            d="M11.6849 15.595C11.6149 15.595 11.5399 15.595 11.4649 15.58C10.5049 15.46 9.82488 14.585 9.94488 13.625L10.6849 7.68C10.8049 6.72 11.6799 6.045 12.6349 6.16C13.5949 6.28 14.2749 7.155 14.1549 8.115L13.4149 14.06C13.3049 14.945 12.5499 15.595 11.6799 15.595H11.6849Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </div>
  );
};
export const MenuHealthCheckIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g transform="translate(6, 6)">
          <path
            d="M15.575 18.145C15.765 18.05 15.92 17.925 16.03 17.765C16.155 17.585 16.22 17.375 16.22 17.135C16.22 16.855 16.155 16.62 16.03 16.425C15.905 16.23 15.73 16.085 15.505 15.985C15.28 15.885 15.025 15.835 14.73 15.835C14.435 15.835 14.185 15.885 13.96 15.985C13.735 16.085 13.56 16.23 13.43 16.425C13.3 16.615 13.24 16.855 13.24 17.135C13.24 17.375 13.305 17.585 13.43 17.765C13.54 17.925 13.7 18.045 13.885 18.145C13.78 18.195 13.68 18.25 13.59 18.32C13.445 18.43 13.335 18.565 13.255 18.725C13.175 18.885 13.135 19.06 13.135 19.26C13.135 19.55 13.205 19.8 13.345 19.995C13.485 20.19 13.675 20.345 13.915 20.44C14.155 20.54 14.425 20.59 14.73 20.59C15.035 20.59 15.305 20.54 15.545 20.44C15.785 20.34 15.97 20.19 16.11 19.995C16.25 19.8 16.315 19.55 16.315 19.26C16.315 18.995 16.245 18.765 16.11 18.575C15.98 18.39 15.8 18.25 15.575 18.145ZM14.29 16.875C14.33 16.79 14.385 16.725 14.46 16.68C14.535 16.635 14.625 16.61 14.725 16.61C14.825 16.61 14.925 16.635 15 16.68C15.075 16.73 15.13 16.795 15.17 16.88C15.21 16.965 15.23 17.065 15.23 17.185C15.23 17.305 15.21 17.405 15.175 17.495C15.14 17.585 15.08 17.65 15.01 17.7C14.935 17.75 14.845 17.775 14.74 17.775C14.635 17.775 14.545 17.75 14.47 17.7C14.395 17.65 14.34 17.58 14.3 17.495C14.26 17.405 14.24 17.305 14.24 17.185C14.24 17.065 14.26 16.96 14.3 16.875H14.29ZM15.245 19.525C15.195 19.62 15.13 19.695 15.04 19.745C14.95 19.795 14.85 19.82 14.73 19.82C14.61 19.82 14.505 19.795 14.415 19.745C14.325 19.695 14.255 19.62 14.205 19.525C14.155 19.43 14.13 19.315 14.13 19.185C14.13 19.055 14.155 18.935 14.205 18.84C14.255 18.745 14.325 18.675 14.415 18.62C14.505 18.565 14.61 18.54 14.725 18.54C14.84 18.54 14.945 18.565 15.035 18.62C15.125 18.675 15.195 18.745 15.245 18.84C15.295 18.935 15.32 19.05 15.32 19.185C15.32 19.32 15.295 19.43 15.245 19.525Z"
            fill="currentColor"
          />
          <path
            d="M19.8551 17.075C19.8051 16.865 19.7351 16.68 19.6451 16.525C19.5551 16.37 19.4401 16.24 19.3101 16.14C19.1801 16.04 19.0301 15.965 18.8701 15.915C18.7101 15.865 18.5351 15.84 18.3451 15.84C18.1101 15.84 17.8951 15.88 17.7001 15.955C17.5051 16.03 17.3401 16.15 17.1951 16.31C17.0501 16.47 16.9451 16.675 16.8701 16.92C16.7951 17.165 16.7551 17.46 16.7551 17.8V18.62C16.7551 18.89 16.7801 19.135 16.8301 19.345C16.8801 19.56 16.9501 19.745 17.0451 19.895C17.1401 20.045 17.2501 20.18 17.3801 20.28C17.5101 20.385 17.6551 20.46 17.8201 20.505C17.9851 20.555 18.1601 20.58 18.3451 20.58C18.5801 20.58 18.7951 20.54 18.9901 20.465C19.1851 20.39 19.3501 20.27 19.4901 20.11C19.6301 19.95 19.7401 19.745 19.8151 19.495C19.8901 19.245 19.9301 18.955 19.9301 18.615V17.795C19.9301 17.52 19.9051 17.28 19.8551 17.065V17.075ZM18.9401 18.75C18.9401 18.945 18.9251 19.115 18.9001 19.25C18.8751 19.39 18.8351 19.5 18.7801 19.58C18.7301 19.665 18.6651 19.725 18.5951 19.76C18.5251 19.795 18.4401 19.815 18.3451 19.815C18.2701 19.815 18.2001 19.805 18.1401 19.78C18.0801 19.755 18.0201 19.72 17.9701 19.665C17.9201 19.61 17.8801 19.545 17.8501 19.46C17.8151 19.375 17.7901 19.275 17.7751 19.155C17.7551 19.035 17.7501 18.9 17.7501 18.74V17.665C17.7501 17.47 17.7651 17.3 17.7901 17.165C17.8151 17.03 17.8551 16.92 17.9051 16.84C17.9551 16.76 18.0201 16.7 18.0901 16.66C18.1601 16.62 18.2501 16.605 18.3401 16.605C18.4151 16.605 18.4801 16.615 18.5451 16.64C18.6051 16.665 18.6651 16.7 18.7101 16.75C18.7551 16.8 18.8001 16.87 18.8301 16.95C18.8651 17.035 18.8901 17.135 18.9051 17.25C18.9251 17.37 18.9301 17.505 18.9301 17.665V18.74L18.9401 18.75Z"
            fill="currentColor"
          />
          <path
            d="M21.595 14.13L20.705 15.02C21.355 15.89 21.745 16.96 21.745 18.125C21.745 19.41 21.275 20.585 20.5 21.495C19.93 22.165 19.195 22.68 18.355 22.995H20.76C21.255 22.565 21.685 22.06 22.03 21.495C22.635 20.51 22.99 19.36 22.99 18.125C22.99 16.615 22.465 15.23 21.595 14.13Z"
            fill="currentColor"
          />
          <path
            d="M7.76995 3.645C7.78495 3.695 7.79995 3.745 7.81495 3.795C7.85995 3.925 7.91495 4.05 7.97995 4.165C8.02995 4.255 8.08995 4.34 8.15495 4.42C8.23495 4.52 8.32495 4.61 8.41995 4.69C8.53495 4.79 8.66495 4.875 8.79995 4.945C8.86995 4.98 8.93995 5.01 9.01495 5.04C9.08995 5.07 9.16495 5.09 9.24495 5.11C9.28495 5.12 9.32495 5.125 9.36495 5.135C9.47495 5.155 9.58495 5.17 9.69995 5.17C10.05 5.17 10.375 5.07 10.665 4.915C10.735 4.875 10.805 4.835 10.87 4.79C11.37 4.425 11.7 3.84 11.7 3.175C11.7 2.625 11.475 2.125 11.115 1.76C11.025 1.67 10.925 1.59 10.82 1.515C10.715 1.445 10.6 1.38 10.48 1.33C10.24 1.23 9.97995 1.175 9.69995 1.175C9.58495 1.175 9.47495 1.19 9.36495 1.21C9.32495 1.215 9.28495 1.225 9.24495 1.235C9.16495 1.255 9.08995 1.28 9.01495 1.305C8.83995 1.37 8.67995 1.45 8.53495 1.56C8.44995 1.62 8.36995 1.685 8.29495 1.755C8.16995 1.88 8.06495 2.025 7.97495 2.18C7.90495 2.3 7.84995 2.42 7.80995 2.555C7.79495 2.605 7.77495 2.65 7.76495 2.7C7.75495 2.745 7.74495 2.79 7.73495 2.84C7.71495 2.95 7.69995 3.065 7.69995 3.18C7.69995 3.295 7.71495 3.41 7.73495 3.52C7.74495 3.565 7.75495 3.615 7.76495 3.66L7.76995 3.645Z"
            fill="currentColor"
          />
          <path
            d="M16.5351 12.925C17.7001 12.925 18.7701 13.315 19.6401 13.965L20.5301 13.075C19.4301 12.205 18.0451 11.68 16.5351 11.68C12.9751 11.68 10.0801 14.575 10.0801 18.135C10.0801 19.37 10.4351 20.525 11.0401 21.505C11.1751 21.72 11.3201 21.93 11.4751 22.13C11.7251 22.445 12.0051 22.74 12.3101 23.005H14.7151C14.2501 22.83 13.8151 22.595 13.4251 22.3C13.1151 22.065 12.8251 21.8 12.5751 21.505C12.5001 21.42 12.4351 21.33 12.3651 21.24C11.7151 20.37 11.3251 19.3 11.3251 18.135C11.3251 15.265 13.6601 12.93 16.5301 12.93L16.5351 12.925Z"
            fill="currentColor"
          />
          <path
            d="M16.4199 4.01C15.6149 5.03 13.1749 6.13 9.70492 6.13C6.23492 6.13 3.79492 5.03 2.98992 4.01L1.41992 5.245C2.38492 6.47 4.26992 7.39 6.56492 7.835L6.98992 22.99H8.98992V20.62C8.71992 19.815 8.57992 18.975 8.57992 18.125C8.57992 15.085 10.2949 12.44 12.8099 11.1L12.9899 7.805C15.2199 7.35 17.0449 6.445 17.9899 5.245L16.4199 4.01Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </div>
  );
};
export const MenuRecordIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g transform="translate(8, 7)">
          <path
            d="M19.1967 13.25V16.415H22.3617V18.58H19.1967V21.745H17.0317V18.58H13.8667V16.415H17.0317V13.25H19.1967ZM20.4467 12H15.7817V15.165H12.6167V19.83H15.7817V22.995H20.4467V19.83H23.6117V15.165H20.4467V12Z"
            fill="currentColor"
          />
          <path
            d="M3.43164 17.02C3.29164 17.02 3.18164 16.91 3.18164 16.77V2.77C3.18164 2.63 3.29164 2.52 3.43164 2.52H17.4316C17.5716 2.52 17.6816 2.63 17.6816 2.77V10.5H19.1816V2.77C19.1816 1.805 18.3966 1.02 17.4316 1.02H3.43164C2.46664 1.02 1.68164 1.805 1.68164 2.77V16.77C1.68164 17.735 2.46664 18.52 3.43164 18.52H11.1166V17.02H3.43164Z"
            fill="currentColor"
          />
          <path d="M18.4316 5.655H2.43164V7.155H18.4316V5.655Z" fill="currentColor" />
          <path d="M8.29678 9.415H6.57178V10.915H8.29678V9.415Z" fill="currentColor" />
          <path d="M11.2917 9.415H9.56665V10.915H11.2917V9.415Z" fill="currentColor" />
          <path d="M14.2868 9.415H12.5618V10.915H14.2868V9.415Z" fill="currentColor" />
          <path d="M8.29678 13.065H6.57178V14.565H8.29678V13.065Z" fill="currentColor" />
          <path d="M11.2917 13.065H9.56665V14.565H11.2917V13.065Z" fill="currentColor" />
        </g>
      </svg>
    </div>
  );
};
export const MenuEventIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g transform="translate(9, 6)">
          <path
            d="M18.5116 11.605C18.5116 11.545 18.5016 11.485 18.4866 11.43C18.4866 11.42 18.4816 11.415 18.4766 11.405C18.4516 11.325 18.4216 11.25 18.3716 11.18C18.3716 11.18 18.3716 11.18 18.3716 11.175C18.3216 11.105 18.2616 11.045 18.1916 10.995C18.1916 10.995 18.1866 10.995 18.1816 10.99C18.1116 10.94 18.0316 10.91 17.9516 10.885C17.9466 10.885 17.9416 10.88 17.9366 10.88C17.8916 10.87 13.4766 9.76 10.8366 6.705L15.0666 4.745C15.3316 4.62 15.5016 4.355 15.5016 4.065C15.5016 3.775 15.3316 3.505 15.0666 3.385L9.97664 1.025C9.74664 0.915 9.47164 0.935 9.25664 1.075C9.04164 1.215 8.91164 1.45 8.91164 1.705V6.18C6.44664 9.515 1.78664 10.78 1.41164 10.88C1.06664 10.945 0.806641 11.25 0.806641 11.615C0.806641 13.395 2.16664 14.865 3.90164 15.045L1.90664 22.41H17.2866L15.2966 15.055C17.0866 14.93 18.5066 13.44 18.5066 11.615L18.5116 11.605ZM10.4166 2.87L12.9716 4.055L10.4166 5.24V2.87ZM9.66664 7.64C10.8916 9.075 12.4316 10.12 13.8266 10.855H5.50664C6.90164 10.12 8.43664 9.08 9.66664 7.64ZM13.5066 14.675C12.2966 15.295 10.9616 15.625 9.60664 15.625C8.25164 15.625 6.96164 15.305 5.76664 14.705C6.23664 14.475 6.64664 14.15 6.96664 13.745C7.60164 14.54 8.57164 15.055 9.66664 15.055C10.7616 15.055 11.7366 14.54 12.3666 13.745C12.6766 14.13 13.0616 14.45 13.5066 14.675ZM7.86164 12.355H11.4616C11.1666 13.06 10.4716 13.555 9.66164 13.555C8.85164 13.555 8.15664 13.06 7.86164 12.355ZM2.46164 12.355H6.06164C5.76664 13.06 5.07164 13.555 4.26164 13.555C3.45164 13.555 2.75664 13.06 2.46164 12.355ZM3.87164 20.9L5.17164 16.095C7.93164 17.45 11.2716 17.45 14.0316 16.095L15.3316 20.9H3.87164ZM15.0616 13.555C14.2516 13.555 13.5566 13.06 13.2616 12.355H16.8616C16.5666 13.06 15.8716 13.555 15.0616 13.555Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </div>
  );
};
export const MenuCouponIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <path
          d="M16.1832 11.24V11.99C16.1832 12.87 15.4682 13.585 14.5882 13.585C13.7082 13.585 12.9932 12.87 12.9932 11.99V11.24H8.11816V24.765H12.9882V24.015C12.9882 23.135 13.7032 22.42 14.5832 22.42C15.4632 22.42 16.1782 23.135 16.1782 24.015V24.765H28.5432V11.24H16.1782H16.1832ZM27.0482 23.26H17.5882C17.3132 22.155 16.4382 21.295 15.3332 21.02V19.89H13.8332V21.02C12.7282 21.295 11.8532 22.16 11.5782 23.26H9.61316V12.74H11.5782C11.8532 13.845 12.7282 14.705 13.8332 14.985V16.115H15.3332V14.985C16.4382 14.71 17.3132 13.845 17.5882 12.74H27.0482V23.265V23.26Z"
          fill="currentColor"
        />
        <path d="M15.3381 16.95H13.8381V19.05H15.3381V16.95Z" fill="currentColor" />
      </svg>
    </div>
  );
};
export const MenuResultIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g clip-path="url(#clip0_80895_9349)">
          <path
            d="M71.4599 -37.37H2.80493V36.07H71.4599V-37.37Z"
            stroke="white"
            stroke-miterlimit="10"
          />
          <path
            d="M26.6201 28.81H10.1201V8.81H14.2851V10.31H11.6201V27.31H25.1201V10.31H22.4551V8.81H26.6201V28.81Z"
            fill="currentColor"
          />
          <path d="M23.8701 21.34H12.8701V22.84H23.8701V21.34Z" fill="currentColor" />
          <path d="M19.8701 24.355H12.8701V25.855H19.8701V24.355Z" fill="currentColor" />
          <path
            d="M21.3701 15.335H19.3701V13.335H17.3701V15.335H15.3701V17.335H17.3701V19.335H19.3701V17.335H21.3701V15.335Z"
            fill="currentColor"
          />
          <path
            d="M23.0802 11.94H13.6602V7.19H23.0752V11.94H23.0802ZM14.9102 10.69H21.8252V8.44H14.9102V10.69Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </div>
  );
};
export const MenuMedicationIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <path
          d="M14.6232 27.46C13.1532 27.46 11.6782 26.9 10.5582 25.78C8.31817 23.54 8.31817 19.89 10.5582 17.65L17.9832 10.225C20.2232 7.98 23.8732 7.985 26.1132 10.225C28.3532 12.465 28.3532 16.115 26.1132 18.355L18.6882 25.78C17.5682 26.9 16.0932 27.46 14.6232 27.46ZM22.0432 10.04C20.9082 10.04 19.8432 10.48 19.0382 11.285L11.6132 18.71C10.8082 19.515 10.3682 20.58 10.3682 21.715C10.3682 22.85 10.8082 23.92 11.6132 24.72C12.4182 25.52 13.4832 25.965 14.6182 25.965C15.7532 25.965 16.8182 25.525 17.6232 24.72L25.0482 17.295C25.8532 16.49 26.2932 15.425 26.2932 14.29C26.2932 13.155 25.8532 12.085 25.0482 11.285C24.2432 10.485 23.1782 10.04 22.0432 10.04Z"
          fill="currentColor"
        />
        <path
          d="M15.3287 13.9341L14.2681 14.9948L21.3391 22.0659L22.3998 21.0052L15.3287 13.9341Z"
          fill="currentColor"
        />
      </svg>
    </div>
  );
};
export const MenuUserSettingIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g clipPath="url(#clip0_80895_9382)">
          {/* 头像外圆 */}
          <path
            d="M18 8.875C20.205 8.875 22 10.67 22 12.875C22 15.08 20.205 16.875 18 16.875C15.795 16.875 14 15.08 14 12.875C14 10.67 15.795 8.875 18 8.875ZM18 7.375C14.96 7.375 12.5 9.835 12.5 12.875C12.5 15.915 14.96 18.375 18 18.375C21.04 18.375 23.5 15.915 23.5 12.875C23.5 9.835 21.04 7.375 18 7.375Z"
            fill="currentColor"
          />
          {/* 身体/肩膀 */}
          <path
            d="M15.72 19.875C11.05 19.875 7.25 23.8 7.25 28.625H8.75C8.75 24.625 11.875 21.375 15.72 21.375H17.35L18.215 19.875H15.72Z"
            fill="currentColor"
          />
          {/* 齿轮外框 */}
          <path
            d="M28.7451 28.56L28.7051 28.63H28.7451C28.7451 28.63 28.7451 28.585 28.7451 28.56Z"
            fill="currentColor"
          />
          <path
            d="M25.2001 29.02H22.2501L21.8551 27.84C21.6051 27.725 21.3701 27.59 21.1451 27.435L19.9301 27.685L18.4551 25.13L19.2801 24.2C19.2651 24.065 19.2601 23.925 19.2601 23.79C19.2601 23.655 19.2651 23.525 19.2801 23.38L18.4551 22.45L19.9301 19.895L21.1451 20.145C21.3651 19.99 21.6051 19.85 21.8551 19.735L22.2501 18.555H25.2001L25.5951 19.735C25.8451 19.85 26.0851 19.985 26.3051 20.145L27.5201 19.895L28.9951 22.45L28.1701 23.38C28.1851 23.52 28.1901 23.655 28.1901 23.79C28.1901 23.925 28.1851 24.055 28.1701 24.2L28.9951 25.13L27.5201 27.685L26.3051 27.435C26.0851 27.59 25.8451 27.725 25.5951 27.84L25.2001 29.02ZM23.1551 27.77H24.3001L24.5901 26.895L24.8651 26.79C25.1951 26.665 25.4901 26.495 25.7551 26.28L25.9851 26.09L26.8851 26.275L27.4601 25.28L26.8401 24.58L26.9001 24.27C26.9251 24.105 26.9401 23.94 26.9401 23.785C26.9401 23.63 26.9251 23.465 26.8951 23.28L26.8451 22.965L27.0751 22.73L27.4651 22.29L26.8901 21.295L25.9901 21.48L25.7601 21.29C25.5001 21.075 25.2001 20.905 24.8751 20.78L24.6001 20.675L24.3101 19.8H23.1651L22.8751 20.675L22.6001 20.78C22.2751 20.905 21.9751 21.075 21.7101 21.29L21.4801 21.48L20.5801 21.295L20.0051 22.285L20.5951 22.95L20.5751 23.225C20.5401 23.46 20.5251 23.625 20.5251 23.78C20.5251 23.935 20.5401 24.1 20.5701 24.285L20.6151 24.575L20.0051 25.27L20.5801 26.265L21.4801 26.08L21.7101 26.27C21.9701 26.485 22.2701 26.655 22.6001 26.78L22.8751 26.885L23.1651 27.76L23.1551 27.77Z"
            fill="currentColor"
          />
          {/* 齿轮中心圆 */}
          <path
            d="M23.73 25.54C24.6965 25.54 25.48 24.7565 25.48 23.79C25.48 22.8235 24.6965 22.04 23.73 22.04C22.7635 22.04 21.98 22.8235 21.98 23.79C21.98 24.7565 22.7635 25.54 23.73 25.54Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_80895_9382">
            <rect width="36" height="36" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
};
export const MenuWaklingEventIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <path
          d="M20.4201 28.95H18.5451C17.5351 28.95 16.7151 28.125 16.7151 27.105V26.43C16.7151 25.415 17.5351 24.585 18.5451 24.585H18.6001C18.9801 24.585 19.3201 24.355 19.4601 23.995L21.3751 19.185L22.0301 19.675C22.1851 19.79 23.0301 20.325 23.8401 19.45C24.2901 18.96 24.2651 18.835 24.2301 18.63C24.1501 18.2 24.1201 17.715 24.6751 16.955L25.1101 16.365L28.4201 19.705C29.1501 20.44 29.1501 21.635 28.4201 22.37L22.9451 27.895C22.2701 28.575 21.3751 28.95 20.4251 28.95H20.4201ZM18.5451 25.835C18.2251 25.835 17.9651 26.1 17.9651 26.43V27.105C17.9651 27.435 18.2251 27.7 18.5451 27.7H20.4201C21.0351 27.7 21.6151 27.455 22.0551 27.015L27.5301 21.49C27.7751 21.24 27.7751 20.835 27.5301 20.585L25.4751 18.51C25.5451 18.975 25.5051 19.485 24.7601 20.295C23.8201 21.315 22.7451 21.305 21.9851 21.04L20.6251 24.455C20.2901 25.29 19.5001 25.83 18.6051 25.83H18.5501L18.5451 25.835Z"
          fill="currentColor"
        />
        <path
          d="M17.35 27.385L17.325 26.135L20.25 26.08C20.505 26.08 20.745 25.97 20.93 25.79L27.2 19.485L28.085 20.365L21.815 26.665C21.405 27.08 20.855 27.315 20.275 27.325L17.355 27.38L17.35 27.385Z"
          fill="currentColor"
        />
        <path
          d="M16.7201 18.745V19.435C18.5651 19.175 20.1051 17.985 20.8251 16.33C20.6551 16.305 20.4801 16.265 20.3001 16.21L20.7451 14.78C20.8951 14.825 21.0551 14.86 21.2201 14.865C21.2201 14.835 21.2301 14.81 21.2301 14.785L21.6151 11.075H21.1451V9.57501H21.7701L22.0001 7.36501H9.93506L10.1651 9.57501H10.7901V11.075H10.3201L10.7051 14.785C10.7051 14.815 10.7151 14.84 10.7151 14.865C10.8801 14.865 11.0401 14.83 11.1901 14.78L11.6351 16.21C11.4551 16.265 11.2801 16.305 11.1101 16.33C11.8301 17.985 13.3751 19.175 15.2151 19.435V18.745H16.7151H16.7201ZM12.1951 14.63L11.6001 8.86501H20.3401L19.7451 14.63C19.5451 16.55 17.9251 17.995 15.9701 17.995C14.0151 17.995 12.3951 16.55 12.1951 14.63Z"
          fill="currentColor"
        />
        <path
          d="M22.885 11.075L22.695 12.905C22.655 13.32 22.55 13.69 22.39 14.005C22.035 14.7 21.615 14.875 21.22 14.87C21.16 15.39 21.02 15.88 20.825 16.34C20.97 16.36 21.115 16.375 21.26 16.375C22.3 16.375 23.17 15.79 23.73 14.69C23.975 14.21 24.13 13.665 24.19 13.065L24.55 9.58H21.775L21.62 11.08H22.89L22.885 11.075Z"
          fill="currentColor"
        />
        <path
          d="M20.745 14.785L20.3 16.215C20.48 16.27 20.655 16.31 20.825 16.335C21.025 15.875 21.16 15.385 21.22 14.865C21.055 14.865 20.895 14.83 20.745 14.78V14.785Z"
          fill="currentColor"
        />
        <path d="M21.145 11.075H21.615L21.77 9.575H21.145V11.075Z" fill="currentColor" />
        <path
          d="M9.54001 14.005C9.38001 13.695 9.28001 13.325 9.23501 12.905L9.04501 11.075H10.315L10.16 9.575H7.38501L7.74501 13.06C7.80501 13.66 7.96001 14.21 8.20501 14.685C8.76501 15.785 9.64001 16.37 10.675 16.37C10.815 16.37 10.96 16.355 11.11 16.335C10.91 15.875 10.775 15.385 10.715 14.865C10.32 14.875 9.90001 14.7 9.54501 14L9.54001 14.005Z"
          fill="currentColor"
        />
        <path
          d="M11.1901 14.785C11.0401 14.83 10.8801 14.865 10.7151 14.87C10.7751 15.39 10.9151 15.88 11.1101 16.34C11.2801 16.315 11.4551 16.275 11.6351 16.22L11.1901 14.79V14.785Z"
          fill="currentColor"
        />
        <path d="M10.79 11.075V9.575H10.165L10.32 11.075H10.79Z" fill="currentColor" />
        <path
          d="M18.2 23.1L18.225 23.04H13.38V22.635C13.38 22.305 13.65 22.04 13.975 22.04H18.18C18.33 22.04 18.46 22.1 18.565 22.19L19.13 20.775C18.845 20.63 18.525 20.54 18.185 20.54H16.72V21.43H15.22V20.54H13.98C12.825 20.54 11.885 21.48 11.885 22.635V24.54H15.805C16.345 23.75 17.21 23.205 18.205 23.1H18.2Z"
          fill="currentColor"
        />
        <path
          d="M16.72 19.44C16.475 19.475 16.225 19.5 15.97 19.5C15.715 19.5 15.465 19.475 15.22 19.44V20.54H16.72V19.44Z"
          fill="currentColor"
        />
        <path
          d="M16.72 19.44V18.75H15.22V19.44C15.465 19.475 15.715 19.5 15.97 19.5C16.225 19.5 16.475 19.475 16.72 19.44Z"
          fill="currentColor"
        />
        <path d="M16.72 20.54H15.22V21.43H16.72V20.54Z" fill="currentColor" />
      </svg>
    </div>
  );
};
export const MenuPhotoIcon: React.FC<SvgIconProps> = ({
  size = 36,
  className = 'text-primary',
  fill = 'bg-primary-5 rounded-full',
}) => {
  return (
    <div className={fill}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <path
          d="M17.3868 18.745V19.365H17.7968C19.4568 19 20.8268 17.865 21.4918 16.335C21.3218 16.31 21.1468 16.27 20.9668 16.215L21.4118 14.785C21.5618 14.83 21.7218 14.865 21.8868 14.87C21.8868 14.84 21.8968 14.815 21.8968 14.79L22.2818 11.08H21.8118V9.58H22.4368L22.6668 7.37H10.6018L10.8318 9.58H11.4568V11.08H10.9868L11.3718 14.79C11.3718 14.82 11.3818 14.845 11.3818 14.87C11.5468 14.87 11.7068 14.835 11.8568 14.785L12.3018 16.215C12.1218 16.27 11.9468 16.31 11.7768 16.335C12.4968 17.99 14.0418 19.18 15.8818 19.44V18.75H17.3818L17.3868 18.745ZM12.8618 14.63L12.2668 8.865H21.0068L20.4118 14.63C20.2118 16.55 18.5918 17.995 16.6368 17.995C14.6818 17.995 13.0618 16.55 12.8618 14.63Z"
          fill="currentColor"
        />
        <path
          d="M23.5517 11.075L23.3617 12.905C23.3217 13.32 23.2167 13.69 23.0567 14.005C22.7017 14.7 22.2817 14.875 21.8867 14.87C21.8267 15.39 21.6867 15.88 21.4917 16.34C21.6367 16.36 21.7817 16.375 21.9267 16.375C22.9667 16.375 23.8367 15.79 24.3967 14.69C24.6417 14.21 24.7967 13.665 24.8567 13.065L25.2167 9.58H22.4417L22.2867 11.08H23.5567L23.5517 11.075Z"
          fill="currentColor"
        />
        <path d="M21.8118 11.075H22.2818L22.4368 9.575H21.8118V11.075Z" fill="currentColor" />
        <path
          d="M21.4118 14.785L20.9668 16.215C21.1468 16.27 21.3218 16.31 21.4918 16.335C21.6918 15.875 21.8268 15.385 21.8868 14.865C21.7218 14.865 21.5618 14.83 21.4118 14.78V14.785Z"
          fill="currentColor"
        />
        <path
          d="M10.2068 14.005C10.0468 13.695 9.94676 13.325 9.90176 12.905L9.71176 11.075H10.9818L10.8268 9.575H8.05176L8.41176 13.06C8.47176 13.66 8.62676 14.21 8.87176 14.685C9.43176 15.785 10.3068 16.37 11.3418 16.37C11.4818 16.37 11.6268 16.355 11.7768 16.335C11.5768 15.875 11.4418 15.385 11.3818 14.865C10.9868 14.875 10.5668 14.7 10.2118 14L10.2068 14.005Z"
          fill="currentColor"
        />
        <path d="M11.4568 11.075V9.575H10.8318L10.9868 11.075H11.4568Z" fill="currentColor" />
        <path
          d="M11.8568 14.785C11.7068 14.83 11.5468 14.865 11.3818 14.87C11.4418 15.39 11.5818 15.88 11.7768 16.34C11.9468 16.315 12.1218 16.275 12.3018 16.22L11.8568 14.79V14.785Z"
          fill="currentColor"
        />
        <path
          d="M14.0466 23.04V22.635C14.0466 22.305 14.3166 22.04 14.6416 22.04H17.2016V21.43H15.8816V20.54H14.6416C13.4866 20.54 12.5466 21.48 12.5466 22.635V24.54H17.2016V23.04H14.0466Z"
          fill="currentColor"
        />
        <path
          d="M17.2018 19.465C17.0168 19.485 16.8268 19.495 16.6318 19.495C16.3768 19.495 16.1268 19.47 15.8818 19.435V20.535H17.2018V19.46V19.465Z"
          fill="currentColor"
        />
        <path
          d="M17.2018 19.465V19.365H17.3818V18.745H15.8818V19.435C16.1268 19.47 16.3768 19.495 16.6318 19.495C16.8218 19.495 17.0118 19.48 17.2018 19.465Z"
          fill="currentColor"
        />
        <path d="M17.2067 20.54H15.8867V21.43H17.2067V20.54Z" fill="currentColor" />
        <path
          d="M17.3867 19.44C17.5267 19.42 17.6617 19.395 17.7967 19.365H17.3867V19.44Z"
          fill="currentColor"
        />
        <path
          d="M17.2017 19.365V19.465C17.2617 19.46 17.3217 19.45 17.3817 19.44V19.365H17.2017Z"
          fill="currentColor"
        />
        <path
          d="M29.6317 28.965H18.7017V20.865H21.5067L21.9767 19.45H26.3567L26.8267 20.865H29.6317V28.965ZM20.2017 27.465H28.1317V22.365H25.7467L25.2767 20.95H23.0567L22.5867 22.365H20.2017V27.465Z"
          fill="currentColor"
        />
        <path
          d="M24.1667 26.8C25.2078 26.8 26.0517 25.9561 26.0517 24.915C26.0517 23.8739 25.2078 23.03 24.1667 23.03C23.1257 23.03 22.2817 23.8739 22.2817 24.915C22.2817 25.9561 23.1257 26.8 24.1667 26.8Z"
          fill="currentColor"
        />
      </svg>
    </div>
  );
};
// export const BarChartRankingIcon: React.FC<SvgIconProps> = (props) => {
//   return <BarChartIcon {...props} />;
// };
// export const ScoreIcon: React.FC<SvgIconProps> = (props) => {
//   return <BarChartScoreIcon {...props} />;
// };
