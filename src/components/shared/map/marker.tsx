import { getMarkerPosition } from '@/hooks/use-map';
import { type Marker, MarkerType } from '@/types/map';
import type { Marker as GoogleMapsMarker } from '@googlemaps/markerclusterer';
import { AdvancedMarker } from '@vis.gl/react-google-maps';
import { motion } from 'motion/react';
import { useTheme } from 'next-themes';
import { useCallback } from 'react';
import { THEME_COLOR, Theme } from './color';

export type HealthMarkerProps = {
  marker: Marker;
  onClick: (healthMarker: Marker) => void;
  setMarkerRef?: (marker: GoogleMapsMarker | null, key: string) => void;
};

export default function HealthMarkerComponent({
  marker: healthMarker,
  onClick,
  setMarkerRef,
}: HealthMarkerProps) {
  const { theme } = useTheme();
  const handleClick = useCallback(() => {
    onClick(healthMarker);
  }, [onClick, healthMarker]);

  const ref = useCallback(
    (marker: google.maps.marker.AdvancedMarkerElement) =>
      setMarkerRef?.(marker, String(healthMarker.id)),
    [setMarkerRef, healthMarker.id],
  );

  const size = healthMarker.isCurrent ? 48 : 36;

  return (
    <AdvancedMarker position={getMarkerPosition(healthMarker)} onClick={handleClick} ref={ref}>
      {healthMarker.type === MarkerType.WALKING_COURSE && (
        <WalkingCourseMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
      {healthMarker.type === MarkerType.EVENT && (
        <EventMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
      {healthMarker.type === MarkerType.COUPON && (
        <CouponMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
      {healthMarker.type === MarkerType.PHOTO && (
        <PhotoMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
      {healthMarker.type === MarkerType.POST && (
        <PostMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
    </AdvancedMarker>
  );
}

interface MarkerProps {
  theme?: string;
  size?: number;
  isFav?: boolean;
}

function WalkingCourseMarker({ theme = Theme.BLUE, size = 36, isFav }: MarkerProps) {
  const fill = THEME_COLOR[theme].walkingCourse;
  return (
    <MarkerComponent size={size} fill={fill} isFav={isFav}>
      <path
        d="M30.0523 25.9521H13.9487C13.431 25.9521 13.0112 26.375 13.0112 26.8965V29.6756C13.0112 30.1972 13.431 30.62 13.9487 30.62H30.0523C30.5701 30.62 30.9898 30.1972 30.9898 29.6756V26.8965C30.9898 26.375 30.5701 25.9521 30.0523 25.9521Z"
        fill="#FF945F"
      />
      <path
        d="M18.2882 22.0449H25.5793C26.3775 22.0449 27.0204 22.6979 27.0204 23.4965V24.7593H16.8472V23.4965C16.8472 22.6925 17.4954 22.0449 18.2882 22.0449Z"
        fill="#FF945F"
      />
      <path
        d="M24.6569 14.3554C24.8069 14.0047 24.8926 13.6161 24.8926 13.206C24.8926 11.5979 23.5962 10.292 21.9998 10.292C20.4034 10.292 19.1069 11.5979 19.1069 13.206C19.1069 13.6161 19.1926 14.0047 19.3426 14.3554C19.3426 14.3554 20.5855 17.3018 20.5855 23.2378H23.2105C23.2105 23.2378 23.0444 18.5214 24.6569 14.3554Z"
        fill="#FF945F"
      />
    </MarkerComponent>
  );
}

function EventMarker({ theme = Theme.BLUE, size = 36 }: MarkerProps) {
  const fill = THEME_COLOR[theme].event;
  return (
    <MarkerComponent size={size} fill={fill}>
      <path
        d="M30.6729 20.2812C30.6729 21.8894 29.3765 23.1953 27.7801 23.1953C26.1836 23.1953 24.8872 21.8894 24.8872 20.2812"
        fill="#15B47F"
      />
      <path
        d="M19.1016 20.2812C19.1016 21.8894 17.8052 23.1953 16.2088 23.1953C14.6123 23.1953 13.3159 21.8894 13.3159 20.2812"
        fill="#15B47F"
      />
      <path
        d="M24.8873 20.2812C24.8873 21.8894 23.5908 23.1953 21.9944 23.1953C20.398 23.1953 19.1016 21.8894 19.1016 20.2812"
        fill="#15B47F"
      />
      <path
        d="M21.9999 14.6797C19.0909 18.8726 13.3267 20.2757 13.3267 20.2757H30.6784C30.6784 20.2757 24.9142 18.8726 22.0052 14.6797H21.9999Z"
        fill="#15B47F"
      />
      <path d="M22 14.6796V9.59082L27.4536 12.1325L22 14.6796Z" fill="#15B47F" />
      <path
        d="M21.9307 25.1808C19.7718 25.1808 17.7575 24.5548 16.0485 23.4863L13.8789 31.5485H29.9825L27.8128 23.4863C26.0985 24.5548 24.0896 25.1808 21.9307 25.1808Z"
        fill="#15B47F"
      />
    </MarkerComponent>
  );
}

function CouponMarker({ theme = Theme.BLUE, size = 36 }: MarkerProps) {
  const fill = THEME_COLOR[theme].coupon;
  return (
    <MarkerComponent size={size} fill={fill}>
      <path
        d="M20.4252 15.1572C20.4252 16.4362 19.5252 17.5046 18.3305 17.7529V18.3788H17.2591V17.7529C16.0645 17.5046 15.1645 16.4362 15.1645 15.1572H11.3823V28.7452H15.1645C15.1645 27.4663 16.0645 26.3978 17.2591 26.1496V25.5236H18.3305V26.1496C19.5252 26.3978 20.4252 27.4663 20.4252 28.7452H32.618V15.1572H20.4252ZM18.3305 24.5037H17.2591V22.4585H18.3305V24.5037ZM18.3305 21.4386H17.2591V19.3934H18.3305V21.4386Z"
        fill="#FC7DBF"
      />
    </MarkerComponent>
  );
}

function PhotoMarker({ theme = Theme.BLUE, size = 36 }: MarkerProps) {
  const fill = THEME_COLOR[theme].photo;
  return (
    <MarkerComponent size={size} fill={fill}>
      <path
        d="M21.855 26.4326C23.8991 26.4326 25.5561 24.7633 25.5561 22.7041C25.5561 20.6449 23.8991 18.9756 21.855 18.9756C19.8109 18.9756 18.1538 20.6449 18.1538 22.7041C18.1538 24.7633 19.8109 26.4326 21.855 26.4326Z"
        fill="#4457D1"
      />
      <path
        d="M28.379 15.9592L27.2274 12.1768H16.4828L15.3312 15.9592H11.6729V29.724H32.0373V15.9592H28.379ZM21.8551 27.7653C19.0805 27.7653 16.8256 25.4991 16.8256 22.6986C16.8256 19.8982 19.0752 17.6319 21.8551 17.6319C24.635 17.6319 26.8846 19.8982 26.8846 22.6986C26.8846 25.4991 24.635 27.7653 21.8551 27.7653ZM30.093 19.5475H27.8702V17.9287H30.093V19.5475Z"
        fill="#4457D1"
      />
    </MarkerComponent>
  );
}

function PostMarker({ theme = Theme.BLUE, size = 36 }: MarkerProps) {
  const fill = THEME_COLOR[theme].photo;
  return (
    <svg
      width="103"
      height="56"
      viewBox="0 0 103 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <ellipse cx="52.5469" cy="55" rx="3" ry="1" fill="#4457D1" />
      <line x1="52.5469" y1="26" x2="52.5469" y2="46" stroke="#4457D1" stroke-width="2" />
      <g filter="url(#filter0_d_65893_114112)">
        <path
          d="M2.54688 14.5C2.54688 7.59644 8.14332 2 15.0469 2H88.0469C94.9504 2 100.547 7.59644 100.547 14.5C100.547 21.4036 94.9504 27 88.0469 27H15.0469C8.14332 27 2.54688 21.4036 2.54688 14.5Z"
          fill="#F6F8FF"
          shape-rendering="crispEdges"
        />
        <path
          d="M15.0469 2.5H88.0469C94.6743 2.5 100.047 7.87258 100.047 14.5C100.047 21.1274 94.6743 26.5 88.0469 26.5H15.0469C8.41946 26.5 3.04688 21.1274 3.04688 14.5C3.04688 7.87258 8.41946 2.5 15.0469 2.5Z"
          stroke="#4457D1"
          shape-rendering="crispEdges"
        />
        <path
          d="M14.5401 15.4903C13.3534 15.4903 12.3867 14.5236 12.3867 13.3369C12.3867 12.1503 13.3534 11.1836 14.5401 11.1836C15.7267 11.1836 16.6934 12.1503 16.6934 13.3369C16.6934 14.5236 15.7267 15.4903 14.5401 15.4903ZM14.5401 12.1836C13.9034 12.1836 13.3867 12.7003 13.3867 13.3369C13.3867 13.9736 13.9034 14.4903 14.5401 14.4903C15.1767 14.4903 15.6934 13.9736 15.6934 13.3369C15.6934 12.7003 15.1767 12.1836 14.5401 12.1836Z"
          fill="#4457D1"
        />
        <path
          d="M14.5409 20.8935L10.8442 16.6468C10.0276 15.7368 9.57422 14.5601 9.57422 13.3368C9.57422 10.5968 11.8009 8.37012 14.5409 8.37012C17.2809 8.37012 19.5076 10.5968 19.5076 13.3368C19.5076 14.5568 19.0542 15.7335 18.2342 16.6501L14.5409 20.8935ZM14.5409 9.37012C12.3542 9.37012 10.5742 11.1501 10.5742 13.3368C10.5742 14.3135 10.9376 15.2535 11.5942 15.9835L14.5409 19.3701L17.4876 15.9835C18.1442 15.2535 18.5076 14.3101 18.5076 13.3368C18.5076 11.1501 16.7276 9.37012 14.5409 9.37012Z"
          fill="#4457D1"
        />
        <path
          d="M25.5989 8.822C26.1122 8.878 26.6909 8.92 27.3349 8.948C27.9789 8.976 28.6695 8.99 29.4069 8.99C29.8642 8.99 30.3355 8.98067 30.8209 8.962C31.3155 8.94333 31.7915 8.92 32.2489 8.892C32.7062 8.864 33.1122 8.83133 33.4669 8.794V10.656C33.1402 10.684 32.7435 10.712 32.2769 10.74C31.8195 10.768 31.3389 10.7913 30.8349 10.81C30.3402 10.8193 29.8689 10.824 29.4209 10.824C28.6929 10.824 28.0162 10.81 27.3909 10.782C26.7655 10.754 26.1682 10.7167 25.5989 10.67V8.822ZM26.7749 14.758C26.7095 15.0007 26.6535 15.2387 26.6069 15.472C26.5602 15.7053 26.5369 15.9387 26.5369 16.172C26.5369 16.62 26.7609 16.9887 27.2089 17.278C27.6662 17.5673 28.3989 17.712 29.4069 17.712C30.0415 17.712 30.6529 17.6887 31.2409 17.642C31.8289 17.5953 32.3842 17.53 32.9069 17.446C33.4389 17.362 33.9102 17.264 34.3209 17.152L34.3349 19.126C33.9242 19.2193 33.4669 19.2987 32.9629 19.364C32.4589 19.4387 31.9129 19.4947 31.3249 19.532C30.7462 19.5787 30.1302 19.602 29.4769 19.602C28.4129 19.602 27.5215 19.4853 26.8029 19.252C26.0842 19.0187 25.5475 18.6827 25.1929 18.244C24.8382 17.796 24.6609 17.25 24.6609 16.606C24.6609 16.1953 24.6935 15.822 24.7589 15.486C24.8242 15.15 24.8849 14.8513 24.9409 14.59L26.7749 14.758ZM39.5989 8.822C40.1122 8.878 40.6909 8.92 41.3349 8.948C41.9789 8.976 42.6695 8.99 43.4069 8.99C43.8642 8.99 44.3355 8.98067 44.8209 8.962C45.3155 8.94333 45.7915 8.92 46.2489 8.892C46.7062 8.864 47.1122 8.83133 47.4669 8.794V10.656C47.1402 10.684 46.7435 10.712 46.2769 10.74C45.8195 10.768 45.3389 10.7913 44.8349 10.81C44.3402 10.8193 43.8689 10.824 43.4209 10.824C42.6929 10.824 42.0162 10.81 41.3909 10.782C40.7655 10.754 40.1682 10.7167 39.5989 10.67V8.822ZM40.7749 14.758C40.7095 15.0007 40.6535 15.2387 40.6069 15.472C40.5602 15.7053 40.5369 15.9387 40.5369 16.172C40.5369 16.62 40.7609 16.9887 41.2089 17.278C41.6662 17.5673 42.3989 17.712 43.4069 17.712C44.0415 17.712 44.6529 17.6887 45.2409 17.642C45.8289 17.5953 46.3842 17.53 46.9069 17.446C47.4389 17.362 47.9102 17.264 48.3209 17.152L48.3349 19.126C47.9242 19.2193 47.4669 19.2987 46.9629 19.364C46.4589 19.4387 45.9129 19.4947 45.3249 19.532C44.7462 19.5787 44.1302 19.602 43.4769 19.602C42.4129 19.602 41.5215 19.4853 40.8029 19.252C40.0842 19.0187 39.5475 18.6827 39.1929 18.244C38.8382 17.796 38.6609 17.25 38.6609 16.606C38.6609 16.1953 38.6935 15.822 38.7589 15.486C38.8242 15.15 38.8849 14.8513 38.9409 14.59L40.7749 14.758ZM57.3509 7.772C57.3042 8.024 57.2295 8.35067 57.1269 8.752C57.0242 9.15333 56.8749 9.606 56.6789 10.11C56.5295 10.4553 56.3569 10.8147 56.1609 11.188C55.9742 11.552 55.7829 11.8787 55.5869 12.168C55.7082 12.112 55.8622 12.0653 56.0489 12.028C56.2355 11.9813 56.4269 11.9487 56.6229 11.93C56.8282 11.902 57.0102 11.888 57.1689 11.888C57.7569 11.888 58.2422 12.056 58.6249 12.392C59.0169 12.728 59.2129 13.2273 59.2129 13.89C59.2129 14.0767 59.2129 14.3053 59.2129 14.576C59.2222 14.8467 59.2315 15.1313 59.2409 15.43C59.2502 15.7193 59.2595 16.004 59.2689 16.284C59.2782 16.564 59.2829 16.816 59.2829 17.04H57.6029C57.6215 16.8813 57.6309 16.6947 57.6309 16.48C57.6402 16.256 57.6449 16.0227 57.6449 15.78C57.6542 15.5373 57.6589 15.304 57.6589 15.08C57.6682 14.8467 57.6729 14.6367 57.6729 14.45C57.6729 14.002 57.5515 13.694 57.3089 13.526C57.0755 13.3487 56.8142 13.26 56.5249 13.26C56.1329 13.26 55.7362 13.358 55.3349 13.554C54.9429 13.7407 54.6115 13.9647 54.3409 14.226C54.1355 14.4313 53.9255 14.66 53.7109 14.912C53.5055 15.164 53.2815 15.4487 53.0389 15.766L51.5409 14.646C52.1475 14.086 52.6702 13.5447 53.1089 13.022C53.5569 12.4993 53.9349 11.9813 54.2429 11.468C54.5509 10.9547 54.8029 10.4413 54.9989 9.928C55.1389 9.55467 55.2509 9.16733 55.3349 8.766C55.4282 8.35533 55.4842 7.97267 55.5029 7.618L57.3509 7.772ZM52.0869 9.06C52.4415 9.116 52.8522 9.158 53.3189 9.186C53.7855 9.214 54.1962 9.228 54.5509 9.228C55.1669 9.228 55.8389 9.214 56.5669 9.186C57.3042 9.158 58.0509 9.11133 58.8069 9.046C59.5629 8.97133 60.2815 8.878 60.9629 8.766L60.9489 10.39C60.4542 10.4553 59.9175 10.516 59.3389 10.572C58.7695 10.6187 58.1862 10.6607 57.5889 10.698C57.0009 10.726 56.4362 10.7493 55.8949 10.768C55.3535 10.7773 54.8729 10.782 54.4529 10.782C54.2662 10.782 54.0329 10.782 53.7529 10.782C53.4822 10.7727 53.2022 10.7633 52.9129 10.754C52.6235 10.7353 52.3482 10.7167 52.0869 10.698V9.06ZM63.1749 13.036C63.0255 13.0827 62.8482 13.1433 62.6429 13.218C62.4469 13.2833 62.2462 13.3533 62.0409 13.428C61.8449 13.4933 61.6675 13.5587 61.5089 13.624C61.0609 13.8013 60.5289 14.016 59.9129 14.268C59.3062 14.52 58.6715 14.814 58.0089 15.15C57.5889 15.374 57.2342 15.5933 56.9449 15.808C56.6555 16.0227 56.4362 16.242 56.2869 16.466C56.1375 16.6807 56.0629 16.9187 56.0629 17.18C56.0629 17.376 56.1049 17.5393 56.1889 17.67C56.2729 17.7913 56.3989 17.8893 56.5669 17.964C56.7349 18.0387 56.9449 18.09 57.1969 18.118C57.4489 18.146 57.7475 18.16 58.0929 18.16C58.6902 18.16 59.3482 18.1273 60.0669 18.062C60.7855 17.9873 61.4529 17.8893 62.0689 17.768L62.0129 19.574C61.7142 19.6113 61.3362 19.6487 60.8789 19.686C60.4215 19.7327 59.9455 19.7653 59.4509 19.784C58.9655 19.8027 58.4989 19.812 58.0509 19.812C57.3229 19.812 56.6695 19.742 56.0909 19.602C55.5122 19.462 55.0549 19.224 54.7189 18.888C54.3922 18.5427 54.2289 18.0713 54.2289 17.474C54.2289 16.9887 54.3362 16.55 54.5509 16.158C54.7749 15.766 55.0689 15.4113 55.4329 15.094C55.7969 14.7767 56.1935 14.492 56.6229 14.24C57.0615 13.9787 57.4909 13.7407 57.9109 13.526C58.3402 13.302 58.7275 13.1107 59.0729 12.952C59.4182 12.7933 59.7402 12.6533 60.0389 12.532C60.3469 12.4013 60.6409 12.2707 60.9209 12.14C61.1915 12.0187 61.4529 11.902 61.7049 11.79C61.9569 11.6687 62.2135 11.5427 62.4749 11.412L63.1749 13.036ZM68.0889 12.56V17.726H66.5209V14.114H65.0649V12.56H68.0889ZM68.0889 17.068C68.3595 17.544 68.7469 17.894 69.2509 18.118C69.7642 18.342 70.3802 18.468 71.0989 18.496C71.5375 18.5147 72.0602 18.5287 72.6669 18.538C73.2735 18.538 73.9129 18.5333 74.5849 18.524C75.2569 18.5147 75.9102 18.5007 76.5449 18.482C77.1889 18.454 77.7582 18.4167 78.2529 18.37C78.1969 18.5007 78.1315 18.6593 78.0569 18.846C77.9915 19.0233 77.9309 19.21 77.8749 19.406C77.8189 19.6113 77.7769 19.7933 77.7489 19.952C77.3009 19.9707 76.7875 19.9847 76.2089 19.994C75.6302 20.0127 75.0282 20.022 74.4029 20.022C73.7869 20.0313 73.1942 20.0313 72.6249 20.022C72.0555 20.0127 71.5469 19.9987 71.0989 19.98C70.2495 19.9427 69.5262 19.8073 68.9289 19.574C68.3409 19.3407 67.8322 18.9487 67.4029 18.398C67.1415 18.678 66.8662 18.958 66.5769 19.238C66.2969 19.518 65.9889 19.812 65.6529 20.12L64.8549 18.524C65.1442 18.3187 65.4475 18.09 65.7649 17.838C66.0822 17.586 66.3855 17.3293 66.6749 17.068H68.0889ZM64.9669 8.262L66.2969 7.436C66.5489 7.65067 66.8009 7.898 67.0529 8.178C67.3142 8.44867 67.5522 8.71933 67.7669 8.99C67.9815 9.26067 68.1495 9.51267 68.2709 9.746L66.8429 10.684C66.7402 10.46 66.5862 10.208 66.3809 9.928C66.1755 9.63867 65.9469 9.34933 65.6949 9.06C65.4522 8.77067 65.2095 8.50467 64.9669 8.262ZM68.9289 12.84H77.5249V14.058H68.9289V12.84ZM68.5229 14.996H77.9589V16.228H68.5229V14.996ZM70.8049 12.084H72.3729V15.598H70.8049V12.084ZM73.9969 12.084H75.5929V15.598H73.9969V12.084ZM69.3209 9.172H71.3649V8.598H68.6909V7.534H72.7789V10.25H69.3209V9.172ZM68.9009 9.172H70.2729V10.53C70.2729 10.6793 70.3009 10.7773 70.3569 10.824C70.4129 10.8613 70.5249 10.88 70.6929 10.88C70.7395 10.88 70.8095 10.88 70.9029 10.88C70.9962 10.88 71.0989 10.88 71.2109 10.88C71.3322 10.88 71.4442 10.88 71.5469 10.88C71.6495 10.88 71.7242 10.88 71.7709 10.88C71.9202 10.88 72.0182 10.8473 72.0649 10.782C72.1115 10.7167 72.1442 10.572 72.1629 10.348C72.2935 10.432 72.4709 10.516 72.6949 10.6C72.9282 10.6747 73.1429 10.7307 73.3389 10.768C73.2642 11.244 73.1195 11.566 72.9049 11.734C72.6995 11.902 72.3822 11.986 71.9529 11.986C71.8875 11.986 71.7849 11.986 71.6449 11.986C71.5142 11.986 71.3742 11.986 71.2249 11.986C71.0755 11.986 70.9309 11.986 70.7909 11.986C70.6602 11.986 70.5622 11.986 70.4969 11.986C70.0769 11.986 69.7502 11.9487 69.5169 11.874C69.2929 11.79 69.1342 11.6407 69.0409 11.426C68.9475 11.2113 68.9009 10.9173 68.9009 10.544V9.172ZM73.9129 9.172H75.9709V8.598H73.2549V7.534H77.3989V10.25H73.9129V9.172ZM73.5069 9.172H74.8789V10.53C74.8789 10.6793 74.9069 10.7773 74.9629 10.824C75.0189 10.8613 75.1355 10.88 75.3129 10.88C75.3595 10.88 75.4295 10.88 75.5229 10.88C75.6255 10.88 75.7375 10.88 75.8589 10.88C75.9802 10.88 76.0922 10.88 76.1949 10.88C76.3069 10.88 76.3909 10.88 76.4469 10.88C76.5962 10.88 76.6895 10.8473 76.7269 10.782C76.7735 10.7167 76.8109 10.5627 76.8389 10.32C76.9695 10.4133 77.1469 10.502 77.3709 10.586C77.6042 10.6607 77.8189 10.7167 78.0149 10.754C77.9402 11.2393 77.7955 11.566 77.5809 11.734C77.3755 11.902 77.0582 11.986 76.6289 11.986C76.5542 11.986 76.4469 11.986 76.3069 11.986C76.1669 11.986 76.0175 11.986 75.8589 11.986C75.7095 11.986 75.5649 11.986 75.4249 11.986C75.2849 11.986 75.1822 11.986 75.1169 11.986C74.6969 11.986 74.3702 11.9487 74.1369 11.874C73.9035 11.79 73.7402 11.6407 73.6469 11.426C73.5535 11.2113 73.5069 10.9173 73.5069 10.544V9.172ZM73.7729 16.83L75.0049 16.13C75.3409 16.2793 75.6909 16.452 76.0549 16.648C76.4189 16.8347 76.7642 17.026 77.0909 17.222C77.4175 17.4087 77.6975 17.5813 77.9309 17.74L76.2089 18.37C75.9475 18.146 75.5882 17.894 75.1309 17.614C74.6829 17.3247 74.2302 17.0633 73.7729 16.83ZM71.3089 16.144L72.7789 16.648C72.4242 16.984 71.9809 17.306 71.4489 17.614C70.9169 17.922 70.4082 18.174 69.9229 18.37C69.8295 18.2673 69.7082 18.1553 69.5589 18.034C69.4189 17.9033 69.2695 17.7773 69.1109 17.656C68.9522 17.5347 68.8169 17.432 68.7049 17.348C69.1902 17.1987 69.6709 17.0213 70.1469 16.816C70.6229 16.6013 71.0102 16.3773 71.3089 16.144ZM84.8189 7.884H86.4989V12.658C86.4989 13.2367 86.4709 13.8667 86.4149 14.548C86.3682 15.22 86.2655 15.9013 86.1069 16.592C85.9575 17.2827 85.7335 17.9453 85.4349 18.58C85.1455 19.2053 84.7582 19.7653 84.2729 20.26C84.1889 20.148 84.0675 20.0127 83.9089 19.854C83.7502 19.7047 83.5869 19.56 83.4189 19.42C83.2509 19.2893 83.1015 19.1867 82.9709 19.112C83.4002 18.6733 83.7362 18.188 83.9789 17.656C84.2309 17.1147 84.4175 16.5593 84.5389 15.99C84.6602 15.4113 84.7349 14.8373 84.7629 14.268C84.8002 13.6987 84.8189 13.162 84.8189 12.658V7.884ZM89.1309 13.414C89.2802 14.2167 89.4902 14.9727 89.7609 15.682C90.0315 16.382 90.3815 17.0027 90.8109 17.544C91.2402 18.076 91.7629 18.5007 92.3789 18.818C92.2482 18.93 92.1082 19.07 91.9589 19.238C91.8095 19.4153 91.6695 19.5973 91.5389 19.784C91.4082 19.9707 91.3009 20.1387 91.2169 20.288C90.5075 19.8867 89.9102 19.364 89.4249 18.72C88.9489 18.076 88.5569 17.3293 88.2489 16.48C87.9502 15.6213 87.7075 14.6833 87.5209 13.666L89.1309 13.414ZM85.4349 7.884H91.6789V13.946H85.4349V12.378H89.9849V9.466H85.4349V7.884ZM79.1069 9.802H84.2589V11.356H79.1069V9.802ZM80.9549 7.1H82.5649V18.426C82.5649 18.846 82.5182 19.168 82.4249 19.392C82.3315 19.616 82.1635 19.798 81.9209 19.938C81.6689 20.0593 81.3609 20.1387 80.9969 20.176C80.6329 20.2133 80.1802 20.232 79.6389 20.232C79.6202 20.0173 79.5595 19.7607 79.4569 19.462C79.3635 19.1633 79.2655 18.9067 79.1629 18.692C79.4802 18.7013 79.7835 18.7107 80.0729 18.72C80.3715 18.72 80.5722 18.72 80.6749 18.72C80.7775 18.7107 80.8475 18.6873 80.8849 18.65C80.9315 18.6033 80.9549 18.524 80.9549 18.412V7.1ZM78.8829 14.198C79.3495 14.1233 79.8769 14.0347 80.4649 13.932C81.0529 13.82 81.6689 13.6987 82.3129 13.568C82.9569 13.4373 83.5962 13.3113 84.2309 13.19L84.3849 14.716C83.5075 14.912 82.6162 15.108 81.7109 15.304C80.8055 15.4907 79.9842 15.6587 79.2469 15.808L78.8829 14.198Z"
          fill="#4457D1"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_65893_114112"
          x="0.546875"
          y="0"
          width="102"
          height="29"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_65893_114112"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_65893_114112"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
}

function MarkerComponent({
  fill,
  size,
  children,
  isFav,
}: { fill: string; size: number; children: React.ReactNode; isFav?: boolean }) {
  return (
    <svg
      width={size * 0.7}
      height={size}
      viewBox="0 0 44 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M36.3155 36.7114C30.8617 43.3287 26.4463 51.2303 23.6793 59.4065C23.0754 61.1831 21.0725 61.7259 20.3947 59.7221C17.372 50.7822 12.6854 42.5871 6.33182 35.1399C2.04271 30.1098 1.01665 22.3534 2.90854 16.2662C8.01418 -0.158765 29.2101 -3.08717 38.6511 11.0784C42.6906 17.1434 43.1806 25.6604 39.5478 32.3093C39.027 33.2655 37.9486 34.7328 36.3186 36.7114H36.3155Z"
        fill={fill}
      />
      <ellipse cx="21.9999" cy="22.1467" rx="15.7143" ry="15.8293" fill="white" />
      {isFav && (
        <path
          d="M21.9999 39.415L23.7129 42.7962L27.4345 43.3924L24.7716 46.0783L25.3587 49.8279L21.9999 48.1067L18.6412 49.8279L19.2283 46.0783L16.5653 43.3924L20.287 42.7962L21.9999 39.415Z"
          fill="white"
        />
      )}
      {children}
    </svg>
  );
}

export function SelfMarker() {
  return (
    <div className="w-[24px] h-[24px] flex items-center justify-center">
      <motion.div
        className="absolute w-[24px] h-[24px] rounded-full bg-[#6393F2]"
        animate={{
          scale: [0.8, 1, 0.8],
          opacity: [0.3, 0.1, 0.3],
        }}
        transition={{
          duration: 2,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
          delay: 0.3,
        }}
      />
      <motion.div
        className="w-[14px] h-[14px] rounded-full bg-[#6393F2] border-2 border-white"
        animate={{
          scale: [1.0, 0.9, 1.0],
        }}
        transition={{
          duration: 4,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
          delay: 0.3,
        }}
      />
    </div>
  );
}
