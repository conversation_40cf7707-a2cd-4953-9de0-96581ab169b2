import HealthRecordPage from '@/app/(webview)/health-record/_create/page';
import { Button } from '@/components/shared/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/shared/drawer';
import { ScrollArea, ScrollBar } from '@/components/shared/scroll-area';
import { APP_TEXT } from '@/const/text/app';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useVirtualKeyboardEvent } from '@/hooks/use-virtual-keyboard-event';
import { formatDate } from '@/utils/date-format';
import { Plus, X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface HealthRecordCreateButtonProps {
  date?: string;
  onDataUpdated?: () => void;
}

export default function HealthRecordCreateButton({
  date,
  onDataUpdated,
}: HealthRecordCreateButtonProps) {
  const currentDate = formatDate(new Date(), 'yyyy-MM-dd');
  const [open, setOpen] = useState(false);
  const [bottom, setBottom] = useState<number>(0);
  const [scrollAreaHeight, setScrollAreaHeight] = useState<string>('');
  const safeArea = useSafeArea();
  const virtualKeyboardInfo = useVirtualKeyboardEvent();

  const handleFormSubmit = (dataUpdateState: boolean) => {
    if (dataUpdateState) {
      onDataUpdated?.();
    }
    setOpen(false);
  };

  useEffect(() => {
    const menuHeight = 61;
    const menuMargin = 16;
    const bottom = safeArea.bottom + menuHeight + menuMargin;
    setBottom(bottom);
  }, [safeArea]);

  useEffect(() => {
    const pagePadding = 48;
    const pageHeaderHeight = 56;
    const scrollAreaTop = pagePadding + pageHeaderHeight + safeArea.top;
    setScrollAreaHeight(`${virtualKeyboardInfo.currentHeight - scrollAreaTop}px`);
  }, [safeArea, virtualKeyboardInfo]);

  return (
    <>
      <Button
        type="button"
        onClick={() => setOpen(true)}
        className="w-[164px] fixed right-4"
        style={{ bottom }}
      >
        <Plus />
        {APP_TEXT.HEALTH_RECORD_PAGE.ADD_RECORD}
      </Button>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent className="height-auto-important">
          <DrawerHeader className="relative">
            <DrawerTitle>{APP_TEXT.HEALTH_RECORD_PAGE.ADD_RECORD}</DrawerTitle>
            <DrawerDescription className="sr-only">HealthRecordCreateButton</DrawerDescription>
            <DrawerClose asChild>
              <button type="button" className="absolute right-5 top-3">
                <X size={24} />
              </button>
            </DrawerClose>
          </DrawerHeader>
          <ScrollArea style={{ height: scrollAreaHeight }} type="hover">
            <ScrollBar orientation="vertical" />
            <HealthRecordPage date={date || currentDate} onFormSubmit={handleFormSubmit} />
            <div style={{ height: safeArea.bottom }} />
            {/* <VirtualKeyboardInfo virtualKeyboardInfo={virtualKeyboardInfo} /> */}
          </ScrollArea>
        </DrawerContent>
      </Drawer>
    </>
  );
}
