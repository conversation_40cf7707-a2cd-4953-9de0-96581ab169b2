import { usePostStore } from '@/store/post';
import * as RadioGroup from '@radix-ui/react-radio-group';
import { useState } from 'react';

export default function LayoutSwitcher({
  className,
}: {
  className?: string;
}) {
  const { layout, setLayout } = usePostStore();

  return (
    <RadioGroup.Root
      className="flex bg-white h-12 rounded-[8px]"
      value={layout}
      onValueChange={setLayout}
    >
      <RadioGroup.Item
        value="overview"
        className={`
          p-2 rounded-[8px]
          ${layout === 'overview' ? 'bg-primary' : ''}
        `}
      >
        <span className="w-7 h-7">
          <img
            className={`text-gray-800 ${layout === 'overview' ? 'inline-block' : 'hidden'}`}
            src="/images/post/icon-post-overview-w.png"
            alt=""
          />
          <img
            className={`text-gray-800 ${layout === 'overview' ? 'hidden' : 'inline-block'}`}
            src="/images/post/icon-post-overview.png"
            alt=""
          />
        </span>
      </RadioGroup.Item>
      <RadioGroup.Item
        value="grid"
        className={`
          p-2 rounded-[8px]
          ${layout === 'grid' ? 'bg-primary' : ''}
        `}
      >
        <span className="w-7 h-7">
          <img
            className={`text-gray-800 ${layout === 'grid' ? 'inline-block' : 'hidden'}`}
            src="/images/post/icon-post-grid-w.png"
            alt=""
          />
          <img
            className={`text-gray-800 ${layout === 'grid' ? 'hidden' : 'inline-block'}`}
            src="/images/post/icon-post-grid.png"
            alt=""
          />
        </span>
      </RadioGroup.Item>
    </RadioGroup.Root>
  );
}
