'use client';

import RouterLink from '@/components/shared/router-link';
import SectionTitle from '@/components/shared/section-title';
import { cn } from '@/lib/utils';
import { ChevronRight } from 'lucide-react';

interface MenuItem {
  label: string;
  href: string;
  onClick?: () => void;
  subLabel?: React.ReactNode;
}

interface SettingsMenuPanelProps {
  title?: string;
  desc?: string;
  className?: string;
  menuItems: MenuItem[];
}

export default function MenuPanel3({ title, desc, menuItems, className }: SettingsMenuPanelProps) {
  return (
    <>
      {title && <SectionTitle>{title}</SectionTitle>}
      {desc && <div className="ml-4 text-gray-500">{desc}</div>}
      <div className={cn('bg-card mx-6 rounded-xl px-3', className)}>
        {menuItems.map((item, index) => (
          <RouterLink
            key={index}
            href={item.href}
            onClick={item.onClick}
            className="flex items-center border-b border-text-muted last:border-b-0"
          >
            <div className="flex-1 items-center py-4">
              <div className="text-base text-left text-gray-500">{item.label}</div>
              {item.subLabel}
            </div>
            <ChevronRight className="w-8 h-8" />
          </RouterLink>
        ))}
      </div>
    </>
  );
}
