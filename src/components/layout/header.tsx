import { gamificationAPI } from '@/api/modules/my-page';
import { getLevelIconPath, levelUpIconsNew } from '@/const/gamification';
import { useLevelUp } from '@/hooks/use-level-up';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { OrganizerInfoBean } from '@/types/home-data';
import { useQuery } from '@tanstack/react-query';
import { IconButtonWithBadge } from '../ui/icon-button';
interface HomeHeaderProps {
  organInfo: OrganizerInfoBean;
  onRightClick?: () => void;
}
const HomeHeader: React.FC<HomeHeaderProps> = ({ organInfo, onRightClick }) => {
  const safeArea = useSafeArea();
  const safeTop = safeArea.top ?? 0;
  const router = useRouter();
  const { checkLevelUp, currentLevel } = useLevelUp();

  const handleAvatarClick = () => {
    checkLevelUp('home', (msg) => {
      if (msg !== 'levelUp') {
        router.push('/my-page');
      }
    });
  };

  return (
    <>
      <div className="mt-[48px]" style={{ height: safeTop }} />
      <div
        style={{ paddingTop: safeTop }}
        className="fixed top-0 left-0 right-0 z-50 bg-white rounded-b-[16px]"
      >
        <div className="bg-white shadow px-4 h-[72px] flex justify-between items-center rounded-b-[16px]">
          {/* <IconButtonWithBadge
            icon={'/images/header/heart.svg'}
            text={''}
            badgeCount={0}
            onClick={onRightClick}
            visible={false}
          /> */}
          {currentLevel && (
            <UserAvatar
              level={String(currentLevel ?? '*')}
              newIcon={getLevelIconPath(currentLevel)}
              onClick={() => router.push('/my-page')}
            />
          )}
          {organInfo.iconUrl ? (
            <img src={organInfo.iconUrl} alt="" className="mr-2 h-[48px] w-auto" />
          ) : (
            <span className="text-black	text-base font-bold mr-2">
              {organInfo.organizerName || '-'}
            </span>
          )}
          <IconButtonWithBadge
            icon={'/images/header/notice.svg'}
            text={''}
            badgeCount={100}
            onClick={handleAvatarClick}
            visible={false}
          />
        </div>
      </div>
    </>
  );
};

export default HomeHeader;

interface UserAvatarProps {
  level: string;
  newIcon?: string;
  onClick?: () => void;
}

export function UserAvatar({ level, newIcon, onClick }: UserAvatarProps) {
  return (
    <button
      type="button"
      className="relative cursor-pointer bg-transparent border-none p-0"
      onClick={onClick}
    >
      <div className="relative z-10 flex flex-col items-center justify-center">
        {/* ノード(アイコン) */}
        <div className="relative w-9 h-9">
          <div className="w-9 h-9 rounded-full flex items-center justify-center overflow-hidden bg-primary-30">
            <img src={newIcon} alt="icon" className="w-full h-full" />
          </div>
        </div>

        {/* レベルバッジ */}
        <div className="relative -mt-1 h-[18px] w-12 rounded-[4px] text-xs font-bold border flex items-center justify-center bg-card text-primary border-2 border-primary-90">
          Lv.{level}
        </div>
      </div>
    </button>
  );
}
