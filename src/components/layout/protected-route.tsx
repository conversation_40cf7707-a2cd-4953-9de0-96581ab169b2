'use client';

import Loading from '@/components/layout/loading-page';
import { useGlobalInit } from '@/hooks/use-global-init';
import { useRoutes } from '@/hooks/use-routes';
import { useAuthStore } from '@/store/auth';
import { useDevModeStore } from '@/store/dev-mode';
import { nlog } from '@/utils/logger';
import { useEffect } from 'react';
export default function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { goLogin } = useRoutes();
  const { log } = useDevModeStore();
  const { isAuthenticated, isRehydrated } = useAuthStore();

  useEffect(() => {
    nlog(`zzz01${isRehydrated} ${isAuthenticated}`);
    if (isRehydrated && !isAuthenticated) {
      nlog('zzz02 goLogin');
      goLogin();
    }
    log(`ProtectedRoute: isAuthenticated: ${isAuthenticated}, isRehydrated: ${isRehydrated}`);
  }, [isAuthenticated, isRehydrated]);

  if (!isRehydrated || !isAuthenticated) {
    nlog('zzz03 Loading');
    return <Loading />;
  }

  return <>{children}</>;
}
