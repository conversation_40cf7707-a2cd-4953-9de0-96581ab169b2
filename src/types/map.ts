export interface Position {
  x: number;
  y: number;
}

// Google Mapsの座標データ型
export interface LatLng {
  lat: number;
  lng: number;
}

// 線（接続）の型定義
export interface Connection {
  id: string; // courseId or other page id
  path: LatLng[];
}

export enum MarkerType {
  WALKING_COURSE = 'walking-course',
  EVENT = 'event',
  COUPON = 'coupon',
  PHOTO = 'photo',
  USER_LOCATION = 'user-location',
  PICK = 'pick',
  POST = 'post',
}

export type Marker = {
  id: string;
  name: string;
  type: MarkerType;
  address?: string;
  latitude: string;
  longitude: string;
  imagePath?: string;
  isFav?: boolean;
  isCurrent?: boolean;
};

export interface MapBounds {
  center: LatLng;
  zoom: number;
}
