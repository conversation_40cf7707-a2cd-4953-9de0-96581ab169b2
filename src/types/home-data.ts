import type { MissionCardType } from './mission';

interface slidesArea {
  city: string;
  count: number;
  isPadding: boolean;
  logo: string;
}

interface slidesActivity {
  id: number;
  url: string;
  imgUrl: string;
}
export interface OrganizerInfoListResponse {
  organSettingInfo?: OrganizerInfoBean[];
}

export interface OrganizerInfoBean {
  organizerId: number;
  color?: number;
  organizerName?: string;
  iconUrl?: string;
  characterUrl?: string;
  pointCount?: number;
  futurePoint?: number;
  availablePoint?: number;
  diffPoint?: number;
  pointAuthority?: number;
}
export interface getStepInfoResponse {
  data?: StepInfoResponse;
}
export interface setPresetOrganResponse {
  data?: object;
}
export interface StepInfoResponse {
  step: number;
  stepTarget: number;
  targetAchieveDays?: string;
  distance?: string;
  exerciseTime?: string;
  energy?: string;
}
export type { slidesActivity, slidesArea };
export interface MissionResponse {
  achievedMissionExistFlg: boolean;
  missionList: MissionCardType[];
}
export interface StepPointInfo {
  totalPoint?: string | null;
  details?: PointInfoDetails[] | null;
}

export interface PointInfoDetails {
  organizerId: string;
  organizerName: string;
  point: number;
}

/**
 * event response
 */
export interface HomeEventResponse {
  eventList?: HomeEventList[];
}

export interface HomeEventList {
  eventEndDate?: string;
  eventId?: string;
  eventImageFilePath?: string;
  eventName?: string;
  eventStartDate?: string;
  isFavorite?: boolean;
  venueName?: string;
}
export interface PopupInfo {
  totalPoint?: number;
  details?: PopupInfoDetails[];
}
export interface PopupInfoDetails {
  organizerId?: string;
  organizerName?: string;
  point?: number | string;
  firstPoint?: number;
  firstPointName?: string;
  dailyPoint?: number;
  dailyPointName?: string;
  continuousPoint?: number;
  continuousPointName?: string;
  continuousNum?: number;
}
export interface GetLoginBonusPointResponse {
  isPopupShow?: boolean;
  pointInfo?: PopupInfo;
}
export interface GetPhotosClaimResponse {
  isPopupShow?: boolean;
  pointList?: PointList[];
}
export interface PointList {
  organizerNm?: string;
  point?: number;
}
export interface MenuButton {
  id: number;
  label: string;
  url?: string;
  iconKey: IconKey;
  callback?: () => void;
}

export type IconKey =
  | 'graph'
  | 'score'
  | 'mission'
  | 'ranking'
  | 'walking'
  | 'event'
  | 'coupon'
  | 'userSetting'
  | 'healthCheck'
  | 'result'
  | 'record';

export interface OptionUseStatus {
  fcnId: number;
  useSts: number;
}

export interface OrganizerSetting {
  icon: string;
  character: string;
}
export interface Group {
  groupId: number;
  organizerId: number;
  groupCd: string;
  groupKind: number;
  groupNm: string;
  groupKana: string;
  webPref: string;
  webAddress1: string;
  rankingFlg: number;
  lotteryJoinFlg: number;
  pointExchangeFlg: number;
  isPublic: number;
  groupType: number;
}

export interface Organizer {
  organizerId: number;
  organizerCode: string;
  organizerName: string;
  zipCd: string;
  address: string;
  loginUrl: string;
  registerUrl: string;
  organizerSetting: OrganizerSetting;
  groupList: Group;
}

export interface OrganizerInfoResponse {
  optionUseStatus: OptionUseStatus[];
  organizerList: Organizer[];
}

export interface OrganizerInfoRequest {
  organizerCode: string[];
}

export interface HomePostItem {
  userId: number;
  postId: number;
  likeCount: number;
  thumbnailImage: string;
}
export interface HomePostResponse {
  posts: HomePostItem[];
}
