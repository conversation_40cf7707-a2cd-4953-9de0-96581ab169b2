export interface PostListRequest {
  queryUserId?: number;
  categoryCd?: number[];
  userType?: number[];
  distFromCurrloc?: number;
  currentPositionLon?: string;
  currentPositionLat?: string;
  sortKind: number;
  pagination?: Pagination;
}
export type PostClassifyListRequest = Omit<PostListRequest, 'sortKind' | 'userType'> & {
  pagination: Pagination;
};
export interface IPostStatistics {
  postCount: number;
  inReview: number;
  reject: number;
}
export interface PostClassifyListResponse {
  userId: number;
  nickName: string;
  isFriend: number;
  avatar: string;
  isOneself: number;
  posts: PostInterface[];
  pagination: Required<Pagination>;
  postStatistics: IPostStatistics;
}

export interface PostListResponse {
  posts: PostInterface[];
  pagination: Required<Pagination>;
  postStatistics: PostStatistics;
}

export interface PostInterface {
  userId: number;
  postId: number;
  isLike: number;
  nickName: string;
  avatar: string;
  isFriend: number;
  categoryCd: number;
  isOneself: number;
  images: Images[];
  likeCount: number;
  viewCount: number;
  postComment: string;
  postStatus: number;
  latitude: string;
  longitude: string;
  createdAt: string;
  onMap: number;
}

export interface Images {
  photoId: number;
  photoOrder: number;
  image: string;
  thumbnailImage: string;
}

export interface Pagination {
  total?: number;
  page?: number;
  limit?: number;
  pages?: number;
}

export interface PostStatistics {
  postCount: number;
}

export interface likeUsers {
  userId: number;
  nickName: string;
  avatar: string;
  likeAt: string;
  postId: number;
  thumbnailImage: string;
}
export interface GetReceivedListResponse {
  likeUsers: likeUsers[];
  pagination: Required<Pagination>;
}
export interface FilterPostListRequest {
  categoryCd: number[];
  userType: number[];
  distFromCurrloc: number;
  currentPositionLon: string;
  currentPositionLat: string;
  sortKind: number;
  pagination: Pagination;
}

export interface PostDetailRequest {
  queryUserId: number;
  postId: number;
}

export interface GivePost {
  userId: number;
  postId: number;
  thumbnailImage: string;
  likeCount: number;
}
export interface GetGivesLikesResponse {
  posts: GivePost[];
  pagination: Required<Pagination>;
}

export interface likeOrReviewRequest {
  postId: number;
  likeOrReviewUser: number;
  updKind: number;
}

export interface PostFilters {
  selectedCategoryValues: string[];
  selectedUserValues: string[];
  selectedDistanceValue: string;
}

export interface ConfirmPost {
  id: number;
  url: string;
}
