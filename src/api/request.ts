// utils/request.ts
import type {
  AxiosError,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
  AxiosRequestConfig as OriginalAxiosRequestConfig,
} from 'axios';

import { RESPONSE_CODE } from '@/const/response-code';
import { useAuthStore } from '@/store/auth';
import { useDevModeStore } from '@/store/dev-mode';
import { isAndroid } from '@/utils/device-detect';
import axios from 'axios';
import toast from 'react-hot-toast';

// AxiosRequestConfig 型を拡張し、リトライ設定を追加
export interface AxiosRequestConfig extends OriginalAxiosRequestConfig {
  retry?: boolean;
  retryDelay?: number;
  retryCount?: number;
  _retryAttempt?: number; // 内部使用、現在のリトライ回数を記録
  showErrorToast?: boolean;
}

// API レスポンスデータ構造を定義
// biome-ignore lint: api response type is any
export interface ApiResponse<T = any> {
  code: string;
  data: T;
  message: string;
}

export interface ErrorResponse {
  code: string;
  message: string;
  error?: Error | AxiosError;
}

// 成功的业务响应码
const SUCCESS_CODE = '0000'; //  "0000" Success Code

// デフォルトのリトライ設定
const DEFAULT_RETRY_COUNT = 2;
const DEFAULT_RETRY_DELAY = 1000; // 1秒

// axios インスタンスを作成
const request: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 15000,
});

let phone = '2'; // デフォルトは iOS

if (typeof window !== 'undefined') {
  phone = isAndroid() ? '1' : '2';
}

// リクエストインターセプター
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // トークンを追加
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        // Axios v1.x に対応するために set メソッドを使用
        config.headers.set('Authorization', `Bearer ${token}`);
      }
    }
    const userAgent = navigator.userAgent;
    // "Content-Type": "application/json",
    // "Accept": "*/*",
    // "Use-Organizer-ID": "805",
    // "User-ID": "\(userID)",
    // "Organizer-ID": "805,802",
    // "Phone-OS": phone
    const { user, appData, token } = useAuthStore.getState();
    config.headers.set('Content-Type', 'application/json');
    config.headers.set('appkey', '7de46d24d36154044eebeb432805510e');
    config.headers.set('Accept', '*/*');
    config.headers.set('Use-Organizer-ID', user?.useOrganizerID || '');
    config.headers.set('User-ID', user?.id || '');
    config.headers.set('Organizer-ID', user?.organizerID || ''); //
    config.headers.set('Phone-OS', phone);
    config.headers.set('App-Version', appData?.appVersion ?? '1.0');
    config.headers.set('User-Agent', userAgent);

    return config;
  },
  (error) => {
    return Promise.reject({
      code: RESPONSE_CODE.UNKNOWN_ERROR,
      message: '未知のエラーが発生しました',
      error,
    });
  },
);
/**
 * showToastパラメータに基づいてエラートーストを表示するかどうかを決定します
 * @param showToast - トースト表示の有無を制御するフラグ
 * @param message - 表示するエラーメッセージ
 */
const isShowToast = (showToast: boolean, message: string) => {
  if (showToast !== false) {
    toast.error(message);
  }
};
// レスポンスインターセプター
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // バックエンドAPIの仕様に基づいてレスポンスデータを処理
    const res = response.data;
    const { log } = useDevModeStore.getState();
    const { user, appData, token } = useAuthStore.getState();
    log(
      `api${res.code.endsWith(SUCCESS_CODE) ? 'ok' : 'err'} userid:${user?.id} url:${response.config.baseURL}${response.config.url} header:${JSON.stringify(response.config.headers)} 
      request:${JSON.stringify(response.config.data)} params:${JSON.stringify(response.config.params)}
       response:${JSON.stringify(res.data)}`,
    );
    // 业务成功码判断，假设成功码以 "0000" 结尾
    if (!res.code.endsWith(SUCCESS_CODE)) {
      toast.error(res.message || 'リクエストが失敗しました');
      return Promise.reject(response);
    }

    // 成功の場合はdata部分だけを返す
    return res.data;
  },
  (error: Error | AxiosError) => {
    const { log } = useDevModeStore.getState();
    let errorResponse: ErrorResponse = {
      code: RESPONSE_CODE.UNKNOWN_ERROR,
      message: '未知のエラーが発生しました',
      error,
    };
    log(`apierr ${JSON.stringify(error)}`);
    if (!axios.isAxiosError(error)) {
      return Promise.reject(errorResponse);
    }
    // リクエスト設定を取得
    const config = error.config as AxiosRequestConfig;

    // リトライが必要かどうかを確認
    const shouldRetry =
      // リトライ機能が有効
      config.retry !== false &&
      // ネットワークエラーまたはサーバーエラー (5xx)
      (!error.response || (error.response.status >= 500 && error.response.status < 600));

    // リトライカウントを初期化
    config._retryAttempt = config._retryAttempt || 0;

    // リトライが必要で、最大リトライ回数に達していない場合
    if (shouldRetry && config._retryAttempt < (config.retryCount || DEFAULT_RETRY_COUNT)) {
      config._retryAttempt++;

      // 遅延時間を計算（指数バックオフ戦略を実装可能）
      const delay = config.retryDelay || DEFAULT_RETRY_DELAY;

      // 遅延実行のPromiseを作成
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(request(config));
        }, delay);
      });
    }
    const shouldShowToast = config.showErrorToast !== false; // デフォルトはtrue、falseの場合は無効
    // エラー処理
    if (error.response) {
      // API からのレスポンスがある場合
      const { status } = error.response;
      const errorData = error.response.data as ApiResponse | undefined;

      // API からのエラーメッセージがある場合はそれを表示
      const errorMessage = errorData?.message || '未知のエラーが発生しました';

      // HTTP ステータスコードに基づくエラー処理
      switch (status) {
        case 401:
          // 未認証、ユーザー情報をクリア
          localStorage.removeItem('token');
          isShowToast(
            shouldShowToast,
            'セッションが期限切れになりました。再度ログインしてください。',
          );
          break;
        case 403:
          isShowToast(
            shouldShowToast,
            errorMessage || 'このリソースにアクセスする権限がありません',
          );
          break;
        case 500:
          isShowToast(shouldShowToast, errorMessage || 'サーバーエラー、管理者に連絡してください');
          break;
        default:
          isShowToast(shouldShowToast, errorMessage || 'リクエストが失敗しました');
      }
      errorResponse = {
        code: RESPONSE_CODE.INTERNAL_SERVER_ERROR,
        message: errorMessage,
        error,
      };
    } else if (error.request) {
      // リクエストは送信されたが応答がない
      isShowToast(shouldShowToast, 'ネットワーク異常、ネットワーク接続を確認してください');
      errorResponse = {
        code: RESPONSE_CODE.NETWORK_ERROR,
        message: 'ネットワーク異常、ネットワーク接続を確認してください',
        error,
      };
    } else {
      // リクエスト設定エラー
      isShowToast(shouldShowToast, error.message || 'リクエスト設定エラー');
      errorResponse = {
        code: RESPONSE_CODE.REQUEST_SETUP_ERROR,
        message: error.message || 'リクエスト設定エラー',
        error,
      };
    }

    return Promise.reject(errorResponse);
  },
);

// リクエストメソッドをエクスポート
export default request;

// デフォルトリクエスト設定
const defaultRequestConfig: AxiosRequestConfig = {
  retry: true,
  retryCount: DEFAULT_RETRY_COUNT,
  retryDelay: DEFAULT_RETRY_DELAY,
  showErrorToast: true, // デフォルトでエラーメッセージを表示する
};

// 型付きAPIメソッドをエクスポート
export const get = <T = unknown>(
  url: string,
  params?: unknown,
  config?: AxiosRequestConfig,
): Promise<T> => {
  return request.get<unknown, T>(url, {
    params,
    ...defaultRequestConfig,
    ...config,
  });
};

export const post = <T = unknown>(
  url: string,
  data?: unknown,
  config?: AxiosRequestConfig,
): Promise<T> => {
  return request.post<unknown, T>(url, data, {
    ...defaultRequestConfig,
    ...config,
  });
};

export const put = <T = unknown>(
  url: string,
  data?: unknown,
  config?: AxiosRequestConfig,
): Promise<T> => {
  return request.put<unknown, T>(url, data, {
    ...defaultRequestConfig,
    ...config,
  });
};

export const del = <T = unknown>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return request.delete<unknown, T>(url, {
    ...defaultRequestConfig,
    ...config,
  });
};

export const patch = <T = unknown>(
  url: string,
  data?: unknown,
  config?: AxiosRequestConfig,
): Promise<T> => {
  return request.patch<unknown, T>(url, data, {
    ...defaultRequestConfig,
    ...config,
  });
};
