import { ENDPOINTS } from '@/api/endpoints';
import { type ApiResponse, del, get, post, put } from '@/api/request';
import type { PaginationParams } from '@/types/api-common';
import type {
  FilterPostListRequest,
  GetGivesLikesResponse,
  GetReceivedListResponse,
  PostClassifyListRequest,
  PostClassifyListResponse,
  PostDetailRequest,
  PostInterface,
  PostListRequest,
  PostListResponse,
  likeOrReviewRequest,
} from '@/types/post';

export const postAPI = {
  // いいねをもらった一覧
  getReceivedList: (queryParams: PaginationParams) =>
    post<GetReceivedListResponse>(`${ENDPOINTS.POST.RECEIVED_LIST}`, {
      pagination: queryParams,
    }),
  posts: (query: PostListRequest) => post<PostListResponse>(ENDPOINTS.POST.LIST, query),
  getPostList: (request: FilterPostListRequest) =>
    post<PostListResponse>(ENDPOINTS.POST.LIST, request),

  getCurrentUserPost: (query: PostDetailRequest) =>
    get<PostInterface>(`${ENDPOINTS.POST.INFO}`, query),
  getGivesLikeList: (queryParams: PaginationParams) =>
    post<GetGivesLikesResponse>(`${ENDPOINTS.POST.GIVES_LIKES}`, {
      pagination: queryParams,
    }),
  likeOrReview: (query: likeOrReviewRequest) => post(ENDPOINTS.POST.LIKEORREVIEW, query),
  deletePost: (postId: number) =>
    del<ApiResponse>(`${ENDPOINTS.POST.POST}`, { params: { postId } }),
  getPostClassifyList: (request: PostClassifyListRequest) =>
    post<PostClassifyListResponse>(ENDPOINTS.POST.POST_CLASSIFY_LIST, request),
};
