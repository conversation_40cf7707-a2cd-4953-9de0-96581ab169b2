import { ENDPOINTS } from '@/api/endpoints';
import { get, post, put } from '@/api/request';

import type {
  GetLoginBonusPointResponse,
  GetPhotosClaimResponse,
  HomeEventResponse,
  HomePostResponse,
  MissionResponse,
  OrganizerInfoListResponse,
  OrganizerInfoRequest,
  OrganizerInfoResponse,
  StepInfoResponse,
  setPresetOrganResponse,
} from '@/types/home-data';
import { MissionCardType } from '@/types/mission';

export const homePageAPI = {
  getOrganizerList: (queryParams: object) =>
    get<OrganizerInfoListResponse>(`${ENDPOINTS.HOME.ORGANIZER_LIST}`, {
      ...queryParams,
    }),
  setOrganizerId: (queryParams: { defOrganizerId: number }) =>
    put<setPresetOrganResponse>(`${ENDPOINTS.HOME.SET_ORGANIZER}`, {
      ...queryParams,
    }),
  getStepInfo: (queryParams: object) =>
    get<StepInfoResponse>(`${ENDPOINTS.HOME.GET_STEP}`, {
      ...queryParams,
    }),
  //queryParams 1：ホーム　2：ヘルスチェックAI
  getHomeMission: (queryParams: object) =>
    get<MissionResponse>(`${ENDPOINTS.HOME.GET_MISSION}`, {
      ...queryParams,
    }),
  getHomeEventList: () => get<HomeEventResponse>(`${ENDPOINTS.POINT.GET_EVENT_HOME}`),
  getLoginBonusPoint: () => get<GetLoginBonusPointResponse>(`${ENDPOINTS.HOME.LOGIN_BONUS_POINT}`),
  getPhotosClaim: () => get<GetPhotosClaimResponse>(`${ENDPOINTS.HOME.PHOTOS_CLAIM}`),
  getOrganizerInfo: (request: OrganizerInfoRequest) =>
    post<OrganizerInfoResponse>(`${ENDPOINTS.HOME.ORGANIZER_INFO}`, request),
  // ホームページ写真投稿取得API
  getHomePostList: () => get<HomePostResponse>(`${ENDPOINTS.HOME.HOME_POST}`),
};
