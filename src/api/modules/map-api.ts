import type { FavoriteRequest } from '@/types/map-types';
import { ENDPOINTS } from '../endpoints';
import { del, post } from '../request';

const BASE_URL = ENDPOINTS.MAP.base;

export const mapAPI = {
  // お気に入り登録API
  toggleFavorite: (favoriteRequest: FavoriteRequest) =>
    post(`${BASE_URL}/favorite`, favoriteRequest),
  // お気に入り解除API
  //   removeFromFavorite: (favoriteRequest: FavoriteRequest) =>
  //     del(`${BASE_URL}/favorite`, favoriteRequest),
};
