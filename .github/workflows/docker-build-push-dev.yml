name: New Dev Build and Push Docker Image

on:
  push:
    tags:
      - 'dev*'  # trigger on pushes to dev tag ps:dev* can match dev1.0.0, dev1.0.1, dev1.0.2, etc.

env:
  REGISTRY_NAME: ${{ secrets.DEV_REGISTRY }}
  IMAGE_NAME: web-app
  REPOSITORY_NAME: mileage-dev
  DOCKERFILE_PATH: "./Dockerfile"  # Dockerfile path demo: ./Dockerfile dir
  BUILD_CONTEXT: "./"  # build context app dir
  NEXT_PUBLIC_APP_ENV: 'dev'
  NEXT_PUBLIC_API_URL: 'https://app-api.kmileage-dev.sdpf4hc-ntt.com/api/v1/app'
  NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY_DEV || 'dev_maps_key' }}
  NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID: ${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID_DEV || 'dev_maps_mapid' }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Extract Commit Hash
        id: extract-hash
        run: |
          SHORT_HASH=${GITHUB_SHA::7}
          echo "DOCKER_TAG=$SHORT_HASH" >> $GITHUB_OUTPUT
          echo "Extracted commit hash: $SHORT_HASH"

      - name: Login to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.DEV_REGISTRY }}
          username: ${{ secrets.ACR_DEV_USERNAME }}
          password: ${{ secrets.ACR_DEV_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ${{ env.BUILD_CONTEXT }}  # config build context
          file: ${{ env.DOCKERFILE_PATH }}    # finger out Dockerfile path
          push: true
          build-args: |
            NEXT_PUBLIC_APP_ENV=${{ env.NEXT_PUBLIC_APP_ENV }}
            NEXT_PUBLIC_API_URL=${{ env.NEXT_PUBLIC_API_URL }}
            NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${{ env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}
            NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID=${{ env.NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID }}
          tags: |
            ${{ secrets.DEV_REGISTRY }}/${{ env.REPOSITORY_NAME }}/${{ env.IMAGE_NAME }}:${{ steps.extract-hash.outputs.DOCKER_TAG }}
            ${{ secrets.DEV_REGISTRY }}/${{ env.REPOSITORY_NAME }}/${{ env.IMAGE_NAME }}:latest
          labels: |
            org.opencontainers.image.source=${{ github.repositoryUrl }}
            org.opencontainers.image.version=${{ steps.extract-hash.outputs.DOCKER_TAG }}
      
      - name: Checkout INFRA repository
        uses: actions/checkout@v4
        with:
          repository: BBX-Unified-Repo/shKENKOMILEAGE_INFRA
          token: ${{ secrets.INFRA_GITHUB_TOKEN }}  # need token for INFRA repo commit
          path: infra-repo
          ref: develop 

      - name: Update values.yaml
        run: |
          # enter INFRA dir
          cd infra-repo
          
          # modify values.yaml tag value
          sed -i "s/tag: \".*\"/tag: \"${{ steps.extract-hash.outputs.DOCKER_TAG }}\"/g" environments/develop/values/web-app-values.yaml
          
          # check 
          git diff
          
          # config git account info
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Action"

      - name: Commit and push changes
        run: |
          cd infra-repo
          
          # check change file
          if git diff --quiet; then
            echo "No changes to commit"
          else
            git add environments/develop/values/web-app-values.yaml
            git commit -m "Update web-app image tag to ${{ steps.extract-hash.outputs.DOCKER_TAG }}"
            git push origin HEAD:develop  # push to target branch 
          fi